require 'active_support/core_ext/integer/time'

Rails.application.configure do
  # Settings specified here will take precedence over those in config/application.rb.

  # Code is not reloaded between requests.
  config.cache_classes = true

  # Eager load code on boot. This eager loads most of Rails and
  # your application in memory, allowing both threaded web servers
  # and those relying on copy on write to perform better.
  # Rake tasks automatically ignore this option for performance.
  config.eager_load = true

  # Full error reports are disabled and caching is turned on.
  config.consider_all_requests_local       = false

  config.action_controller.perform_caching = true
  if Settings.memcached_enabled
    dalli_opts = Settings.memcached.to_h
    dalli_opts[:pool_size]        ||= Integer(ENV['DB_POOL'] || ENV['RAILS_MAX_THREADS'] || 5)
    dalli_opts[:expires_in]       ||= 30.days
    dalli_opts[:value_max_bytes]  ||= 10_485_760 # 10MB
    dalli_opts[:username]           = ENV['MEMCACHED_USERNAME'] if <PERSON>NV['MEMCACHED_USERNAME']
    dalli_opts[:password]           = ENV['MEMCACHED_PASSWORD'] if <PERSON>N<PERSON>['MEMCACHED_PASSWORD']
    config.cache_store = :mem_cache_store, *dalli_opts.delete(:hosts), dalli_opts
  end
  # redis_store ma zrabana konfiguracje poolingu jako cache_store, nie nadaje sie

  # Enable Rack::Cache to put a simple HTTP cache in front of your application
  # Add `rack-cache` to your Gemfile before enabling this.
  # For large-scale production use, consider using a caching reverse proxy like nginx, varnish or squid.
  # config.action_dispatch.rack_cache = true

  # Disable serving static files from the `/public` folder by default since
  # Apache or NGINX already handles this.
  config.public_file_server.enabled = true

  # IE <10 - read: http://www2.bindle.me/blog/index.php/200/splitting-the-asset-destroying-arcane-ie-bugs-on-the-rails-rack

  # http://stackoverflow.com/a/15972973
  # https://www.fastly.com/blog/accelerating-rails-part-1-built-in-caching
  # s-maxage - The length of time for the content to be cached in proxy caches, i.e. CDNs or memcached.
  config.public_file_server.headers = {
    'Cache-Control' => 'no-cache'
  }

  # https://robots.thoughtbot.com/content-compression-with-rack-deflater
  # https://github.com/rack/rack/blob/master/lib/rack/deflater.rb
  # kompresuje html i json i dodaje naglowek Vary: Accept-Encoding
  # curl -x '' -w -s --compressed -H "Accept-Encoding: gzip,deflate" http://localhost:3000/api/users.json
  # config.middleware.use Rack::Deflater
  config.middleware.insert_before ActionDispatch::Static, Rack::Deflater

  # Specifies the header that your server uses for sending files.
  # config.action_dispatch.x_sendfile_header = 'X-Sendfile' # for Apache
  config.action_dispatch.x_sendfile_header = 'X-Accel-Redirect' # for NGINX

  # Force all access to the app over SSL, use Strict-Transport-Security, and use secure cookies.
  # config.force_ssl = true

  # Use the lowest log level to ensure availability of diagnostic information
  # when problems arise.
  config.log_level = :info

  # Prepend all log lines with the following tags.
  config.log_tags = [:request_id]

  # Use a different cache store in production.
  # config.cache_store = :mem_cache_store

  # Enable serving of images, stylesheets, and JavaScripts from an asset server.
  # config.action_controller.asset_host = 'http://assets.example.com'

  # Ignore bad email addresses and do not raise email delivery errors.
  # Set this to true and configure the email server for immediate delivery to raise delivery errors.
  config.action_mailer.raise_delivery_errors = true

  config.action_mailer.perform_caching = false

  # Enable locale fallbacks for I18n (makes lookups for any locale fall back to
  # the I18n.default_locale when a translation cannot be found).
  config.i18n.fallbacks = true

  # Don't log any deprecations.
  config.active_support.report_deprecations = false

  # Use default logging formatter so that PID and timestamp are not suppressed.
  config.log_formatter = Logger::Formatter.new

  # Use a different logger for distributed setups.
  # require "syslog/logger"
  # config.logger = ActiveSupport::TaggedLogging.new(Syslog::Logger.new 'app-name')

  if ENV['RAILS_LOG_TO_STDOUT'].present?
    logger           = ActiveSupport::Logger.new($stdout)
    logger.formatter = config.log_formatter
    config.logger    = ActiveSupport::TaggedLogging.new(logger)
  end

  # Do not dump schema after migrations.
  config.active_record.dump_schema_after_migration = false
end

unless `hostname` =~ /dev/
  warning = <<'HEREDOC'
  ______              _            _   _
  | ___ \            | |          | | (_)
  | |_/ / __ ___   __| |_   _  ___| |_ _  ___  _ __
  |  __/ '__/ _ \ / _` | | | |/ __| __| |/ _ \| '_ \
  | |  | | | (_) | (_| | |_| | (__| |_| | (_) | | | |
  \_|  |_|  \___/ \__,_|\__,_|\___|\__|_|\___/|_| |_|

HEREDOC

  puts warning
  puts 'WARNING: YOU ARE RUNNING PRODUCTION. PROCEED WITH CAUTION.'
end
