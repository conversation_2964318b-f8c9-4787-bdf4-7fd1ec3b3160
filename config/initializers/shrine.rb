require 'shrine'
require 'shrine/storage/file_system'

if Rails.env.test?
  cache_dir = 'uploads/test/cache'
  store_dir = 'uploads/test/store'
else
  cache_dir = 'uploads/cache'
  store_dir = 'uploads/store'
end

Shrine.storages = {
  cache: Shrine::Storage::FileSystem.new('db', prefix: cache_dir),
  store: Shrine::Storage::FileSystem.new('db', prefix: store_dir),
  public_cache: Shrine::Storage::FileSystem.new('public', prefix: cache_dir),
  public_store: Shrine::Storage::FileSystem.new('public', prefix: store_dir)
}

Shrine.plugin :instrumentation

Shrine.logger = Rails.logger

Shrine.plugin :column

if Rails.env.development?
  tmp_storages = []
  tmp_storages << Shrine.storages[:cache]
  tmp_storages << Shrine.storages[:public_cache]
  tmp_storages.each do |tmp_storage|
    tmp_storage.clear! { |path| path.mtime < 1.week.ago } # delete files older than 1 week
  end
end
