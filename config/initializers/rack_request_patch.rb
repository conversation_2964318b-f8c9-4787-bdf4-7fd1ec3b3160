module Rack
  class Request
    # wywalone 192.x, bo u nas to sa adresy komputerow z wew. sieci a nie proxy, proxy (remote addr) jest na 10.x i sie lapie jako trusted
    def trusted_proxy?(ip)
      ip =~ /\A127\.0\.0\.1\Z|\A(10|172\.(1[6-9]|2[0-9]|30|31))\.|\A::1\Z|\Afd[0-9a-f]{2}:.+|\Alocalhost\Z|\Aunix\Z|\Aunix:/i
    end
    # NOTE: Rails 5: use this instead of patch
    # config.log_tags = [:uuid, :remote_ip]
    # config.action_dispatch.trusted_proxies = %w(127.0.0.1 ::1 fc00::/7 **********/12).map {
    #   |proxy| IPAddr.new(proxy) }
    # end
  end
end
