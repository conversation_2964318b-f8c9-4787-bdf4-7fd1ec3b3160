# Name: rack
# Version: 1.6.13
# Advisory: CVE-2020-8184
# Criticality: Unknown
# URL: https://groups.google.com/g/rubyonrails-security/c/OWtmozPH9Ak
# Title: Percent-encoded cookies can be used to overwrite existing prefixed cookie names
# Solution: upgrade to ~> 2.1.4, >= 2.2.3

# rubocop: disable Style/RescueModifier
module Rack
  module Utils
    module_function def parse_cookies_header(header)
      return {} unless header
      header.split(/[;] */n).each_with_object({}) do |cookie, cookies|
        next if cookie.empty?
        key, value = cookie.split('=', 2)
        cookies[key] = (unescape(value) rescue value) unless cookies.key?(key)
      end
    end
  end
end
# rubocop: enable Style/RescueModifier
