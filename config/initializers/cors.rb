# Be sure to restart your server when you modify this file.

# Avoid CORS issues when API is called from the frontend app.
# Handle Cross-Origin Resource Sharing (CORS) in order to accept cross-origin AJAX requests.

# Read more: https://github.com/cyu/rack-cors

if Rails.application.secrets['enable_cors']
  Rails.application.config.middleware.insert_before 0, Rack::Cors, debug: true, logger: (-> { Rails.logger }) do
    allow do
      origins '*'
      resource '*',
               headers: :any,
               methods: [:get, :post, :delete, :put, :patch, :options, :head],
               expose: ['etag', 'content-length', 'last-modified', # Rails
                        'access-token', 'expiry', 'token-type', 'uid', 'client', # devise_token_auth
                        'link', 'x-total', 'x-per-page', 'x-page',               # api-pagination
                        'x-holidays', 'x-holidays-user-statistics',
                        'X-Current-User-Can-Also-See-Previous-Months-Of-Other-Users',
                        'X-Flash-Messages'],
               max_age: 0
    end
  end
end
