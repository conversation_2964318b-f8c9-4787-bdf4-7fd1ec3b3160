# handle 204 No Content response

MicrosoftKiotaFaraday::FaradayRequestAdapter.class_eval do
  # override method defined in microsoft_kiota_faraday-0.12.0/lib/microsoft_kiota_faraday/faraday_request_adapter.rb:69
  def get_root_parse_node(response)
    raise StandardError, 'response cannot be null' unless response
    return Hashie::Mash.new if response.status == 204

    response_content_type = get_response_content_type(response)
    raise StandardError, 'no response content type found for deserialization' unless response_content_type

    @parse_node_factory.get_parse_node(response_content_type, response.body)
  end
end
