if Rails.env.development?
  require 'http_logger'
  # HttpLogger.logger = Logger.new(...) # defaults to Rails.logger if Rails is defined
  # HttpLogger.colorize = true # Default: true
  # HttpLogger.ignore = [/newrelic\.com/]
  HttpLogger.log_headers = true # Default: false
  # HttpLogger.log_request_body  = false  # Default: true
  # HttpLogger.log_response_body = false  # Default: true
  # HttpLogger.level = :info # Desired log level as a symbol. Default: :debug
end
