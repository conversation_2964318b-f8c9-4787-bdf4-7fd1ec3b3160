# LDAP
ldap_provider:
  connection:
    host: localhost # dev123
    port: 389
    auth:
      method: :simple
      username: "uid=admin"
      password: secretsecret
  trees:
    - name: root
      base: "dc=root,dc=efigence,dc=com"
      users_tree: "ou=users"
      groups_tree: "ou=groups"
      projects_tree: "ou=groups"
      booking_resources_tree: "ou=resources"
    - name: address_book
      base: "dc=address_book,dc=efigence,dc=com"
      users_tree: "ou=users"
      active_only: true
    - name: docs_cloud
      base: "dc=docs_cloud,dc=efigence,dc=com"
      users_tree: "ou=users"
      groups_tree: "ou=groups"
      projects_tree: "ou=projects"
      booking_resources_tree: "ou=resources"
      membership_column: docs_ldap
    - name: chat
      base: "dc=chat,dc=efigence,dc=com"
      users_tree: "ou=users"
      groups_tree: "ou=organizations"
      projects_tree: "ou=groups"
      membership_column: chat_ldap
  min_group_gid_number: **********
  min_project_gid_number: **********
  default_user_gid: '667'
  default_admin_gid: '666'

gitlab:
  protocol: gitlab
  host: git.efigence.com

docscloud:
  url: 'https://docs.efigence.com/'

# JPK
jpk:
  gtu_opts:
    - name: GTU_11
      desc: "w zakresie przenoszenia uprawnień do emisji gazów cieplarnianych, o których mowa w ustawie o systemie handlu uprawnieniami do emisji gazów cieplarnianych"
    - name: GTU_12
      desc: "o charakterze niematerialnym – wyłącznie: doradczych, księgowych, prawnych, zarządczych, szkoleniowych, marketingowych, firm centralnych (head offices), reklamowych, badania rynku i opinii publicznej, w zakresie badań naukowych i prac rozwojowych"
    - name: GTU_13
      desc: "transportowych i gospodarki magazynowej – Sekcja H PKWiU 2015 symbol ex 49.4, ex 52.1"
  transaction_code_opts:
    - name: SW
      desc: "dostawy w ramach sprzedaży wysyłkowej z terytorium kraju, o której mowa w art. 23 ustawy"
    - name: EE
      desc: "świadczenia usług telekomunikacyjnych, nadawczych i elektronicznych, o których mowa w art. 28k ustawy"
    - name: TP
      desc: "powiązania między nabywcą a dokonującym dostawy towarów lub usługodawcą, o których mowa w art. 32 ust. 2 pkt 1 ustawy"
    - name: TT_WNT
      desc: "wewnątrzwspólnotowe nabycie towarów dokonanego przez drugiego w kolejności podatnika VAT w ramach transakcji trójstronnej w procedurze uproszczonej, o której mowa w dziale XII rozdział 8 ustawy"
    - name: TT_D
      desc: "dostawa towarów poza terytorium kraju dokonana przez drugiego w kolejności podatnika VAT w ramach transakcji trójstronnej w procedurze uproszczonej, o której mowa w dziale XII rozdział 8 ustawy"
    - name: MR_T
      desc: "świadczenie usług turystyki opodatkowanych na zasadach marży zgodnie z art. 119 ustawy"
    - name: MR_UZ
      desc: "dostawa towarów używanych, dzieł sztuki, przedmiotów kolekcjonerskich i antyków, opodatkowana na zasadach marży zgodnie z art. 120 ustawy"
    - name: I_42
      desc: "wewnątrzwspólnotowa dostawa towarów następująca po imporcie tych towarów w ramach procedury celnej 42"
    - name: I_63
      desc: "wewnątrzwspólnotowa dostawa towarów następująca po imporcie tych towarów w ramach procedury celnej 63"
    - name: B_SPV
      desc: "transfer bonu jednego przeznaczenia dokonany przez podatnika działającego we własnym imieniu, opodatkowanego zgodnie z art. 8a ust. 1 ustawy"
    - name: B_SPV_DOSTAWA
      desc: "dostawa towarów oraz świadczenia usług, których dotyczy bon jednego przeznaczenia na rzecz podatnika, który wyemitował bon zgodnie z art. 8a ust. 4 ustawy"
    - name: B_MPV_PROWIZJA
      desc: "świadczenie usług pośrednictwa oraz innych usług dotyczących transferu bonu różnego przeznaczenia, opodatkowanych zgodnie z art. 8b ust. 2 ustawy"
    - name: MPP
      desc: "transakcje objęte obowiązkiem stosowania mechanizmu podzielonej płatności"
# Redmine
redmine_auth_source_id: 1
new_project_user_role_id: 55
# Rack::Attack middleware settings
throttle_req_limit: 1000
throttle_req_period: 30
throttle_login_limit: 1000
throttle_login_period: 30
gus_bir1:
  production: false
  client_key: abcde12345abcde12345
