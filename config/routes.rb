Rails.application.routes.draw do
  use_doorkeeper do
    skip_controllers :authorizations, :applications, :authorized_applications
  end
  # /uploads/store/holiday_request/254/file/bd88392e87723e85a9e9decdc5c72637-original.png
  # api prefix is hack for spa adding auth headers only for specific host and path
  get '/api/uploads/:storage/:model/:id/:attachment/:filename.:extension', to: 'api/v1/holiday_requests#file'
  get '/uploads/:storage/:model/:id/:attachment/:filename.:extension', to: 'api/v1/holiday_requests#file'

  Object.send(:remove_const, 'LATEST_API_VERSION') if defined?(LATEST_API_VERSION)
  # rubocop:disable Lint/ConstantDefinitionInBlock
  LATEST_API_VERSION = 2
  # rubocop:enable Lint/ConstantDefinitionInBlock

  scope module: 'api', path: '/api' do
    # post '/api/auth/confirmation', to: 'overrides/confirmations#create'
    mount_devise_token_auth_for 'User',
                                at: 'auth',
                                skip: %i[omniauth_callbacks registrations],
                                controllers: {
                                  confirmations: 'overrides/confirmations'
                                }

    scope module: :v1, defaults: { format: :json } do
      as(:user) do
        resources :registry_categories, except: %i[new edit] do
          member do
            patch :activate
            patch :close
          end
        end
        resources :deletion_requests, only: %i[index show create update destroy] do
          member do
            patch :processing
            patch :complete
          end
        end

        resources :registry_activities, except: %i[edit] do
          member do
            patch :activate
            patch :close
          end
        end
        resources :violation_registers, except: %i[new edit] do
          member do
            patch :processing
            patch :reported
            patch :complete
            get :attachment
          end
        end
        resources :risk_analyses, except: %i[new edit] do
          member do
            post :create_risk_analysis_item
            patch :update_risk_analysis_item
            delete :destroy_risk_analysis_item
          end
        end
        resources :attachments, only: %i[create] do
          collection do
            get '/file/:id', action: :file, as: :file
            delete :bulk_destroy
          end
        end

        resources :kubernetes_clusters, only: :index do
          collection do
            get :keys
          end
        end

        # assets
        namespace :assets do
          resources :servers, only: %i[create show]
          resources :kubernetes_namespaces, only: %i[create show index]
          resources :cms_access, only: %i[create show]
          resources :vpns, only: %i[create show]
          resources :ssl_certificates, only: %i[create show]
          resources :google_services, only: %i[create show]
          resources :domains, only: %i[create show]
          resources :generic_assets, only: %i[create show]
        end

        resources :assets, only: %i[index show update] do
          collection do
            get :stats
          end

          member do
            get :history
            patch :allow_execution
            patch :confirm_execution
            patch :activate
            patch :reject
            patch :close
            patch :decommission
          end

          collection do
            get :asset_orders
          end
        end

        resources :remote_work_periods, except: :edit do
          collection do
            get :users
            get :report
          end

          member do
            patch :accept
            patch :reject
            get :history
          end
        end

        resources :training_budgets, except: %i[new edit]
        resources :training_requests, except: %i[edit] do
          member do
            patch :accept
            patch :reject
          end
        end

        resources :bios, only: %i[index create show update]
        resources :links, only: :destroy
        resources :jpk_options, only: :index
        get 'asset/requests', to: 'assets#assets_requests_index'
        get 'asset/my_requests', to: 'assets#my_requests'
        get 'asset/my_projects_assets', to: 'assets#my_projects_assets'

        resources :inventory_items, only: %i[create show index update] do
          member do
            patch :activate
            patch :reject
            patch :close
          end
        end

        resources :temporary_attachments, only: :create
        resources :attachment_wysiwygs, only: :create
        resources :activities, only: [:index]
        resources :request_tracker_issues, only: :create
        resources :project_agreements, only: :show
        resources :approvals, only: :index do
          collection do
            get :not_accepted
          end
          member do
            patch :accept
          end
        end
        match '/agreements', to: 'agreements#form_data', via: 'options'
        resources :default_agreement_contents, only: %i[index update]
        resources :agreements do
          resources :approvals, only: %i[index update]
        end
        resources :api_keys, except: %i[new edit]
        resources :companies, except: %i[new edit]
        resources :departments, except: %i[new edit] do
          resources :department_members, only: :index
        end
        namespace :evaluations do
          resources :survey_answers, only: %i[index show update]
          resources :users, only: %i[index show] do
            resources :evaluations, only: %i[new create update show] do
              member do
                patch :start
                patch :stop
                patch :discard
              end
            end
          end
        end
        resources :global_roles, except: %i[new edit]
        resources :groups, except: %i[new edit]
        resources :holiday_requests, except: %i[new edit] do
          collection do
            get 'users_list', to: 'holiday_requests/users#users_list'
            get 'users_csv', to: 'holiday_requests/users#users_csv'
            get 'users'
            get 'category_options'
            get 'category_options_for_search'
            get 'department_options'
            get 'project_options'
            get 'user_options'
          end
          member do
            get 'convert_to_options'
            get 'history'
          end
        end
        resources :calendar_months, only: %i[index] do
          collection do
            get :form_data
            put :bulk_update
          end
        end
        resources :memberships, except: %i[new edit]
        resources :notifications, except: %i[new edit]
        resources :clients, except: %i[new edit] do
          resources :client_addresses, except: %i[new edit]
          member do
            patch :activate
          end
        end
        resources :contractors, except: %i[new edit] do
          member do
            patch :activate
          end
        end
        get 'controlling', to: 'accounting#index'
        get 'controlling/:id', to: 'accounting#show'
        get 'controlling/client_projects/:id', to: 'accounting#client_projects'
        get 'controlling_payments_report', to: 'accounting#payments_report'

        resources :employees_reports, only: %i[index create] do
          member do
            get :file
          end
        end

        resources :b2b_reports, only: %i[index create] do
          member do
            get :file
          end
        end

        namespace :accounting do
          resources :invoices, except: %i[new create destroy] do
            collection do
              get :monthly_report
            end
            member do
              patch :accept
              patch :issue
              patch :reject
              patch :back_to_pending
              get :invoice_document
              post :invoice_document, action: :create_invoice_document
              get :history
            end
          end
          resources :payments, only: %i[index create show update]
        end

        match '/booking_resources', to: 'booking_resources#form_data', via: 'options'
        resources :booking_resources, except: %i[new edit]
        namespace :b2b do
          resources :cost_invoices, except: %i[edit destroy] do
            member do
              patch :accept
              patch :reject
              patch :withdraw
              patch :recall
              get :document
              get :history
            end
            collection do
              post :ocr
              post :perform_daily_worker
            end
          end
        end
        namespace :dms do
          resources :cost_invoices, except: :edit do
            member do
              get :document
              get :history
              patch :accept
              patch :reject
              patch :recall
            end
            collection do
              post :ocr
              post :perform_daily_worker
            end
          end
        end

        resources :cost_invoice_acceptances, only: %i[index show] do
          member do
            patch :accept
            patch :reject
          end
        end
        resources :cost_allocation_templates, only: %i[index create update destroy]

        resource :dashboard, only: :show

        resources :accounting_numbers, except: %i[new edit]
        resources :cost_account_numbers, except: %i[new edit destroy]
        resources :bank_accounts, except: %i[new edit destroy]
        resources :external_costs, only: :index, constraints: { format: 'xlsx' }

        resources :projects, except: %i[new edit] do
          resources :external_costs, except: :show
          resources :docs_files, only: %i[index create new] do
            resources :docs_file_comments, only: %i[index create]
          end
          resources :public_activities, only: :index
          resources :approvals, only: %i[index update]
          resources :project_agreements, only: %i[index show update] do
            collection do
              get '/file/:id', action: :file
              get '/selected', action: :selected
            end
          end
          resource :payment_schedule, except: %i[new]
          resources :invoices, except: %i[new edit destroy] do
            get 'form_data/:payment_id', on: :collection, action: 'form_data',
                                         as: :form_data
          end

          member do
            patch 'archive'
            patch 'unarchive'
            patch 'close'
            patch 'reopen'
            get   'copy_source'
            post  'copy'
            get   'regression_stats'
            get   'implementation_stats'
            get   'gitlab_repositories'
          end
          collection do
            get 'report'
            get 'regressive'
            get 'effectiveness_stats'
            get 'utilization_stats'
            get :idle
          end
        end
        resources :logs, only: %i[index] do
          collection do
            get 'tab_list'
          end
        end
        resources :docs_files, only: %i[index new]
        resources :my_payment_schedules, only: :index
        resources :positions, except: %i[new edit]
        resources :mpk_numbers, only: %i[index]
        resources :roles, except: %i[new edit]
        resources :users, except: %i[new edit] do
          resources :user_entry_cards, except: %i[new edit destroy]
          resources :user_contracts, except: %i[new edit]
          member do
            get 'history'
            put 'impersonate'
            post :resend_confirmation_instructions
          end
        end
        resources :public_keys, only: %i[index create destroy]
        resources :wifi_tokens, only: %i[create index]
        resources :cards, only: :index
        namespace :pages do
          resource :profile, except: %i[new edit] do
            patch 'save_preferences'
            patch 'dismiss_onboarding'
          end
          resource :navbar, only: [:show] do
            collection do
              get 'public_show'
            end
          end
          resource :dashboard, only: :show
        end
        resources :issues, only: [] do
          collection do
            get :trackers
          end
        end
      end
      resources :days_off, only: :index
      patch '/refresh_account/:token', to: 'refresh_account#perform'
      post 'check_absences/check'
      get 'random/:length', to: proc { |env|
        param_length = env['action_dispatch.request.path_parameters'][:length].to_i
        length = (8..20).to_a.include?(param_length) ? param_length : 8
        [200,
         { 'Content-Type' => 'application/json; charset=utf-8' },
         [{ random: Devise.friendly_token.first(length) }.to_json]]
      }
    end

    namespace :v1 do
      get 'api-docs.json', to: 'apidocs#index'
    end

    get '*path', to: 'v1/api#route_not_found'
  end

  # http://wiki.non.3dart.com/JSPWiki/Wiki.jsp?page=Projects.java.Monitoring
  match '/isItWorking', to: proc { |_env|
                              if File.exist?(Rails.root.join('tmp/isItWorking.txt'))
                                [200, {}, ['I am working.']]
                              else
                                [500, {}, ["I'm not working."]]
                              end
                            }, via: %i[get post head]
  get '/isItWorking/REVISION', to: proc { |_env| [200, {}, [REVISION.to_s]] }
  get '/isItWorking/FRONT_REVISION', to: proc { |_env| [200, {}, [FRONT_REVISION.to_s]] }

  require 'sidekiq/web'
  require 'sidekiq-scheduler/web'
  if Rails.env.production?
    Sidekiq::Web.use Rack::Auth::Basic do |username, password|
      # Protect against timing attacks:
      # - See https://codahale.com/a-lesson-in-timing-attacks/
      # - See https://thisdata.com/blog/timing-attacks-against-string-comparison/
      # - Use & (do not use &&) so that it doesn't short circuit.
      # - Use digests to stop length information leaking (
      # see also ActiveSupport::SecurityUtils.variable_size_secure_compare)
      Rails.application.secrets['sidekiq_username'].present? &&
        Rails.application.secrets['sidekiq_password'].present? &&
        (ActiveSupport::SecurityUtils.secure_compare(
          Digest::SHA256.hexdigest(username),
          Digest::SHA256.hexdigest(Rails.application.secrets['sidekiq_username'])
        ) &
          ActiveSupport::SecurityUtils.secure_compare(
            Digest::SHA256.hexdigest(password),
            Digest::SHA256.hexdigest(Rails.application.secrets['sidekiq_password'])
          ))
    end
  end
  mount Sidekiq::Web, at: '/sidekiq'

  if Rails.env.development?
    # require 'sidekiq/web'
    # mount Sidekiq::Web => '/sidekiq'
    mount LetterOpenerWeb::Engine, at: '/letter_opener'
  end

  get '/account_confirmation_success', to: 'application#account_confirmation_success', as: :account_confirmation_success
  get '/', to: 'application#spa', as: :root
end
