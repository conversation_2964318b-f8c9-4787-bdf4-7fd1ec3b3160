#
# ln -s development.local.yml.example development.local.yml
#
rabbit_enabled: true # audit, wifi token
rabbit:
  connection:
    host: 127.0.0.1
    user: guest
    pass: guest
  node_uuid: 1
  app_id: <EMAIL>
audit_enabled: true # if rabbit disabled, it fallbacks to sidekiq logger
audit_exchange: 'audit'
rabbit_audit:
  connection:
    host: 127.0.0.1
    user: guest
    pass: guest
  node_uuid: 1
  app_id: <EMAIL>
