secret_key_base: 4f7ca6fcbff16d3def2f93481b138dab1259c2985012ed5ef5e99fd892978770433b3b7bd5b6ca672b281a54dca67533d5875770599101a7c99ab52cac997790
redmine_api:
  uri: 'http://local.non.3dart.com:3001/imperator_api/v1'
  # basic_auth:
  #  username: redmine
  #  password: 'redmine'
  imperator_api_key: 'de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a'
  redmine_api_key: '83f9887452a64366da8102e711a1284eb5dae0de'
  redmine_absences_check_api_key: '21775217f0ef4b8e56ce51a3213fdsdfaga3423'
  dashboard_stats:
    role_ids: [6]
    tester_role_id: 9
  # no_proxy: true
  # host_header: 'redmine.dev.non.3dart.com'
click_up_api:
  uri: 'https://api.clickup.com/api/v2'
  client_id: '5ZSN76RCUS4TWSXQV3QE4X9017V26YL2A'
  client_secret: 'XB2MP245IKOA28NVD48BYF3WSSJ8H20NHRJVF74VTPB8Y0EU9MGF32PFXHI6D61HA'
  api_key: 'pk_188448912_CEZLYGB9AVK1U5UOM12R1RA1Y5105TIRA'
space_cooperative_project_id: ************
arte_space_accounting_number: [0, 706]
nbp_api: 'https://api.nbp.pl/api/exchangerates'
redmine_working_time:
  base_url: 'http://www.example.com'
  api_key: ABCDEF
  api_key: 'M7vFVP9Q'
  placeholder_project_identifiers:
    artegence: 'artegence-prace-wewnetrzne'
    efigence: 'efigence-prace-wewnetrzne'
invoices_integration:
  files_path: <%= Rails.root.join('db/uploads/test') %>
  estelligence_share_path: <%= Rails.root.join('db/uploads/test/estelligence') %>
  bi_share_path: <%= Rails.root.join('db/uploads/test/bi') %>
mailer:
  from: "no-reply@localhost"
  default_url_options:
    host: "test.host"
    protocol: "http"
  smtp_settings:
   address: "localhost"
   port: 25
   enable_starttls_auto: false
   openssl_verify_mode: "none" # Only use this option for a self-signed and/or wildcard certificate
exception_notification:
  email_prefix: "[IMPERATOR] "
  sender_address: "exception.notifier@localhost"
  exception_recipients: <%= %w( <EMAIL> <EMAIL> ) %>
use_proxy: false
proxy:
  protocol: "http://"
  address: proxy.non.3dart.com
  port: 3128
memcached_enabled: false
memcached:
  hosts:
    - '127.0.0.1:11211'
  namespace: imperator
  compress: true
  failover: true
  socket_timeout: 1.5
  socket_failure_delay: 0.2
redis_enabled: false
redis:
  url: 'redis://localhost:6379/3'
# Swagger
swagger_host: 'localhost:3000'
swagger:
  api_url_base: 'http://localhost:3000'
allow_impersonification_without_authentication:	true # DANGER! use `true` only for development or test server!
disable_hr_workers:
  disable_absence_quota_worker: false
  disable_holiday_request_reminder_worker: false
  disable_holiday_request_convert_sick_to_vacation_worker: false
  disable_holiday_request_postponed_reminder_worker: false
nextcloud:
  url: test_url
  username: test_username
  password: test_password
features_management_enabled: true
disable_evaluation_workers:
  disable_holiday_request_reminder_worker: false
features_management_login: 'test'
features_management_password: 'test'
impersonation_enabled: true
impersonation_login: 'test'
impersonation_password: 'test'
sidekiq_username: test
sidekiq_password: 'test'
capybara:
  # override this in settings/test.local.yml
  # `which google-chrome`
  # https://sites.google.com/a/chromium.org/chromedriver/capabilities
  chrome_executable_path: '/usr/bin/google-chrome'
rabbit_enabled: false # audit, wifi token
encrypted_db_key: bc49e35008364c980834360107702dc67c47c858c542894935a5014db821faaa3e4220d8953ab1a1d368b11d664efd6735e60e73ed20324327b3336f4fe987b2
rabbit:
  connection:
    host: 127.0.0.1
    user: test
    pass: test
  node_uuid: 1
  app_id: <EMAIL>
audit_enabled: false # if rabbit disabled, it fallbacks to sidekiq logger
audit_exchange: 'audit_test'
rabbit_audit:
  connection:
    host: 127.0.0.1
    user: guest
    pass: guest
  node_uuid: 1
  app_id: <EMAIL>
overwrite_mail_whitelist:
  - mromanow
overwrite_mail_list_to:
  - <EMAIL>
holiday_request_report_to:
  - <EMAIL>
redmine_overtime_activity_id: 16
# Comarch OCR API
comarch_ocr_api:
  enabled: false
  host: 'http://localhost:3000'
  credentials:
    authKey: 'authKey'
    authSecret: 'authSecret'
microsoft_graph_api:
  tenant_id: 'tenant_id'
  client_id: 'client_id'
  client_secret: 'client_secret'
holiday_projects: [3891, 3890]
holiday_accounting_numbers: [707, 706, 101, 102]
inbox_client_id: 2817
payment_schedules_lock_day: 2
global_hr_manager_id: 6
log_files_names: [sidekiq-new.log]
