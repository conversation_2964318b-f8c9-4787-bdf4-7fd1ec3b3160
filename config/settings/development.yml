secret_key_base: aff3af4533489ac7a129327a6a9dde7b94a521578d31007246d1764307f3cb62148c006c606dec1e0ac877c4e8f41ff758406baaa46b747d913233c046c3675d
redmine_api:
  uri: 'http://localhost:3002/imperator_api/v1'

  # basic_auth:
  #  username: redmine
  #  password: 'redmine'
  imperator_api_key: '2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0'
  # imperator_api_key: 'de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a'
  redmine_api_key: '2a015af0aaa2e0b525831b9c2f238b14311bf012'
  # redmine_api_key: '83f9887452a64366da8102e711a1284eb5dae0de'
  redmine_absences_check_api_key: '21775217f0ef4b8e56ce51a3213fdsdfaga3423'
  # no_proxy: true
  # host_header: 'redmine.dev.non.3dart.com'
  dashboard_stats:
    role_ids: [4, 6]
    tester_role_id: 9
click_up_api:
  uri: 'https://api.clickup.com/api/v2'
  client_id: '5ZSN76RCUS4TWSXQV3QE4X9017V26YL2A'
  client_secret: 'XB2MP245IKOA28NVD48BYF3WSSJ8H20NHRJVF74VTPB8Y0EU9MGF32PFXHI6D61HA'
  api_key: 'pk_200469486_BQ9S5OGKCLC39P8ZU2X0GBHNFAMRH28LA'
space_cooperative_project_id: ***********
arte_space_accounting_number: [0, 706]
nbp_api: 'https://api.nbp.pl/api/exchangerates'
redmine_working_time:
  base_url: 'http://localhost:3002'
  api_key: 'M7vFVP9Q'
  placeholder_project_identifiers:
    artegence: 'artegence-prace-wewnetrzne'
    efigence: 'efigence-prace-wewnetrzne'
invoices_integration:
  files_path: 'db/uploads/invoices'
mailer:
  from: "no-reply@localhost"
  default_url_options:
    host: "localhost:3000"
    protocol: "http"
  smtp_settings:
   address: "localhost"
   port: 1025
   enable_starttls_auto: false
   openssl_verify_mode: "none" # Only use this option for a self-signed and/or wildcard certificate
exception_notification:
  email_prefix: "[IMPERATOR] "
  sender_address: "exception.notifier@localhost"
  exception_recipients: <%= %w( <EMAIL> <EMAIL> ) %>
  background_sections:
    - backtrace
    - sidekiq_env
use_proxy: false
proxy:
  protocol: "http://"
  address: proxy.non.3dart.com
  port: 3128
memcached_enabled: false
memcached:
  hosts:
    - '127.0.0.1:11211'
  namespace: imperator_development
  compress: true
  failover: true
  socket_timeout: 1.5
  socket_failure_delay: 0.2
redis_enabled: false
redis:
  url: 'redis://localhost:6379/3'
  inherit_socket: true
# Swagger
swagger_host: 'localhost:3000'
swagger:
  api_url_base: 'http://localhost:3000'
allow_impersonification_without_authentication:	true # DANGER! use `true` only for development or test server!
overwrite_mail_to: false
owncloud:
  url: http://cloudshare.non.3dart.com
  username: redmine
  password: y9nB33nrpi3V
disable_hr_workers:
  disable_absence_quota_worker: false
  disable_holiday_request_reminder_worker: false
  disable_holiday_request_convert_sick_to_vacation_worker: false
  disable_holiday_request_postponed_reminder_worker: false
features_management_enabled: true
features_management_login: 'admin'
features_management_password: '39b3de58ef9fc451cc53d881a005460c'
disable_evaluation_workers:
  disable_holiday_request_reminder_worker: false
impersonation_enabled: true
impersonation_login: 'admin'
impersonation_password: '75004d8c664638ff40fda8f378b4f70d'
rabbit_enabled: false # audit, wifi token
encrypted_db_key: bc49e35008364c980834360107702dc67c47c858c542894935a5014db821faaa3e4220d8953ab1a1d368b11d664efd6735e60e73ed20324327b3336f4fe987b2
rabbit:
  connection:
    host: 127.0.0.1
    user: guest
    pass: guest
  node_uuid: 1
  app_id: <EMAIL>
audit_enabled: true # if rabbit disabled, it fallbacks to sidekiq logger
audit_exchange: 'audit'
rabbit_audit:
  connection:
    host: 127.0.0.1
    user: guest
    pass: guest
  node_uuid: 1
  app_id: <EMAIL>
holiday_request_report_to:
  - <EMAIL>
accounting:
  remind_after_hours: 5
  repeat_remind_after_days: 1
  remind_upcoming_payments_before_days: 4
  remind_exceeded_payments_after_days: 1
  repeat_remind_exceeded_payments_after: 3
  roles_to_notify: [121, 135]
  acc_users_to_notify: [6855]
  accounting_groups: [430]
  invoice_sending_email_from:
    Efigence: <EMAIL>
    Artegence: <EMAIL>
  invoice_sending_email_bcc:
    Efigence: <EMAIL>
    Artegence: <EMAIL>
redmine_absence_activity_id: 182
redmine_overtime_activity_id: 16
# Comarch OCR API
comarch_ocr_api:
  enabled: false
  host: 'https://ocr.erp.comarch.pl'
  credentials:
    authKey: 'authKey'
    authSecret: 'authSecret'
default_chief_role: Observer
default_management_role: Supervisor
admin_asset_placeholder_project_id: 3646
microsoft_graph_api:
  tenant_id: 'tenant_id'
  client_id: 'client_id'
  client_secret: 'client_secret'
devise_token_auth:
  redirect_whitelist:
    - http://ui-mf.imperator.svc.dev.kube.non.3dart.com/*
holiday_projects: [3891, 3890]
holiday_accounting_numbers: [707, 706]
inbox_client_id: 2817
payment_schedules_lock_day: 2
global_hr_manager_id: 6
log_files_names: [sidekiq-new.log]
default_groups:
  253: [62]
