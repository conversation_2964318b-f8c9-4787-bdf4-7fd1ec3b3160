#
# ln -s production.local.yml.example production.local.yml
#
memcached_enabled: true
# Swagger
swagger_host: 'localhost:3000'
swagger:
  api_url_base: 'http://localhost:3000'
allow_impersonification_without_authentication:	true # DANGER! use `true` only for development or test server!
disable_hr_workers:
  disable_absence_quota_worker: false
  disable_holiday_request_reminder_worker: false
  disable_holiday_request_convert_sick_to_vacation_worker: false
  disable_holiday_request_postponed_reminder_worker: false
rabbit_enabled: true # audit, wifi token
rabbit:
  connection:
    host: 127.0.0.1
    user: guest
    pass: guest
  node_uuid: 1
  app_id: <EMAIL>
audit_enabled: true # if rabbit disabled, it fallbacks to sidekiq logger
audit_exchange: 'audit'
rabbit_audit:
  connection:
    host: 127.0.0.1
    user: guest
    pass: guest
  node_uuid: 1
  app_id: <EMAIL>
