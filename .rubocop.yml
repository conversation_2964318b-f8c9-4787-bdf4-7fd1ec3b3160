# https://github.com/bbatsov/rubocop/blob/master/config/default.yml
require: rubocop-rails

AllCops:
  DisabledByDefault: false
  TargetRubyVersion: 3.1.3
  Exclude:
    - 'db/schema.rb'
    - 'db/migrate/*'
    - 'bin/*'
    - 'test/**/*_test.rb'
    - 'test/factories/*'
    - 'test/test_helper.rb'
    - 'app/controllers/api/v1/concerns/documentation/*'
    - 'app/models/concerns/documentation/*'
    - 'lib/**/*'
    - 'config/initializers/new_framework_defaults_7_0.rb'
  NewCops: enable
  DisplayCopNames: true

Rails:
  Enabled: true

Layout/LineLength:
  Max: 130

Metrics/BlockLength:
  Max: 50
  Exclude:
  - 'test/**/*_test.rb'
  - 'config/routes.rb'

Style/Documentation:
  Enabled: false

Metrics/AbcSize:
  Max: 16

Metrics/MethodLength:
  Max: 12
  Exclude:
    - 'db/migrate/*.rb'

Metrics/ClassLength:
  Max: 500

Rails/HasManyOrHasOneDependent:
  Enabled: false

Rails/BelongsTo:
  Enabled: true

Naming/MemoizedInstanceVariableName:
  Enabled: false

Rails/SkipsModelValidations:
  Enabled: true
  Exclude:
    - 'test/**/*'

Style/FrozenStringLiteralComment:
  Enabled: false
Layout/BeginEndAlignment: # (new in 0.91)
  Enabled: true
Layout/EmptyLinesAroundAttributeAccessor: # (new in 0.83)
  Enabled: true
Layout/SpaceAroundMethodCallOperator: # (new in 0.82)
  Enabled: true
Lint/BinaryOperatorWithIdenticalOperands: # (new in 0.89)
  Enabled: true
Lint/ConstantDefinitionInBlock: # (new in 0.91)
  Enabled: true
Lint/DeprecatedOpenSSLConstant: # (new in 0.84)
  Enabled: true
Lint/DuplicateElsifCondition: # (new in 0.88)
  Enabled: true
Lint/DuplicateRequire: # (new in 0.90)
  Enabled: true
Lint/DuplicateRescueException: # (new in 0.89)
  Enabled: true
Lint/EmptyConditionalBody: # (new in 0.89)
  Enabled: true
Lint/EmptyFile: # (new in 0.90)
  Enabled: true
Lint/FloatComparison: # (new in 0.89)
  Enabled: true
Lint/HashCompareByIdentity: # (new in 0.93)
  Enabled: true
Lint/IdentityComparison: # (new in 0.91)
  Enabled: true
Lint/MissingSuper: # (new in 0.89)
  Enabled: true
Lint/MixedRegexpCaptureTypes: # (new in 0.85)
  Enabled: true
Lint/OutOfRangeRegexpRef: # (new in 0.89)
  Enabled: true
Lint/RaiseException: # (new in 0.81)
  Enabled: true
Lint/RedundantSafeNavigation: # (new in 0.93)
  Enabled: true
Lint/SelfAssignment: # (new in 0.89)
  Enabled: true
Lint/StructNewOverride: # (new in 0.81)
  Enabled: true
Lint/TopLevelReturnWithArgument: # (new in 0.89)
  Enabled: true
Lint/TrailingCommaInAttributeDeclaration: # (new in 0.90)
  Enabled: true
Lint/UnreachableLoop: # (new in 0.89)
  Enabled: true
Lint/UselessMethodDefinition: # (new in 0.90)
  Enabled: true
Lint/UselessTimes: # (new in 0.91)
  Enabled: true
Style/AccessorGrouping: # (new in 0.87)
  Enabled: true
Style/BisectedAttrAccessor: # (new in 0.87)
  Enabled: true
Style/CaseLikeIf: # (new in 0.88)
  Enabled: true
Style/ClassEqualityComparison: # (new in 0.93)
  Enabled: true
Style/CombinableLoops: # (new in 0.90)
  Enabled: true
Style/ExplicitBlockArgument: # (new in 0.89)
  Enabled: true
Style/ExponentialNotation: # (new in 0.82)
  Enabled: true
Style/GlobalStdStream: # (new in 0.89)
  Enabled: true
Style/HashAsLastArrayItem: # (new in 0.88)
  Enabled: true
Style/HashEachMethods: # (new in 0.80)
  Enabled: false
Style/HashLikeCase: # (new in 0.88)
  Enabled: true
Style/HashSyntax:
  EnforcedShorthandSyntax: 'either'
Style/HashTransformKeys: # (new in 0.80)
  Enabled: true
Style/HashTransformValues: # (new in 0.80)
  Enabled: true
Style/KeywordParametersOrder: # (new in 0.90)
  Enabled: true
Style/OptionalBooleanParameter: # (new in 0.89)
  Enabled: true
  AllowedMethods:
    - perform
Style/RedundantAssignment: # (new in 0.87)
  Enabled: true
Style/RedundantFetchBlock: # (new in 0.86)
  Enabled: true
Style/RedundantFileExtensionInRequire: # (new in 0.88)
  Enabled: true
Style/RedundantRegexpCharacterClass: # (new in 0.85)
  Enabled: true
Style/RedundantRegexpEscape: # (new in 0.85)
  Enabled: true
Style/RedundantSelfAssignment: # (new in 0.90)
  Enabled: true
Style/SingleArgumentDig: # (new in 0.89)
  Enabled: true
Style/SlicingWithRange: # (new in 0.83)
  Enabled: true
Style/SoleNestedConditional: # (new in 0.89)
  Enabled: true
Style/StringConcatenation: # (new in 0.89)
  Enabled: true
Rails/ActiveRecordCallbacksOrder: # (new in 2.7)
  Enabled: false
Rails/AfterCommitOverride: # (new in 2.8)
  Enabled: true
Rails/AttributeDefaultBlockValue: # (new in 2.9)
  Enabled: true
Rails/FindById: # (new in 2.7)
  Enabled: true
Rails/Inquiry: # (new in 2.7)
  Enabled: true
Rails/MailerName: # (new in 2.7)
  Enabled: true
Rails/MatchRoute: # (new in 2.7)
  Enabled: true
Rails/NegateInclude: # (new in 2.7)
  Enabled: true
Rails/Pluck: # (new in 2.7)
  Enabled: true
Rails/PluckInWhere: # (new in 2.7)
  Enabled: true
Rails/RenderInline: # (new in 2.7)
  Enabled: true
Rails/RenderPlainText: # (new in 2.7)
  Enabled: true
Rails/ShortI18n: # (new in 2.7)
  Enabled: true
Rails/SquishedSQLHeredocs: # (new in 2.8)
  Enabled: false
Rails/WhereEquals: # (new in 2.9)
  Enabled: true
Rails/WhereExists: # (new in 2.7)
  Enabled: true
Rails/WhereNot: # (new in 2.8)
  Enabled: true
Rails/LexicallyScopedActionFilter:
  Enabled: false

Style/ClassAndModuleChildren:
  Enabled: false
