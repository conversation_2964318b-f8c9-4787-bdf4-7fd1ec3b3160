class CostAllocationTemplate < ApplicationRecord
  belongs_to :user

  has_many :cost_allocation_template_positions, dependent: :destroy

  validates :name, presence: true
  validate :positions_share_amounts_consistency

  accepts_nested_attributes_for :cost_allocation_template_positions, allow_destroy: true

  private

  def total_positions_share_amount
    cost_allocation_template_positions.reject(&:marked_for_destruction?)
                                      .sum(&:share_amount)
  end

  def positions_share_amounts_consistency
    return if total_positions_share_amount == 100

    errors.add(:total_positions_share_amount, :is_not_equal_to_one)
  end
end
