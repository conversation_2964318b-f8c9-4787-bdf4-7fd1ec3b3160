class CalendarMonth < ApplicationRecord
  ALLOWED_HOURS = (0..24).to_a.freeze

  belongs_to :user

  serialize :days, Hash

  validates :year, presence: true,
                   numericality: { only_integer: true }
  validates :month, presence: true,
                    numericality: { only_integer: true, greater_than_or_equal_to: 1, less_than_or_equal_to: 12 },
                    uniqueness: { scope: %i[user_id year] }
  validates_each :days, if: %i[year month] do |record, _attr, value|
    allowed_days = (1..(Time.days_in_month(record.month, record.year) || 31)).to_a
    days_valid = (value.keys - allowed_days).empty?
    hours_valid = (value.values - ALLOWED_HOURS).empty?

    if days_valid && hours_valid
      record.total_hours = value.values.sum
    else
      record.errors.add(:days)
    end
  end

  before_validation :normalize_days
  after_commit :sync_holiday_requests

  private

  def normalize_days
    self.days = days.to_h.transform_keys { _1.to_s.to_i }
  end

  def sync_holiday_requests
    user.applicant_holiday_requests
        .between(Date.new(year, month, 1), Date.new(year, month, -1)).each do |holiday_request|
      holiday_request.starts_on_will_change! # Trigger a redmine sync for a visible record
      holiday_request.save
    end
  end
end
