require 'associated_bubbling_validator'

class Invoice < ApplicationRecord
  include Documentation::InvoiceModel
  include InvoiceComparable
  include AASM
  include ActiveSnapshot

  belongs_to :revenue_account, optional: true
  belongs_to :payment
  belongs_to :user, optional: true
  belongs_to :client
  belongs_to :associated_advance_invoice, class_name: 'Invoice',
                                          inverse_of: :advance_accounting_invoices,
                                          optional: true
  belongs_to :advance_accounting_invoices, class_name: 'Invoice',
                                           foreign_key: :associated_advance_invoice_id,
                                           inverse_of: :associated_advance_invoice,
                                           optional: true
  belongs_to :client_address, optional: true
  belongs_to :bank_account, optional: true

  has_many :attachments, as: :attachable, inverse_of: :attachable
  has_many :required_attachments, class_name: 'InvoiceRequiredAttachment', inverse_of: :invoice
  has_one :invoice_document, dependent: :destroy

  has_many :mpk_positions, inverse_of: :invoice, index_errors: true,
                           dependent: :destroy
  has_many :mpk_numbers, through: :mpk_positions
  has_many :invoice_positions, -> { order(:id) }, inverse_of: :invoice, index_errors: true,
                                                  dependent: :destroy
  has_many :allocation_versions, dependent: :destroy

  accepts_nested_attributes_for :mpk_positions, :invoice_positions, :required_attachments, :invoice_document,
                                allow_destroy: true

  validates :payment, :due_date, :invoice_date, :sell_date, :revenue_account, presence: true,
                                                                              if: :should_validate?
  validates :file_name, presence: true, length: { maximum: 80 }, if: :should_validate?
  validates :correction_reason, presence: true,
                                if: -> { amendment? && should_validate? }
  validates :description, length: { maximum: 50 }, if: :should_validate?
  validate :actual_amendment, if: -> { amendment? && should_validate? }
  validates :total_amount, numericality: { greater_than_or_equal_to: 0,
                                           less_than_or_equal_to: 2_000_000_000 },
                           if: :should_validate?
  validates :due_date,
            date: { after_or_equal_to: :invoice_date, allow_blank: false },
            if: :should_validate?
  validate :amounts_consistency, unless: :draft?
  validate :positions_amounts_consistency, if: :should_validate?
  validate :cannot_be_modified, unless: -> { draft? || pending? }, on: :update
  validates :associated_advance_invoice_id, presence: true,
                                            if: -> { should_validate? && advance_accounting? }
  validates :total_order_amount, presence: true, if: -> { should_validate? && advance? }
  validates :total_order_amount, numericality: { greater_than: 0 },
                                 if: -> { should_validate? && advance? }
  validate :project_status, if: :should_validate?
  validate :draft_already_exists, on: :create
  validate :invoice_date_last_in_month, if: -> { invoice_date && should_validate? }
  validates :kind, inclusion: { in: ->(invoice) { invoice.payment.allowed_invoice_kinds } }, if: :payment
  validates_associated_bubbling :client, if: -> { should_validate? && !client_address }
  validates_associated_bubbling :client_address, if: -> { should_validate? && client_address }

  validates :attachments, presence: true, if: lambda {
    should_validate? && !no_attachment? && !force_no_attachment
  }

  validates :required_attachments, presence: true, if: lambda {
    attachments_required? && should_validate?
  }

  before_save :remove_correction_reason, unless: :amendment?
  before_save :remove_required_attachments, unless: :attachments_required?

  before_create :copy_attachments, if: :amendment?

  after_save :create_allocation_version, if: :save_allocation_version
  after_save_commit :recalculate_payments, if: lambda {
    (save_allocation_version || saved_change_to_state?) && (pending? || accepted? || issued?)
  }

  enum state: { draft: 0, issued: 1, pending: 2, rejected: 3, accepted: 4 }
  enum kind: { vat: 0, re_invoice: 1, advance: 2, advance_accounting: 3, accounting_note: 4, vat_barter: 5 }

  default_scope -> { where.not(state: :rejected) }

  scope :of_cycle, lambda { |payment|
    joins(:payment).where(
      'payments.id = :originator_id OR payments.originator_id = :originator_id',
      originator_id: payment.originator_id || payment.id
    )
  }

  scope :allocation_changed_since, lambda { |time|
    eager_load(allocation_versions: { mpk_position_versions: :mpk_number })
      .includes(mpk_positions: :mpk_number)
      .joins('INNER JOIN allocation_versions av ON av.invoice_id = invoices.id')
      .where('av.original = ? AND av.created_at >= ?', false, time)
      .where('allocation_versions.created_at < ?', time)
      .order('allocation_versions.created_at DESC')
  }

  scope :of_company, lambda { |company|
    joins(payment: { payment_schedule: :project }).where(projects: { company_id: company.id })
  }

  scope :accepted_or_issued, -> { where state: [1, 4] }

  scope :advance_issued_for, lambda { |client_id|
    joins(payment: { payment_schedule: :project }).issued.advance
                                                  .where(projects: { client_id: client_id })
  }

  scope :live, -> { where(state: [1, 2, 4]) }

  attr_accessor :force_no_attachment, :save_allocation_version, :allow_update, :action_name, :current_user

  aasm column: :state, enum: true, timestamps: true do
    state :draft, initial: true
    state :accepted, before_enter: %i[generate_number copy_address_info copy_bank_account]
    state :issued
    state :pending, :rejected

    event :send_to_controller do
      transitions from: %i[draft pending], to: :pending
    end

    event :accept, after: proc { |user| create_snapshot(user, :accept) },
                   before: proc { |user| @current_user = user } do
      transitions from: :pending, to: :accepted,
                  success: %i[notify_issued create_allocation_version]
    end

    event :issue, after: proc { |user| create_snapshot(user, :issue) },
                  before: proc { |user| @current_user = user } do
      transitions from: :accepted, to: :issued, guard: :paper_invoice_sending_method?
      transitions from: :accepted, to: :issued, guards: %i[electronic_invoice_sending_method? invoice_document],
                  success: :send_email_to_client
    end

    event :reject, after: proc { |user| create_snapshot(user, :reject) } do
      transitions from: :pending, to: :rejected
    end

    event :back_to_pending, before: :remove_number, after: proc { |user| create_snapshot(user, :pending) } do
      transitions from: %i[accepted issued], to: :pending
    end
  end

  has_snapshot_children do
    invoice = self.class.unscoped.includes(:invoice_positions, :mpk_positions, :attachments, :required_attachments).find(id)

    {
      invoice_positions: invoice.invoice_positions,
      mpk_positions: invoice.mpk_positions,
      attachments: invoice.attachments,
      required_attachments: invoice.required_attachments,
      invoice_document: invoice.invoice_document
    }
  end

  def project
    payment.try(:payment_schedule).try(:project)
  end

  def client
    project.try(:client)
  end

  def amends
    @amends ||= begin
      invoices = payment.invoices.accepted_or_issued
      invoices = invoices.where('invoices.created_at < ?', created_at) unless new_record?
      invoices.last
    end
  end

  def amendment?
    return false unless payment

    amends.present?
  end

  def create_snapshot(user, action, initial_state: nil, comment: nil)
    return if draft? || !persisted?

    create_snapshot!(identifier: snapshot_timestamp(action),
                     user: user,
                     metadata: {
                       action: action,
                       comment: comment,
                       state_was: initial_state || state_before_last_save || state_was
                     })
  end

  def title
    super.presence || invoice_positions.map(&:name).join(',')
  end

  def gross_value
    invoice_positions.to_a.sum(&:gross_value).round(2)
  end

  def vat_value
    invoice_positions.to_a.sum(&:vat_value).round(2)
  end

  def no_tax_kind?
    accounting_note?
  end

  def file_name_base
    number.tr('/', '_') + "_#{project.company.name}"
  end

  def attachments_required?
    super.nil? ? project.invoice_attachments_required : super
  end

  def attachment_required?(attachment)
    required ||= required_attachments

    required.detect { |r| r.attachment_id == attachment.id }
  end

  %i[electronic paper].each do |sending_method|
    method_name = "#{sending_method}_invoice_sending_method?"
    define_method(method_name) do
      if invoice_sending_method_fallback_to_client?
        client.public_send(method_name)
      else
        client_address.public_send(method_name)
      end
    end
  end

  def invoice_sending_method_fallback_to_client?
    !client_address || client_address.fallback_invoice_sending_method?
  end

  private

  def recalculate_payments
    if issued?
      recalculate_completed_payments
    elsif !(payment.current_invoice&.issued? || (payment.current_invoice&.accepted? && pending?))
      recalculate_pending_payments
    end
  end

  def recalculate_completed_payments
    payment.completed_payments.destroy_all
    payment.pending_payments.destroy_all

    group_mpk_positions do |project_id, mpk_number_id, amount|
      payment.completed_payments.create!(payment_attributes(project_id, mpk_number_id, amount))
    end
  end

  def recalculate_pending_payments
    payment.completed_payments.destroy_all
    payment.pending_payments.destroy_all

    group_mpk_positions do |project_id, mpk_number_id, amount|
      payment.pending_payments.create!(payment_attributes(project_id, mpk_number_id, amount))
    end
  end

  def group_mpk_positions
    grouped_mpk_positions = mpk_positions.group_by do |mpk_position|
      [mpk_position.project_id || payment.payment_schedule.project_id, mpk_position.mpk_number_id]
    end

    grouped_mpk_positions.each do |(project_id, mpk_number_id), positions|
      yield(project_id, mpk_number_id, positions.sum(&:amount))
    end
  end

  def payment_attributes(project_id, mpk_number_id, amount)
    {
      amount: amount,
      project_id:,
      mpk_number_id:,
      issued_on: payment.issued_on,
      currency: payment.currency,
      invoice: self
    }
  end

  def copy_bank_account
    account = bank_account || project.bank_account || BankAccount.find_default(project.company)
    return unless account

    self.bank_account_number = account.account_number
  end

  def copy_address_info
    address = client_address || project.client
    self.client_name = address.name
    %i[street additional_address city postcode country vat_number street_number
       apartment post voivodeship district community].each do |attribute|
      self[attribute] = address[attribute]
    end
  end

  def should_validate?
    (pending? && !back_to_pending_action?) || (accepted? && state_changed?)
  end

  def back_to_pending_action?
    action_name == 'back_to_pending'
  end

  def actual_amendment
    return unless equals?(amends)
    return if invoice_positions.map.with_index do |invoice_position, index|
      amended_position = amends.invoice_positions[index]
      return false unless amended_position

      !invoice_position.equals?(amended_position)
    end.any?

    errors.add(:base, :equal_to_parent)
  end

  def equals?(invoice)
    attribute_names = %i[sell_date due_date invoice_date revenue_account_id payment_id receiver_name street
                         additional_address city postcode country total_amount user_id client_name street_number
                         apartment post voivodeship district community no_attachment client_address_id
                         vat_number kind associated_advance_invoice_id total_order_amount bank_account_id
                         bank_account_number attachments_required]

    slice(*attribute_names) == invoice.slice(*attribute_names)
  end

  def filter_essential_attributes(invoice_attributes)
    invoice_attributes.reject do |key, _value|
      key.in?(%w[id correction_reason created_at updated_at description])
    end
  end

  def create_allocation_version
    return unless accepted? || issued?

    allocation_versions.create!(
      original: saved_change_to_attribute?(:state),
      mpk_position_versions_attributes: mpk_positions.map do |mpk_position|
        {
          mpk_number_id: mpk_position.mpk_number_id,
          amount: mpk_position.amount
        }
      end
    )
  end

  def invoice_date_last_in_month
    invoice = last_invoice_in_month
    return unless invoice

    errors.add(:invoice_date, :must_be_greater_or_equal_than, date: invoice.invoice_date)
  end

  def last_invoice_in_month
    same_amendment_issued_scope.joins(payment: { payment_schedule: :project })
                               .where(payments: { currency: payment.currency })
                               .where(projects: { company_id: project.company_id })
                               .where('invoices.invoice_date > ?', invoice_date)
                               .where('invoices.invoice_date <= ?', invoice_date.end_of_month)
                               .order(invoice_date: :desc).first
  end

  def same_amendment_issued_scope
    scope = Invoice.accepted_or_issued
    amendment_condition = { correction_reason: nil }
    amendment? ? scope.where.not(amendment_condition) : scope.where(amendment_condition)
  end

  def draft_already_exists
    return unless payment
    return unless payment.invoices.draft.any?

    errors.add(:base, :draft_exists)
  end

  def notify_issued
    InvoicesWorker.perform_in(1.minute, id)
  end

  def cannot_be_modified
    return if can_be_modified?

    errors.add(:base, :issued_cannot_be_modified)
  end

  def can_be_modified?
    state_changed? || required_attachments.any?(&:changed?) || trivial_changes? || allow_update
  end

  def trivial_changes?
    (changes.keys - %w[attachments_required no_attachment]).empty? && invoice_positions.map(&:changes).all?(&:empty?)
  end

  def remove_correction_reason
    self.correction_reason = nil
  end

  def remove_number
    self.number = nil
  end

  def remove_required_attachments
    self.required_attachments = []
  end

  def amounts_consistency
    return if total_amount == mpk_positions.to_a.select(&:amount).reject(&:marked_for_destruction?)
                                           .sum(&:amount)

    errors.add(:total_amount, :not_consistent_with_mpk_positions)
  end

  def positions_amounts_consistency
    return if total_amount == (invoice_positions.to_a.select(&:net_value)
                                                .reject(&:marked_for_destruction?)
                                                .sum(&:net_value) || 0) * 100

    errors.add(:total_amount, :not_consistent_with_invoice_positions)
  end

  def project_status
    return if project.try(:active?)

    errors.add(:base, :project_is_inactive)
  end

  def generate_number
    year = invoice_date.strftime('%Y')
    month = invoice_date.strftime('%m')
    index = (last_index(month, year) || 0) + 1
    self.number = number_template(index, month, year)
  end

  def last_index(month, year) # rubocop:disable Metrics/CyclomaticComplexity,Metrics/PerceivedComplexity,Metrics/MethodLength,Metrics/AbcSize
    regex = if accounting_note?
              "^(\\d+)\\/#{month}\\/#{year}$"
            elsif amendment? && foreign?
              "^FKE[-\\/](\\d+)\\/#{month}\\/#{year}\\/RSEK$"
            elsif amendment? && !foreign?
              "^FSK[-\\/](\\d+)\\/#{month}\\/#{year}\\/RSK$"
            elsif !amendment? && foreign?
              "^FSE[-\\/](\\d+)\\/#{month}\\/#{year}\\/RSE$"
            elsif re_invoice?
              "^FS[-\\/](\\d+)\\/#{month}\\/#{year}\\/RSR$"
            elsif advance? || advance_accounting?
              "^FSL[-\\/](\\d+)\\/#{month}\\/#{year}\\/RSZ$"
            else
              "^FS[-\\/](\\d+)\\/#{month}\\/#{year}\\/RS$"
            end

    Invoice.of_company(project.company)
           .where('invoices.number REGEXP ?', regex)
           .pluck(:number)
           .map { |number| number.match(/#{regex}/)[1].to_i }
           .max
  end

  # rubocop:disable Metrics/CyclomaticComplexity,Metrics/PerceivedComplexity,Metrics/MethodLength,Metrics/AbcSize
  def number_template(order_number, month, year)
    if accounting_note?
      "#{order_number}/#{month}/#{year}"
    elsif amendment? && foreign?
      "FKE/#{order_number}/#{month}/#{year}/RSEK"
    elsif amendment? && !foreign?
      "FSK/#{order_number}/#{month}/#{year}/RSK"
    elsif !amendment? && foreign?
      "FSE/#{order_number}/#{month}/#{year}/RSE"
    elsif re_invoice?
      "FS/#{order_number}/#{month}/#{year}/RSR"
    elsif advance? || advance_accounting?
      "FSL/#{order_number}/#{month}/#{year}/RSZ"
    else
      "FS/#{order_number}/#{month}/#{year}/RS"
    end
  end
  # rubocop:enable Metrics/CyclomaticComplexity,Metrics/PerceivedComplexity,Metrics/MethodLength,Metrics/AbcSize

  def foreign?
    !(client_address || project.client).country.downcase.in?(%w[poland polska])
  end

  def copy_attachments
    attachment_copies_map = {}

    self.attachments = attachments.map do |copied_attachment|
      next copied_attachment if copied_attachment.new_record?

      new_attachment = Attachment.new
      attacher = new_attachment.file_attacher
      attacher.set(attacher.upload(copied_attachment.file_attacher.file))
      attachment_copies_map[copied_attachment.id] = new_attachment
    end

    copy_required_attachments(attachment_copies_map)
  end

  def copy_required_attachments(attachment_copies_map)
    required_attachments.each do |required_attachment|
      next if required_attachment.attachment.new_record?

      required_attachment.attachment = attachment_copies_map[required_attachment.attachment_id]
    end
  end

  def send_email_to_client
    InvoiceSendingWorker.perform_async(id, current_user&.id)
  end
end
