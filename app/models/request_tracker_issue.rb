class RequestTrackerIssue
  include ActiveModel::Validations
  include ActiveRecord::AttributeAssignment

  attr_accessor :title, :cc, :project_id, :content, :attachments_info

  validates :title, presence: true
  validates :project_id, presence: true
  validates :content, presence: true
  validate :emails_in_cc_should_be_valid_emails

  def initialize(params = {})
    assign_attributes(params)
  end

  def emails_in_cc_should_be_valid_emails
    emails = cc.split(',').map(&:strip)
    emails.each do |mail|
      next if mail =~ Devise.email_regexp
      errors.add(:cc, :format)
      break
    end
  end
end
