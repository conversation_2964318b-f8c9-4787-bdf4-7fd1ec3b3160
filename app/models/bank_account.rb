class BankAccount < ApplicationRecord
  extend BankAccountable

  belongs_to :company

  validates :company_id, :name, :account_number, :currency, :bank_name, presence: true

  has_paper_trail on: %i[create update destroy]

  after_save :mark_other_accounts_not_default, if: :default?
  ibanize :account_number

  enum currency: CURRENCIES

  def self.find_default(company)
    find_by(company: company, default: true)
  end

  private

  def mark_other_accounts_not_default
    BankAccount.where.not(id: id)
               .where(company_id: company_id)
               .update_all(default: false) # rubocop:disable Rails/SkipsModelValidations
  end
end
