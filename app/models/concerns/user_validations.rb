module UserValidations
  extend ActiveSupport::Concern

  MAX_REMOTE_YEARLY_LIMIT = 24

  included do
    validates :username, presence: true,
                          uniqueness: { case_sensitive: false },
                          if: proc { |record| record.first_name.present? && record.last_name.present? }

    validates :username, length: { maximum: 60 }, if: lambda { |record|
      record.username_changed? && record.first_name.present? && record.last_name.present?
    }
    validates :first_name, length: { maximum: 30 }, if: :first_name_changed?
    validates :last_name, length: { maximum: 30 }, if: :last_name_changed?
    validates :username, format: { with: /\A[a-z0-9_\-@\.\+]*\z/i }
    validates :username, length: { maximum: 60 }
    validates :redmine_id, uniqueness: true, allow_nil: true
    validates :first_name, :last_name, presence: true
    validates :activates_on, presence: true, if: :company_id
    validates :email, presence: true, unless: :company_id
    validates :company_id, presence: true, unless: :email
    validates :remote_yearly_limit, presence: true, if: :remote_allowed?
    validates :remote_yearly_limit,
              numericality: { greater_than: 0,
                              less_than_or_equal_to: MAX_REMOTE_YEARLY_LIMIT },
              allow_nil: true

    # validate change, because email_changed? shows false (devise token auth hardcodes it to false!)
    validates :email, uniqueness: { scope: :provider, case_sensitive: false, allow_blank: true },
                      if: -> { provider == 'email' }

    validates :email, length: { maximum: 320 }, allow_blank: true

    validate :email_ascii
    validate :activates_on_not_in_the_past, if: -> { activates_on_changed? && activates_on }
    validate :activates_on_change
    validate :remote_allowed_only_for_coe
  end

  private

  def email_ascii
    return if email.blank? || email.ascii_only?
    errors.add(:email, :ascii_only)
  end

  def activates_on_not_in_the_past
    return if activates_on >= Time.zone.today

    errors.add(:activates_on, :cannot_be_in_the_past)
  end

  def activates_on_change
    return if new_record? || !activates_on_changed?
    return if !activates_on_was || activates_on_was > Time.zone.today
    errors.add(:activates_on, :cannot_be_changed_for_active_user)
  end

  def remote_allowed_only_for_coe
    return if contract_of_employment? || !remote_allowed?

    errors.add(:remote_allowed, :only_for_coe)
  end
end
