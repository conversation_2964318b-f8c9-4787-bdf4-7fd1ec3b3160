# https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#parameterObject
module Documentation
  module DepartmentModel
    include Swagger::Blocks
    extend ActiveSupport::Concern

    included do
      swagger_schema :DepartmentResponses do
        property :DepartmentResponse do
          key :'$ref', :DepartmentResponse
        end
      end
      swagger_schema :DepartmentResponse do
        key :required, [:name, :chief_id, :chief_full_name] + [:id, :created_at, :updated_at, :url, :cache_ts]
        key :id, :Department
        property :id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :created_at do
          key :type, :string
          key :maximum, 255
        end
        property :updated_at do
          key :type, :string
          key :maximum, 255
        end
        property :name do
          key :type, :string
          key :maximum, 255
        end
        property :company_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :chief_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :chief_full_name do
          key :type, :string
          key :maximum, 255
        end
        property :substitute_chief_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :substitute_chief_full_name do
          key :type, :string
          key :maximum, 255
        end
        property :uber_chief_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :uber_chief_full_name do
          key :type, :string
          key :maximum, 255
        end
        property :locked do
          key :type, :boolean
          key :default, false
        end
        property :url do
          key :type, :string
          key :maximum, 255
        end
        property :cache_ts do
          key :type, :string
          key :maximum, 255
        end
      end
      swagger_schema :DepartmentResponseCollectionForSelect do
        key :required, [:name, :company_name] + [:id, :created_at, :updated_at, :url, :cache_ts]
        key :id, :Department
        property :id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :updated_at do
          key :type, :string
          key :maximum, 255
        end
        property :name do
          key :type, :string
          key :maximum, 255
        end
        property :company_name do
          key :type, :string
          key :maximum, 255
        end
        property :cache_ts do
          key :type, :string
          key :maximum, 255
        end
      end
      swagger_schema :DepartmentRequest do
        key :required, [:name, :chief_id]
        property :department do
          property :name do
            key :type, :string
            key :maximum, 255
          end
          property :company_id do
            key :type, :integer
            key :format, :int32
            key :maximum, 2_147_483_647
          end
          property :locked do
            key :type, :boolean
            key :default, false
          end
          property :chief_id do
            key :type, :integer
            key :format, :int32
            key :maximum, 2_147_483_647
          end
          property :substitute_chief_id do
            key :type, :integer
            key :format, :int32
            key :maximum, 2_147_483_647
          end
        end
      end
    end
  end
end
