# https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#parameterObject
module Documentation
  module ProjectModel
    include Swagger::Blocks
    extend ActiveSupport::Concern

    included do
      swagger_schema :ProjectResponses do
        property :ProjectResponse do
          key :'$ref', :ProjectResponse
        end
        property :ProjectResponseCollectionForSelect do
          key :'$ref', :ProjectResponseCollectionForSelect
        end
      end
      swagger_schema :ProjectResponse do
        key :required, [:name, :accounting_number_id, :identifier, :company_id] + [:id, :created_at, :updated_at, :url, :cache_ts]
        key :id, :Project
        property :id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :description do
          key :type, :string
          key :maximum, 65_535
        end
        property :parent_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :inherit_members do
          key :type, :boolean
          key :default, true
        end
        property :public do
          key :type, :boolean
          key :default, false
        end
        property :company_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :status do
          key :type, :string
          key :enum, %w(active closed archived)
          key :default, 'active'
        end
        property :owncloud do
          key :type, :boolean
          key :default, false
        end
        property :uid_number do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :lft do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :rgt do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :created_at do
          key :type, :string
          key :maximum, 255
        end
        property :updated_at do
          key :type, :string
          key :maximum, 255
        end
        property :name do
          key :type, :string
          key :maximum, 255
        end
        property :identifier do
          key :type, :string
          key :maximum, 255
        end
        property :accounting_number_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :homepage do
          key :type, :string
          key :maximum, 255
        end
        property :company_name do
          key :type, :string
          key :maximum, 255
        end
        property :parent_name do
          key :type, :string
          key :maximum, 255
        end
        property :subprojects do
          key :type, :array
          items do
            property :id do
              key :type, :integer
              key :format, :int32
              key :maximum, 2_147_483_647
            end
            property :name do
              key :type, :string
              key :maximum, 255
            end
            property :identifier do
              key :type, :string
              key :maximum, 255
            end
          end
        end
        property :subprojects_count do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :ancestors do
          key :type, :array
          items do
            property :id do
              key :type, :integer
              key :format, :int32
              key :maximum, 2_147_483_647
            end
            property :name do
              key :type, :string
              key :maximum, 255
            end
            property :identifier do
              key :type, :string
              key :maximum, 255
            end
          end
        end
        property :ancestors_count do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :url do
          key :type, :string
          key :maximum, 255
        end
        property :cache_ts do
          key :type, :string
          key :maximum, 255
        end
        # property :permissions_for_current_user do
        #   key :type, :array
        #   key :description, '"add_subprojects", "edit_project", "close_project", "manage_members"'
        #   items do
        #     key :type, :string
        #   end
        # end
        property :actions_for_current_user do
          key :type, :object
          key :description, 'authoritative action permissions'
          (%w(add_subprojects edit_project close_project manage_members) + %w( index show create update destroy copy_source copy archive unarchive close reopen )).each do |action|
            property action.to_sym do
              key :type, :boolean
              key :default, false
            end
          end
        end
      end
      swagger_schema :ProjectResponseCollectionForSelect do
        key :required, [:name, :accounting_number_id, :identifier, :company_id] + [:id, :created_at, :updated_at, :url, :cache_ts]
        key :id, :Project
        property :id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :parent_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :company_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :updated_at do
          key :type, :string
          key :maximum, 255
        end
        property :name do
          key :type, :string
          key :maximum, 255
        end
        property :identifier do
          key :type, :string
          key :maximum, 255
        end
        property :cache_ts do
          key :type, :string
          key :maximum, 255
        end
      end
      swagger_schema :ProjectRequest do
        key :required, [:name, :accounting_number_id, :identifier, :company_id]
        property :project do
          property :name do
            key :type, :string
            key :maximum, 255
          end
          property :description do
            key :type, :string
            key :maximum, 65_535
          end
          property :identifier do
            key :type, :string
            key :maximum, 255
          end
          property :accounting_number_id do
            key :type, :integer
            key :format, :int32
            key :maximum, 2_147_483_647
          end
          property :parent_id do
            key :type, :integer
            key :format, :int32
            key :maximum, 2_147_483_647
          end
          property :company_id do
            key :type, :integer
            key :format, :int32
            key :maximum, 2_147_483_647
          end
          property :uid_number do
            key :type, :integer
            key :format, :int32
            key :maximum, 2_147_483_647
          end
          property :homepage do
            key :type, :string
            key :maximum, 255
          end
          property :public do
            key :type, :boolean
            key :default, false
          end
          property :inherit_members do
            key :type, :boolean
            key :default, true
          end
          property :owncloud do
            key :type, :boolean
            key :default, false
          end
          property :status do
            key :type, :string
            key :enum, %w(active closed archived)
            key :default, 'active'
          end
          # property :permissions_for_current_user do
          #   key :type, :array
          #   key :description, '"add_subprojects", "edit_project", "close_project", "manage_members"'
          #   items do
          #     key :type, :string
          #   end
          # end
          property :actions_for_current_user do
            key :type, :object
            key :description, 'authoritative action permissions'
            (["add_subprojects", "edit_project", "close_project", "manage_members"] + %w( index show create update destroy copy_source copy archive unarchive close reopen )).each do |action|
              property action.to_sym do
                key :type, :boolean
                key :default, false
              end
            end
          end
        end
      end
      swagger_schema :ProjectCopy do
        key :required, [:name, :accounting_number_id, :identifier]
        property :project do
          property :name do
            key :type, :string
            key :maximum, 255
          end
          property :identifier do
            key :type, :string
            key :maximum, 255
          end
          property :accounting_number_id do
            key :type, :integer
            key :format, :int32
            key :maximum, 2_147_483_647
          end
        end
        property :only do
          key :type, :array
          items do
            key :type, :string
          end
        end
        property :enabled_modules do
          key :type, :array
          items do
            key :type, :string
          end
        end
        property :trackers do
          key :type, :array
          items do
            key :type, :string
          end
        end
        property :issue_custom_fields do
          key :type, :array
          items do
            key :type, :string
          end
        end
        property :custom_values do
          key :type, :array
          items do
            key :type, :string
          end
        end
      end
    end
  end
end
