# https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#parameterObject
module Documentation
  module Pages
    module NavbarModel
      include Swagger::Blocks
      extend ActiveSupport::Concern
      included do
        swagger_schema :PagesNavbarResponse do
          property :is_global_programmer do
            key :type, :boolean
            key :description, "current user is allowed to do anything"
          end
          property :is_global_admin do
            key :type, :boolean
            key :description, "current user is allowed to send extra parameters on `users:create` \
                                or `users:update`: `state`, `global_role_ids` (may be trimmed), `company_id`"
          end
          property :is_global_admin_or_privileged_user do
            key :type, :boolean
            key :description, "current user is allowed to send extra parameters on `users:create` \
                                or `users:update` (if these activities are allowed): `state`, `global_role_ids` (may be trimmed)"
          end
          property :is_global_admin_or_hr_user do
            key :type, :boolean
            key :description, "current user is allowed to send extra parameters on `users:create` \
                                or `users:update` (if these activities are allowed): `state`, `company_id`"
          end
          property :my_menu do
            key :type, :object
            key :description, 'current user is allowed to see the following tree as menu items'
          end
          property :my_preferences do
            key :type, :object
            key :description, 'current user\'s preferences'
          end
          property :my_abilities do
            key :type, :object
            key :description, 'current user permissions'
            property :id do
              key :type, :integer
              key :format, :int32
              key :maximum, 2_147_483_647
            end
            property :my_genre do
              key :type, :string
              key :description, "current user\'s authentication scope, e.g. user"
            end
            property :my_roles do
              key :type, :object
              key :description, 'current user\'s global roles perdefined: `true` - has the role, `false` - does not have it'
            end
            property :my_access do
              key :type, :object
              key :description, 'current user\'s API permissions'
            end
            property :my_menu do
              key :type, :object
              key :description, 'current user\'s menu'
            end
          end
          property :front_revision do
            key :type, :string
          end
          property :api_revision do
            key :type, :string
          end
          property :immediate_action do
            key :type, :string
          end
          property :public do
            key :type, :boolean
            key :description, 'Is current user accessing the public instance of the app?'
          end
          property :me do
            key :type, :object
            key :description, 'current user\'s identity'
            property :id do
              key :type, :integer
              key :format, :int32
              key :maximum, 2_147_483_647
            end
            property :username do
              key :type, :string
            end
          end
        end
      end
    end
  end
end
