# https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#parameterObject
module Documentation
  module Pages
    module ProfileModel
      include Swagger::Blocks
      extend ActiveSupport::Concern

      include Documentation::UserModel

      included do
        swagger_schema :PagesProfileResponse do
          property :is_global_admin do
            key :type, :boolean
            key :description, "current user is allowed to send parameters on `users:create` \
                                or `users:update`: `state`, `global_role_ids`, `group_ids`"
          end
          property :has_owncloud_password do
            key :type, :boolean
          end
          property :has_dev_password do
            key :type, :boolean
          end
          allOf do
            schema do
              key :'$ref', :UserResponse
            end
          end
        end
        swagger_schema :PagesProfileRequest do
          key :required, [:first_name, :last_name]
          property :profile do
            property :current_password do
              key :name, :password
              key :type, :string
              key :format, :password
              key :in, :formData
            end
            property :first_name do
              key :type, :string
              key :maximum, 255
            end
            property :last_name do
              key :type, :string
              key :maximum, 255
            end
            property :email do
              key :type, :string
              key :maximum, 255
              key :description, 'editable unless record has `company_id`'
            end
            property :password do
              key :name, :password
              key :type, :string
              key :format, :password
              key :in, :formData
            end
            property :password_confirmation do
              key :name, :password_confirmation
              key :type, :string
              key :format, :password
              key :in, :formData
            end
            property :owncloud_password do
              key :name, :password_confirmation
              key :type, :string
              key :format, :password
              key :in, :formData
              key :description, "can\'t be an empty string"
            end
            property :delete_owncloud_password do
              key :type, :boolean
              key :description, 'set to true to delete current owncloud password'
              key :default, false
            end
            property :dev_password do
              key :name, :password_confirmation
              key :type, :string
              key :format, :password
              key :in, :formData
            end
            property :delete_dev_password do
              key :type, :boolean
              key :description, 'set to true to delete current dev password'
              key :default, false
            end
          end
        end
        swagger_schema :PagesProfilePreferences do
          key :required, [:preferences]
          property :preferences
        end
      end
    end
  end
end
