# encoding: UTF-8
# https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#parameterObject
module Documentation
  module HolidayRequestModel
    include Swagger::Blocks
    extend ActiveSupport::Concern

    included do
      swagger_schema :UsersResponse do
        property :UserResponseCollectionForSelect do
          key :'$ref', :UserResponseCollectionForSelect
        end
        property :holiday_requests do
          key :type, :array
          key :description, 'filtered holiday requests'
          items do
            property :HolidayRequestResponse do
              key :'$ref', :HolidayRequestResponse
            end
          end
        end
      end
      swagger_schema :HolidayRequestResponses do
        property :HolidayRequestResponse do
          key :'$ref', :HolidayRequestResponse
        end
        property :HolidayRequestResponseCollectionForSelect do
          key :'$ref', :HolidayRequestResponseCollectionForSelect
        end
      end
      swagger_schema :DepartmentOptionsResponse do
        property :DepartmentResponseCollectionForSelect do
          key :'$ref', :DepartmentResponseCollectionForSelect
        end
      end
      swagger_schema :ProjectOptionsResponse do
        property :ProjectResponseCollectionForSelect do
          key :'$ref', :ProjectResponseCollectionForSelect
        end
      end
      swagger_schema :UserOptionsResponse do
        property :UserResponseCollectionForSelect do
          key :'$ref', :UserResponseCollectionForSelect
        end
        property :department_names do
          key :type, :array
          key :description, 'names of departments'
          items do
            key :type, :string
          end
        end
      end
      swagger_schema :HolidayRequestResponse do
        key :required, [:applicant_id, :category, :starts_on, :ends_on, :absences_count, :actions_for_current_user, :status] + [:id, :created_at, :updated_at, :url, :cache_ts]
        key :id, :HolidayRequest
        property :id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :applicant_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :examiner_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
          key :description, 'the last person who accepted/rejected the request'
        end
        property :category do
          key :type, :string
          key :enum, ['Niedostępność', 'Niedostępność/Ch', 'Niedostępność/O', 'Niedostępność/Ż', 'Niedostępność/B']
        end
        property :visible do
          key :type, :boolean
          key :default, false
        end
        property :confirmed do
          key :type, :boolean
          key :default, false
          key :description, 'leave was confirmed, e.g. user provided L4 for his `Niedostępność/Ch` leave'
        end
        property :applicant_comment do
          key :type, :string
        end
        property :examiner_comment do
          key :type, :string
          key :description, 'pending comment text'
        end
        property :examiner_comment_history do
          key :type, :array
          key :description, 'examiner_comment history, visible only on `show` action'
          items do
            key :type, :string
          end
        end
        property :starts_on do
          key :type, :string
          key :format, :date
        end
        property :ends_on do
          key :type, :string
          key :format, :date
        end
        property :accepted_at do
          key :type, :string
          key :format, :'date-time'
        end
        property :rejected_at do
          key :type, :string
          key :format, :'date-time'
        end
        property :created_at do
          key :type, :string
          key :format, :'date-time'
        end
        property :updated_at do
          key :type, :string
          key :format, :'date-time'
        end
        property :url do
          key :type, :string
          key :maximum, 255
        end
        property :cache_ts do
          key :type, :string
          key :maximum, 255
        end
        property :overuse_of_holidays do
          key :type, :boolean
          key :default, false
          key :description, 'key is present only if actions_for_current_user[\'overuse_of_holidays\'] == true'
        end
        property :problems do
          key :type, :array
          key :description, 'sentences to be dispalyed in several lines at the top of the page'
          items do
            key :type, :string
          end
        end
        property :absences_count do
          key :type, :integer
        end
        property :actions_for_current_user do
          key :type, :object
          key :description, 'authoritative action permissions'
          (%w(accept reject convert_to confirmed) + %w(index show create update destroy)).each do |action|
            property action.to_sym do
              key :type, :boolean
              key :default, false
            end
          end
        end
        property :file_name do
          key :type, :string
          key :maximum, 255
          key :description, 'present only if record is of a confirmable category, visible only for `global_admin` or `hr_user`'
        end
        property :file_url do
          key :type, :string
          key :maximum, 255
          key :description, 'present only if record is of a confirmable category, visible only for `global_admin` or `hr_user`'
        end
        property :status do
          key :type, :string
          key :enum, %w(Accepted Rejected Pending)
        end
        property :applicant_name do
          key :type, :string
          key :maximum, 255
        end
        property :examiner_name do
          key :type, :string
          key :maximum, 255
        end
      end
      swagger_schema :HolidayRequestResponseCollectionForSelect do
        key :required, [:applicant_id, :category, :starts_on, :ends_on] + [:id, :created_at, :updated_at, :url, :cache_ts]
        key :id, :HolidayRequest
        property :id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :applicant_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :examiner_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
          key :description, 'the last person who accepted/rejected the request'
        end
        property :visible do
          key :type, :boolean
          key :default, false
        end
        property :confirmed do
          key :type, :boolean
          key :default, false
          key :description, 'leave was confirmed, e.g. user provided L4 for his `Niedostępność/Ch` leave'
        end
        property :starts_on do
          key :type, :string
          key :format, :date
        end
        property :ends_on do
          key :type, :string
          key :format, :date
        end
        property :updated_at do
          key :type, :string
          key :format, :'date-time'
        end
        property :url do
          key :type, :string
          key :maximum, 255
        end
        property :cache_ts do
          key :type, :string
          key :maximum, 255
        end
      end
      swagger_schema :HolidayRequestRequest do
        key :required, [:applicant_id, :category, :starts_on, :ends_on]
        property :holiday_request do
          property :category do
            key :type, :string
            key :enum, ['Niedostępność', 'Niedostępność/Ch', 'Niedostępność/O', 'Niedostępność/Ż', 'Niedostępność/B']
            key :description, 'get available options for current_user from /holiday_requests/category_options (e.g. requests with \'Niedostępność/Ż\' are only creatable by examining users)'
          end
          property :applicant_id do
            key :type, :integer
            key :format, :int32
            key :maximum, 2_147_483_647
          end
          property :applicant_comment do
            key :type, :string
          end
          property :examiner_comment do
            key :type, :string
            key :description, 'pending comment text'
          end
          property :starts_on do
            key :type, :string
            key :format, :date
          end
          property :ends_on do
            key :type, :string
            key :format, :date
          end
          property :skip_warning do
            key :type, :boolean
            key :default, false
            key :description, 'skip error on accept if overuse of holidays detected, provide confirm dialog'
          end
          property :accept do
            key :type, :boolean
            key :default, false
          end
          property :reject do
            key :type, :boolean
            key :default, false
          end
          property :confirmed do
            key :type, :boolean
            key :default, false
            key :description, 'leave was confirmed, e.g. user provided L4 for his `Niedostępność/Ch` leave'
          end
          property :convert_to do
            key :type, :string
            key :enum, ['Niedostępność']
            key :description, 'convert the `category` to a specified value'
          end
          property :remove_file do
            key :type, :boolean
            key :default, false
            key :description, 'remove file'
          end
          property :file do
            key :type, :object
            key :description, 'upload file, available only on `update` action, only if record is of confirmable category, only for `global_admin` or `hr_user`'
            property :base64 do
              key :type, :string
              key :description, 'base64\'s last part (content without meta)'
            end
            property :filetype do
              key :type, :string
              key :description, 'mime type, e.g. \'image/jpeg\''
            end
            property :filename do
              key :type, :string
              key :description, 'file\'s original name, e.g. \'Zaznaczenie_201.jpg\''
            end
            property :filesize do
              key :type, :int64
              key :description, 'file\' size in bytes, e.g. 4619 (optional)'
            end
          end
        end
      end
    end
  end
end
