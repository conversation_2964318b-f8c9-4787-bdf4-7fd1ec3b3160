module Documentation
  module PublicKeyModel
    include Swagger::Blocks
    extend ActiveSupport::Concern

    included do
      swagger_schema :PublicKeyResponses do
        property :PublicKeyResponse do
          key :'$ref', :PublicKeyResponse
        end
      end
      swagger_schema :PublicKeyResponse do
        key :required, [:identifier, :id, :created_at, :updated_at,
                        :fingerprint, :url, :cache_ts]
        key :id, :PublicKey
        property :id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :identifier do
          key :type, :string
          key :maximum, 65_535
        end
        property :fingerprint do
          key :type, :string
          key :maximum, 65_535
        end
        property :created_at do
          key :type, :string
          key :maximum, 255
        end
        property :updated_at do
          key :type, :string
          key :maximum, 255
        end
        property :url do
          key :type, :string
          key :maximum, 255
        end
        property :cache_ts do
          key :type, :string
          key :maximum, 255
        end
      end
      swagger_schema :PublicKey do
        key :reqired, [:identifier, :key]
        property :identifier do
          key :type, :string
        end
        property :key do
          key :type, :string
        end
      end
      swagger_schema :PublicKeyRequest do
        key :required, [:public_key]
        property :public_key do
          key :required, [:identifier, :key]
          key :'$ref', :PublicKey
        end
      end
    end
  end
end
