# https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#parameterObject
module Documentation
  module AbsenceModel
    include Swagger::Blocks
    extend ActiveSupport::Concern
    included do
      swagger_schema :AbsenceResponses do
        property :AbsenceResponse do
          key :'$ref', :AbsenceResponse
        end
        property :AbsenceResponseCollectionForSelect do
          key :'$ref', :AbsenceResponseCollectionForSelect
        end
      end
      swagger_schema :AbsenceResponse do
        key :required, [:name] + [:id, :created_at, :updated_at, :url, :cache_ts]
        key :id, :Absence
        property :id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :user_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :date do
          key :type, :string
          key :maximum, 255
        end
        property :created_at do
          key :type, :string
          key :maximum, 255
        end
        property :updated_at do
          key :type, :string
          key :maximum, 255
        end
        property :cache_ts do
          key :type, :string
          key :maximum, 255
        end
        property :errors do
          key :type, :object
          property :base do
            key :type, :array
            items do
              key :type, :string
            end
          end
        end
      end
      swagger_schema :AbsenceResponseCollectionForSelect do
        key :id, :Absence
        property :id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :user_id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :date do
          key :type, :string
          key :maximum, 255
        end
        property :created_at do
          key :type, :string
          key :maximum, 255
        end
        property :updated_at do
          key :type, :string
          key :maximum, 255
        end
        property :cache_ts do
          key :type, :string
          key :maximum, 255
        end
        property :errors do
          key :type, :object
          property :base do
            key :type, :array
            items do
              key :type, :string
            end
          end
        end
      end
      # swagger_schema :AbsenceRequest do
      #   key :required, [:user_id, :date]
      #   property :absence do
      #     property :user_id do
      #       key :type, :integer
      #       key :format, :int32
      #       key :maximum, 2_147_483_647
      #     end
      #     property :date do
      #       key :type, :string
      #       key :maximum, 10
      #       key :description, 'YYYY-MM-DD'
      #     end
      #   end
      # end
      # swagger_schema :BatchCreateAbsenceRequest do
      #   key :required, [:user_id, :dates]
      #   property :absence do
      #     property :user_id do
      #       key :type, :integer
      #       key :format, :int32
      #       key :maximum, 2_147_483_647
      #     end
      #     property :dates do
      #       key :type, :array
      #       items do
      #         key :type, :string
      #         key :maximum, 10
      #         key :description, 'YYYY-MM-DD'
      #       end
      #     end
      #   end
      # end
      # swagger_schema :BatchUpdateAbsenceRequest do
      #   key :required, [:user_id, :date]
      #   property :absence do
      #     property :user_id do
      #       key :type, :integer
      #       key :format, :int32
      #       key :maximum, 2_147_483_647
      #     end
      #     property :date do
      #       key :type, :string
      #       key :maximum, 10
      #       key :description, 'YYYY-MM-DD'
      #     end
      #   end
      # end
    end
  end
end
