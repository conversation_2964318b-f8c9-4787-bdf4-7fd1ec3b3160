module Documentation
  module MpkNumberModel
    include Swagger::Blocks
    extend ActiveSupport::Concern

    included do
      swagger_schema :MpkNumberResponses do
        property :MpkNumberResponse do
          key :'$ref', :MpkNumberResponse
        end
      end
      swagger_schema :MpkNumberResponse do
        key :required, [:key, :name]
        property :key do
          key :type, :string
          key :maximum, 255
        end
        property :name do
          key :type, :string
          key :maximum, 255
        end
      end
    end
  end
end
