module Documentation
  module RevenueAccountModel
    include Swagger::Blocks
    extend ActiveSupport::Concern

    included do
      swagger_schema :RevenuAccountResponses do
        property :RevenueAccountResponse do
          key :'$ref', :RevenueAccountResponse
        end
      end
      swagger_schema :RevenueAccountResponse do
        key :required, [:key, :name]
        property :key do
          key :type, :string
          key :maximum, 255
        end
        property :name do
          key :type, :string
          key :maximum, 255
        end
      end
    end
  end
end
