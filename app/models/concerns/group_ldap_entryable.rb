module GroupLdapEntryable
  extend ActiveSupport::Concern
  include LdapEntryable

  included do
    before_validation :create_gid_number, if: -> { gid_number.blank? }
  end

  def dn
    "cn=#{cn}"
  end

  def ldap_raw_attributes(_)
    data = {
      objectClass: %w(top posixGroup extensibleObject),
      cn: cn,
      description: name,
      gidNumber: gid_number.to_s
    }

    users.active.each_with_index do |user, i|
      data[:memberUid] = [] if i == 0
      data[:memberUid] << user.username
    end

    data[:info] = %w[active]
    data[:info] << 'serviceChat' if chat?
    # TODO: info: serviceCi

    data
  end

  def should_exist_ldap?
    true
  end

  private

  def create_gid_number
    last_gid_number = [
      settings[:min_group_gid_number].to_i,
      Group.order('gid_number DESC').first.try(:gid_number) || 0
    ].max
    self.gid_number = last_gid_number + 1
  end
end
