module LdapEntryable
  extend ActiveSupport::Concern

  included do
    has_many :ldap_entries, as: :entryable

    after_create :create_ldap_entries

    after_update :update_ldap_entries, unless: proc { |obj|
      obj.is_a?(User) &&
        Set.new(obj.saved_changes.keys) == Set.new(%w[updated_at tokens])
    }
    after_touch :update_ldap_entries

    after_destroy :destroy_ldap_entries
  end

  # each model should implement own ldap_raw_attributes method
  # [['attrName', 'attrVal', uniqIdInNameScope]]
  # third param is optional default to 1
  # eg [
  #   ['objectClass', 'top', 1],
  #   ['objectClass', 'inetOrgPerson', 2],
  #   ['cn', 'Jan', 1],
  #   ['cn', 'Kazimierz', 2],
  #   ['sn', 'Waza'],
  # ]

  private

  def destroy_ldap_entries
    LdapWorker.perform_in(5.seconds, 'destroy_ldap_entries', model_name.name, id)
  end

  def create_ldap_entries
    LdapWorker.perform_in(5.seconds, 'create_ldap_entries', model_name.name, id)
  end

  def update_ldap_entries
    LdapWorker.perform_in(15.seconds, 'update_ldap_entries', model_name.name, id)
  end

  def settings
    Settings.ldap_provider.to_h.with_indifferent_access
  end
end
