module LdapPasswordable
  extend ActiveSupport::Concern

  included do
    validates :root_password, :dev_password, :owncloud_password, password_strength: {
      cracklib: true, # NOTE: accepts 'adminadmin' as a good password!
      regexp: true,
      allow_nil: true,
      allow_blank: false
    }

    serialize :hashed_passwords, Hash

    attr_accessor :owncloud_password, :dev_password, :root_password, :delete_dev_password,
                  :delete_owncloud_password

    before_save :service_password
    before_save :reset_external_passwords, if: :system?
  end

  def service_password
    %w(dev owncloud).each do |password_kind|
      value = send("#{password_kind}_password")
      create_service_password(password_kind, value.to_s) if value
    end
  end

  def get_service_password(service, encryption)
    hashed_passwords[service][encryption]
  end

  def valid_password?(plain_password)
    return false if system?
    return false unless super
    return true if new_record?

    if hashed_passwords['root'].blank?
      create_service_password('root', plain_password)
      save(validate: false)
    end
    true
  end

  def password=(new_password)
    @password = new_password
    return if @password.blank?
    create_service_password('root', new_password)
    self.encrypted_password = password_digest(new_password)
  end

  def token_validation_response
    as_json(except: %i[tokens created_at updated_at hashed_passwords])
  end

  def valid_for_authentication?
    super && active?
  end

  def reset_password_period_valid?
    super && active?
  end

  private

  def create_service_password(service, password)
    self.hashed_passwords ||= {}

    self.hashed_passwords[service] = {
      'ssha256' => generate_ssha256(password),
      'ssha512' => generate_ssha512(password)
    }
  end

  def reset_external_passwords
    self.hashed_passwords = {}
  end

  def generate_ssha256(password)
    salt = SecureRandom.hex(16)
    Base64.strict_encode64(Digest::SHA256.digest(password + salt) + salt)
  end

  def generate_ssha512(password)
    salt = SecureRandom.hex(16)
    Base64.strict_encode64(Digest::SHA512.digest(password + salt) + salt)
  end
end
