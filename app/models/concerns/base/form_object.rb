require 'delegate'
require 'ostruct'
module Base
  module FormObject
    extend ActiveSupport::Concern

    included do
      include ActiveModel::Validations
      include ActiveRecord::Validations
      include ActiveRecordValidationsCompatibility
      include ChangesWithAssociations

      attr_accessor :actor, :policy

      delegate :serializable_hash, to: :object

      validates_presence_of :actor, strict: true
      validates_presence_of :policy, strict: true

      # NOTE: Validations with no :on option will run no matter the context.
      # NOTE: Method must be inside included block in order to patch the original from ActiveModel::Validations.
      # @overload valid?(context = nil)
      def valid?(context = nil)
        context ||= object.new_record? ? :create : :update
        output = super(context)
        object_output = object.valid?(context)
        promote_form_errors(errors)
        object.errors.empty? && output && object_output
      end
    end

    def object
      __getobj__
    end

    def form_action
      @_form_action
    end

    def form_success?
      !!@_form_success
    end

    def persist_object(**save_opts)
      save_opts[:context] ||= object.new_record? ? :create : :update
      @_form_action = save_opts[:context]
      valid?(@_form_action) && object.save(**save_opts) && @_form_success = true
    end

    def destroy_object!(proc_before_destroy = -> {})
      @_form_action = :destroy
      valid?(@_form_action) && proc_before_destroy && object.destroy! && object.destroyed? && @_form_success = true
    end

    def notable_changes
      @_notable_changes ||= what_changed?(object, object.persisted? ? object.previous_changes : object.changes).changes
    end

    private

    def promote_form_errors(local_errors)
      local_errors.each do |error|
        object.errors.add(error.attribute, error.message)
      end
    end
  end
end
