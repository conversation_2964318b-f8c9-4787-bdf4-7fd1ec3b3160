module ExaminerCommentHistoryAppendable
  extend ActiveSupport::Concern

  included do
    attr_accessor :examiner_comment_author
    serialize :examiner_comment_history, Array
    before_save :add_to_examiner_comment_history
  end

  private

  # - examiner_comment does not need to be paired with examiner_id or any changes
  # - examiner_comment_author can be any user or worker or default comment note
  # - examiner_comment_history keeps all the comments concerning the request
  # - should be coupled with accept/reject model changes
  def add_to_examiner_comment_history
    if examiner_comment.present? && examiner_comment_changed?
      self.examiner_comment_history << {
        author: examiner_comment_author,
        date: Time.zone.now.to_date,
        content: examiner_comment
      }
    end
  end
end
