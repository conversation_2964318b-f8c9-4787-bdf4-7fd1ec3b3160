module ActiveJobDatabaseLockingRetriable
  extend ActiveSupport::Concern
  include ActiveJobRetryControllable

  included do
    rescue_from ActiveRecord::StatementInvalid, with: :retry_locking_errors
    rescue_from Mysql2::Error, with: :retry_locking_errors
  end

  private

  # :nocov:
  def retry_locking_errors(exception) # rubocop:disable Metrics/AbcSize
    if locking_error?(exception) && !executions_limit_exceeded?
      raise if Rails.env.test?
      message = "[#{self.class.name}] #{exception.message}, custom retry job in 5 seconds"
      logger.warn(message)
      Sidekiq.logger.warn(message) if self.class.queue_adapter.to_s =~ /Sidekiq/
      # should retry faster than default ~15 sec and not wait in line with ordinary jobs
      # TODO: add it to prioritized queue
      # queue: :high_priority
      retry_job(wait: 5.seconds)
    else
      # sidekiq will retry a job many times increasing interval each time (by default 25 times over 21 days)
      raise
    end
  end

  def locking_error?(exception)
    exception.message =~ /Deadlock found/ || exception.message =~ /Lock wait timeout/
  end
  # :nocov:
end
