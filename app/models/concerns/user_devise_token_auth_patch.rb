require 'bcrypt'

# see DeviseTokenAuth::Concerns::User
module UserDeviseTokenAuthPatch
  extend ActiveSupport::Concern

  included do
    # @overload
    # update user's auth token (should happen on each request)
    # rubocop:disable Metrics/AbcSize
    define_method :create_new_auth_token do |client = nil, impersonator = nil|
      now = Time.zone.now

      token_data = {
        client: client,
        previous_token: tokens.fetch(client, {})['token'],
        last_token: tokens.fetch(client, {})['previous_token'],
        updated_at: now
      }

      impersonator_id = impersonator&.id || (tokens[client] && tokens[client]['impersonator_id'])
      token_data.merge!(impersonator_id: impersonator_id) if impersonator_id
      token = create_token(**token_data)

      # rubocop:disable Rails/SkipsModelValidations
      update_column(:tokens, tokens)
      # rubocop:enable Rails/SkipsModelValidations

      build_auth_headers(token.token, token.client)
    end
    # rubocop:enable Metrics/AbcSize
  end
end
