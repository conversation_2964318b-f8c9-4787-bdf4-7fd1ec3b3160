module UserLdapEntryable # rubocop:disable Metrics/ModuleLength
  extend ActiveSupport::Concern
  include LdapEntryable

  included do
    before_save :generate_uid_number, unless: -> { uid_number.present? }

    after_update :update_ldap_groups, if: -> { saved_change_to_username? }
    after_update :update_ldap_projects, if: -> { saved_change_to_username? }
  end

  def dn
    "uid=#{username}"
  end

  def ldap_raw_attributes(tree)
    data = {
      objectClass: %w(top inetOrgPerson posixAccount person
                      organizational<PERSON>erson sshPublicKey extensibleObject),
      sn: last_name,
      cn: full_name,
      givenName: first_name,
      displayName: full_name,
      uid: username,
      uidNumber: uid_number,
      gidNumber: gid_number,
      homeDirectory: home_path || "/home/<USER>",
      loginShell: active? ? (login_shell || '/bin/bash') : '/bin/false',
      gecos: I18n.transliterate(full_name),
      mail: email,
      info: []
    }

    add_ldap_metadata(tree, data)
    add_email_aliases(data)

    data
  end

  def should_exist_ldap?
    true
  end

  def gid_number
    return super if super.present?
    if groups.where(admin_gid: true).any?
      settings['default_admin_gid']
    else
      settings['default_user_gid']
    end
  end

  def generate_uid_number
    time = created_at || Time.now
    range = time.beginning_of_week(:sunday)..time.end_of_week(:sunday)
    users = User.where(created_at: range).to_a
    100.times do |index|
      uid_candidate = time.strftime("%Y%U#{index.to_s.rjust(2, '0')}")
      unless users.detect { |u| u.uid_number == uid_candidate }
        self.uid_number = uid_candidate
        break
      end
    end
  end

  private

  def add_email_aliases(data)
    return if email_aliases.empty?
    data[:mailAlternateAddress] = []
    email_aliases.each do |email_alias|
      data[:mailAlternateAddress] << email_alias.email
    end
  end

  def add_ldap_metadata(tree, data)
    data[:info] << ldap_user_kind
    if active?
      add_ldap_active_metadata(tree, data)
    else
      data[:info] << 'inactive'
    end
  end

  def add_ldap_active_metadata(tree, data)
    data[:info] << 'active'
    data[:info] << 'serviceCloudshare' if cloud?
    data[:info] << 'serviceDocsCloudshare' if docs_cloud?
    data[:info] << 'serviceChat' if chat?
    data[:info] << 'serviceMonitoring' if monitoring?
    data[:info] << 'serviceSvn' if svn?
    add_job_data(data)
    add_ldap_public_keys(data)
    passwords_for_ldap(tree, data)
  end

  def add_job_data(data)
    return unless company
    data[:organizationName] = company.name
    return unless department
    data[:organizationalUnitName] = department.name
  end

  def ldap_user_kind
    if system?
      'userSystem'
    else
      company_id ? 'userInternal' : 'userExternal'
    end
  end

  def add_ldap_public_keys(data)
    public_keys.each_with_index do |k, i|
      data[:sshPublicKey] = [] if i == 0
      data[:sshPublicKey] << k.key
    end
  end

  def passwords_for_ldap(tree, data)
    password_type = tree['password'] || 'root'
    passwords = hashed_passwords[password_type]
    passwords = hashed_passwords['root'] unless passwords
    return if passwords.blank?
    data[:userPassword] = []
    passwords.each do |encryption_method, value|
      data[:userPassword] <<
        "{#{encryption_method.upcase}}#{value.delete("\n")}"
    end
  end

  def update_ldap_groups
    groups.each { |g| LdapWorker.perform_async('update_ldap_entries', 'Group', g.id) }
  end

  def update_ldap_projects
    Project.of_user(self).each { |p| LdapWorker.perform_async('update_ldap_entries', 'Project', p.id) }
  end
end
