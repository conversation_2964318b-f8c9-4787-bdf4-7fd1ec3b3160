module Userstamping
  extend ActiveSupport::Concern

  # place custom config in the initializer, it is global, do not configure in the models (not safe)
  # Userstamping.config = {}
  #
  # FIXME: overcome global config requirement with this in Rails 5:
  # http://blog.bigbinary.com/2016/09/05/rails-5-adds-ability-to-create-module-and-class-level-variables-on-per-thread-basis.html
  # http://qiita.com/yonda/items/06fadf31df39d343570e#thread_mattr_reader-thread_mattr_writer-thread_mattr_accessor
  # thread_mattr_accessor :config, instance_accessor: false do
  #   { store_key: :userstamping_gid, fields: { 'User' => 'user_id' } }
  # end
  # instead of custom setters, to allow configuration per model
  class << self
    def config=(config)
      @config = self.config.merge(config)
    end

    def config
      @config || { store_key: :userstamping_gid, fields: { 'User' => 'user_id' } }
    end
  end

  class_methods do
    # name = 'foo'
    # user = User.last
    # stamper = User.first
    # proc_object = Proc.new do
    #   SomeKlass.create(name: name, user: user)
    # end
    # SomeKlass.with_background_userstamping(stamper, proc_object)
    # :nocov:
    def with_background_userstamping(stamper, proc_object)
      Thread.current[Userstamping.config[:store_key]] = stamper.try(:to_global_id).to_s
      proc_object.call
    ensure
      Thread.current[Userstamping.config[:store_key]] = nil
    end
    # :nocov:
  end
  included do
    if Struct::const_defined? 'NullGlobalID'
      Struct.const_get 'NullGlobalID'
    else
      Struct.new('NullGlobalID', :model_id, :model_name)
    end

    attr_accessor :skip_userstamping

    Userstamping.config[:fields].each do |k_name, f_key|
      belongs_to :"created_by_#{k_name.underscore}", class_name: k_name.to_s,
                                                      foreign_key: "created_by_#{f_key}",
                                                      optional: true
      belongs_to :"updated_by_#{k_name.underscore}", class_name: k_name.to_s,
                                                      foreign_key: "updated_by_#{f_key}",
                                                      optional: true
      scope :"created_by_#{k_name.underscore}", ->(stamper) { where("created_by_#{f_key}" => stamper) }
      scope :"updated_by_#{k_name.underscore}", ->(stamper) { where("updated_by_#{f_key}" => stamper) }
    end

    before_save :set_userstamps
  end

  private

  def set_userstamps # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity, Metrics/MethodLength
    return if @skip_userstamping
    return unless changed?
    userstamping_gid = RequestStore.store[Userstamping.config[:store_key]] || Thread.current[Userstamping.config[:store_key]]
    gid_object = GlobalID.parse(userstamping_gid) if userstamping_gid.present?
    gid_object ||= Struct::NullGlobalID.new(nil, nil)
    gid_object_model_name_underscore = gid_object.model_name.try(:underscore)
    if gid_object.class == Struct::NullGlobalID
      # :nocov:
      stampers_given = []
      if new_record?
        Userstamping.config[:fields].each do |k_name, f_key|
          stampers_given << k_name if send(:"created_by_#{f_key}").present? && send(:"updated_by_#{f_key}").present? && send(:"modified_by_#{k_name.underscore}_at").present?
        end
        raise RuntimeError.new, 'Specify `stamper` object or/and pass 3 userstamping params: `created_by_?_id`, `updated_by_?_id`, `modified_by_?_at`' if stampers_given.empty?
      else
        Userstamping.config[:fields].each do |k_name, f_key|
          stampers_given << k_name if send(:"updated_by_#{f_key}").present? && send(:"modified_by_#{k_name.underscore}_at").present?
        end
        raise RuntimeError.new, 'Specify `stamper` object or/and pass 2 userstamping params: `updated_by_?_id`, `modified_by_?_at`' if stampers_given.empty?
      end
      # :nocov:
    else
      begin
        if new_record?
          send(:"created_by_#{gid_object_model_name_underscore}_id=",
               send(:"created_by_#{gid_object_model_name_underscore}_id") || gid_object.model_id)
          send(:"updated_by_#{gid_object_model_name_underscore}_id=",
               send(:"updated_by_#{gid_object_model_name_underscore}_id") ||
                 send(:"created_by_#{gid_object_model_name_underscore}_id") || gid_object.model_id)
          send(:"modified_by_#{gid_object_model_name_underscore}_at=",
               send(:"modified_by_#{gid_object_model_name_underscore}_at") || Time.current)
        end
        if id
          unless send(:"updated_by_#{gid_object_model_name_underscore}_id_changed?")
            send(:"updated_by_#{gid_object_model_name_underscore}_id=", gid_object.model_id)
          end
          unless send(:"modified_by_#{gid_object_model_name_underscore}_at_changed?")
            send(:"modified_by_#{gid_object_model_name_underscore}_at=", Time.current)
          end
        end
      rescue
        # Ignored
      end
    end
  end
end
