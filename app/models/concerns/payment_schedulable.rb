module PaymentSchedulable
  extend ActiveSupport::Concern

  included do # rubocop:disable Metrics/BlockLength
    has_one :payment_schedule, dependent: :destroy
    has_many :payments, through: :payment_schedule

    belongs_to :client, optional: true

    validates :starts_on, presence: true, if: :payment_schedule_required?
    validates :days_to_payment, numericality: { greater_than_or_equal_to: 0 },
                                if: :payment_schedule_required?
    validates :client, presence: true, if: :payment_schedule_required?
    validate :parent_activity, if: -> { new_record? || parent_id_changed? }

    validate :one_payment_schedule_per_account_number, if: lambda {
      payment_schedule_required? && payment_schedule_required_changed?
    }

    before_save :remind_payment_schedule, if: -> { payment_schedule_required? && payment_schedule_required_changed? }

    enum currency: CURRENCIES

    scope :for_accounting, proc { |date_from, date_to|
      date_conditions = []
      date_conditions << "issued_on >= '#{Arel.sql(date_from.to_fs(:sql))}'" if date_from
      date_conditions << "issued_on <= '#{Arel.sql(date_to.to_fs(:sql))}'" if date_to
      date_condition = "WHERE #{date_conditions.join(' AND ')}" if date_conditions.any?

      join_clause_generator = lambda do |*tables|
        tables.map.with_index do |table, index|
          clause = <<~SQL.squish
            LEFT JOIN (SELECT project_id, #{table}_payments.currency, SUM(amount) AS amount FROM #{table}_payments
            #{date_condition} GROUP BY project_id, #{table}_payments.currency) AS #{table}
            ON payment_schedules.project_id = #{table}.project_id
          SQL
          clause += " AND #{table}.currency = #{tables.first}.currency" if index.positive?
          clause
        end
      end

      select_clause = <<~SQL.squish
        projects.id, projects.name, projects.responsible_id, projects.accounting_number_id,
        COALESCE(scheduled.currency, completed.currency, pending.currency) AS currency,
        COALESCE(scheduled.amount, 0) AS scheduled_sum, COALESCE(pending.amount, 0) AS pending_sum,
        COALESCE(completed.amount, 0) AS paid_sum,
        (COALESCE(completed.amount, 0) - COALESCE(scheduled.amount, 0)) AS balance
      SQL

      scheduled_relation = select(select_clause).joins(:payment_schedule)
                                                .joins(join_clause_generator.call(:scheduled, :completed, :pending))
      completed_relation = select(select_clause).joins(:payment_schedule)
                                                .joins(join_clause_generator.call(:completed, :pending, :scheduled))
      pending_relation = select(select_clause).joins(:payment_schedule)
                                              .joins(join_clause_generator.call(:pending, :scheduled, :completed))
      results = <<~SQL.squish
        SELECT * FROM scheduled_relation UNION SELECT * FROM completed_relation UNION SELECT * FROM pending_relation
      SQL

      with(scheduled_relation:, completed_relation:, pending_relation:, results:)
        .joins(:company)
        .joins('INNER JOIN results ON results.id = projects.id')
        .select('results.*').where(companies: { accounting: true })
        .where('(projects.status = 0 OR scheduled_sum > 0 OR paid_sum > 0 OR pending_sum > 0)
               AND results.currency IS NOT NULL')
        .group('id, currency')
    }
  end

  def destroy
    mark_for_destruction
    super
  end

  private

  def remind_payment_schedule
    remind_after = accounting_settings['remind_after_hours'] || 1
    self.payment_schedule_remind_at = Time.zone.now + remind_after.to_i.hours
  end

  def accounting_settings
    @accounting_settings ||= Settings.accounting || {}
  end

  def one_payment_schedule_per_account_number
    return if accounting_number.blank?

    rel = Project.where(accounting_number: accounting_number,
                        payment_schedule_required: true)
    return if rel.empty?

    errors.add(:payment_schedule_required, :one_per_account_number)
  end
end
