module Searches
  class KubernetesNamespaceSearch < AssetSearch
    option :kubernetes_cluster_name do |scope, value|
      value.present? ? scope.joins(:kubernetes_cluster).where(kubernetes_clusters: { name: value }) : scope
    end

    option :project_identifier do |scope, value|
      value.present? ? scope.joins(:project).where(projects: { identifier: value }) : scope
    end

    option :name do |scope, value|
      value.present? ? scope.where(name: value) : scope
    end
  end
end
