module Searches
  class PaymentSearch
    include SearchObject.module(:model)

    option :issued_on_from do |scope, value|
      value.present? ? scope.where('payments.issued_on >= ?', value) : scope
    end

    option :issued_on_to do |scope, value|
      value.present? ? scope.where('payments.issued_on <= ?', value) : scope
    end

    option :sell_date_from do |scope, value|
      value.present? ? scope.where('payments.sell_date >= ?', value) : scope
    end

    option :sell_date_to do |scope, value|
      value.present? ? scope.where('payments.sell_date <= ?', value) : scope
    end

    option :project_id do |scope, value|
      for_project(scope, value)
    end

    option :company_id do |scope, value|
      for_project(scope, Project.where(company_id: value).ids)
    end

    option :client_id do |scope, value|
      for_project(scope, Project.where(client_id: value).ids)
    end

    private

    def for_project(scope, project_ids)
      base_scope = scope.joins(:payment_schedule, :mpk_positions)
      base_scope.where(payment_schedules: { project_id: project_ids }).or(
        base_scope.where(payment_mpk_positions: { project_id: project_ids })
      ).distinct
    end
  end
end
