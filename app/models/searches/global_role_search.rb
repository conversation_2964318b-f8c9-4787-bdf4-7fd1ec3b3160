# https://github.com/RStankov/SearchObject/tree/master/example
module Searches
  class GlobalRoleSearch
    include SearchObject.module(:model)
    include Sanitizers
    include ::Searches::Concerns::Sorting

    def self.sort_by
      %w( id name created_at updated_at )
    end

    option :term do |scope, value|
      sql = <<-SQL
        global_roles.name LIKE :term
        OR global_roles.activities LIKE :term
      SQL
      scope.where sql, term: escape_search_term(value)
    end

    option :created_after do |scope, value|
      date = parse_date value
      scope.where('DATE(global_roles.created_at) >= ?', date) if date.present?
    end

    option :created_before do |scope, value|
      date = parse_date value
      scope.where('DATE(global_roles.created_at) <= ?', date) if date.present?
    end

    option :collection_for_select do |scope, value|
      scope if value.to_s == 'true'
    end
  end
end
