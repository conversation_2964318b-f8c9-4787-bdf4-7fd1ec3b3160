# https://github.com/RStankov/SearchObject/tree/master/example
module Searches
  class PositionSearch
    include SearchObject.module(:model)
    include Sanitizers
    include ::Searches::Concerns::Sorting

    def self.sort_by
      %w[id name]
    end

    option :term do |scope, value|
      sql = <<-SQL
        positions.name LIKE :term
      SQL
      scope.where sql, term: escape_search_term(value)
    end
  end
end
