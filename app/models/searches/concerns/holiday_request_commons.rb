module Searches
  module Concerns
    module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
      extend ActiveSupport::Concern
      private

      def assemble_compound_filters(filters)
        filters = assemble_conditional_status_filter_params(filters)
        filters = assemble_conditional_date_filter_params(filters)
        filters = assemble_conditional_departments_ids_filter_params(filters)
        filters
      end

      def assemble_conditional_status_filter_params(filters)
        conditional_status_filter_params = filters.slice(*::Finders::Concerns::HolidayRequestCommons::COMPOUND_FILTER_PARAMS[:conditional_status_filter])
        unless conditional_status_filter_params.empty?
          filters[:conditional_status_filter] = conditional_status_filter_params
          filters.reject! { |key, _val| conditional_status_filter_params.include?(key.to_sym) }
        end
        filters
      end

      def assemble_conditional_date_filter_params(filters)
        conditional_date_filter_params = filters.slice(*::Finders::Concerns::HolidayRequestCommons::COMPOUND_FILTER_PARAMS[:conditional_date_filter])
        unless conditional_date_filter_params.empty?
          filters[:conditional_date_filter] = conditional_date_filter_params
          filters.reject! { |key, _val| conditional_date_filter_params.include?(key.to_sym) }
        end
        filters
      end

      def assemble_conditional_departments_ids_filter_params(filters)
        conditional_departments_ids_filter_params = filters.slice(*::Finders::Concerns::HolidayRequestCommons::COMPOUND_FILTER_PARAMS[:conditional_departments_ids_filter])
        unless conditional_departments_ids_filter_params.empty?
          filters[:conditional_departments_ids_filter] = conditional_departments_ids_filter_params
          filters.reject! { |key, _val| conditional_departments_ids_filter_params.include?(key.to_sym) }
        end
        filters
      end
    end
  end
end
