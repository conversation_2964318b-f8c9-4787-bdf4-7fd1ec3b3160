module Searches
  module Concerns
    module Sorting
      extend ActiveSupport::Concern
      included do
        def self.sort_by
          raise NotImplementedError
        end

        l = lambda do |scope, value| # lambda, because I want return to work
          key, direction = value.split
          return scope unless self.class.sort_by.map(&:to_s).include?(key)

          direction = 'asc' unless %w[asc desc].include?(direction)
          scope.order("#{scope.model.table_name}.#{key} #{direction}")
        end

        option :sort, &l
      end
    end
  end
end
