module Searches
  module Concerns
    module Sortable
      extend ActiveSupport::Concern
      included do
        def sort(scope, options = {})
          sortable_by = options[:sortable_by] || ['id']
          sortable_by_expressions = options[:sortable_by_expressions] || []
          filters = options[:filters] || {}
          if sortable_by_expressions.include?(filters['sort'].to_s)
            sort_values = [filters['sort'].to_s]
          else
            values = filters['sort'].to_s.split(',').map(&:strip)
            sort_values = []
            values.each do |value|
              sort_order = value.to_s.split(' ')
              sort_attribute = sort_order.select { |i| sortable_by.map(&:to_s).include?(i) || sortable_by.map(&:to_s).include?("#{scope.model.name.demodulize.tableize}.#{i}") }.first
              sort_direction = sort_order.select { |i| %w(DESC ASC).include?(i.upcase) }.first || 'DESC'
              prefix = sort_attribute =~ /\A.+\..+\z/ ? '' : "#{scope.model.name.demodulize.tableize}."
              sort_values << [prefix + sort_attribute, sort_direction].join(' ') if sort_attribute
            end
          end
          sort_values << ["#{scope.model.name.demodulize.tableize}.id", 'DESC'].join(' ')
          scope.order(sort_values.join(', '))
        end
      end
    end
  end
end
