module Searches
  module Concerns
    module CostInvoiceCommons
      extend ActiveSupport::Concern
      include Sanitizers

      def initialize(options, user)
        @user = user
        super(options)
      end

      # rubocop:disable Metrics/BlockLength
      included do
        option :date_from do |scope, value|
          value.present? ? scope.where('cost_invoices.invoice_date >= ?', value) : scope
        end

        option :date_to do |scope, value|
          value.present? ? scope.where('cost_invoices.invoice_date <= ?', value) : scope
        end

        option :due_date_from do |scope, value|
          value.present? ? scope.where('cost_invoices.due_date >= ?', value) : scope
        end

        option :due_date_to do |scope, value|
          value.present? ? scope.where('cost_invoices.due_date <= ?', value) : scope
        end

        option :sell_date_from do |scope, value|
          value.present? ? scope.where('cost_invoices.sell_date >= ?', value) : scope
        end

        option :sell_date_to do |scope, value|
          value.present? ? scope.where('cost_invoices.sell_date <= ?', value) : scope
        end

        option :contractor_id do |scope, value|
          value.present? ? scope.where(contractor_id: value) : scope
        end

        option :my_documents do |scope, _value|
          scope.where(user_id: @user.id)
        end

        option :accounting_number_id do |scope, value|
          value.presence && scope.joins(:cost_projects)
                                 .where(cost_projects: { accounting_number_id: value })
        end

        option :user_id do |scope, value|
          value.present? ? scope.where(user_id: value) : scope
        end

        option :company_id do |scope, value|
          value.present? ? scope.where(company_id: value) : scope
        end

        option :number do |scope, value|
          scope.where('cost_invoices.number LIKE ?', "%#{value}%")
        end

        option :total_amount_from do |scope, value|
          if value.present?
            cost_invoice_ids = scope.model
                                    .left_joins(:cost_invoice_positions)
                                    .group(:id)
                                    .having('COALESCE(SUM(amount * unit_price), 0) >= ?', value)
                                    .ids

            scope.where(id: cost_invoice_ids)
          else
            scope
          end
        end

        option :total_amount_to do |scope, value|
          if value.present?
            cost_invoice_ids = scope.model
                                    .left_joins(:cost_invoice_positions)
                                    .group(:id)
                                    .having('COALESCE(SUM(amount * unit_price), 0) <= ?', value)
                                    .ids

            scope.where(id: cost_invoice_ids)
          else
            scope
          end
        end

        option :accepted_after do |scope, value|
          date = parse_date value
          scope.where('DATE(cost_invoices.accepted_at) >= ?', date) if date.present?
        end

        option :accepted_before do |scope, value|
          date = parse_date value
          scope.where('DATE(cost_invoices.accepted_at) <= ?', date) if date.present?
        end
      end
      # rubocop:enable Metrics/BlockLength
    end
  end
end
