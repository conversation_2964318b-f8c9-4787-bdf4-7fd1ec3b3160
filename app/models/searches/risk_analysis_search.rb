module Searches
  class RiskAnalysisSearch
    include SearchObject.module(:model)
    include Sanitizers
    include ::Searches::Concerns::Sorting

    SORTABLE_FIELDS = %w[id name administrator_type creation_date creator].freeze

    def self.sort_by
      SORTABLE_FIELDS
    end

    option :term do |scope, value|
      sql = <<-SQL
        risk_analyses.name LIKE :term
      SQL
      scope.where sql, term: escape_search_term(value)
    end

    option :company_id do |scope, value|
      value.present? ? scope.where(company_id: value) : scope
    end

    option :state do |scope, value|
      value.present? ? scope.left_joins(:registry_activity).where(registry_activities: { state: value }) : scope
    end

    option :sort do |scope, value|
      key, direction = value.split
      next scope unless SORTABLE_FIELDS.include?(key)

      direction = 'asc' unless %w[asc desc].include?(direction)

      case key
      when 'administrator_type'
        case_statements = RegistryActivity.administrator_types.map do |type, type_value|
          if type == 'other'
            "WHEN registry_activities.administrator_type = #{type_value} THEN registry_activities.administrator_type_custom"
          else
            "WHEN registry_activities.administrator_type = #{type_value} THEN '#{type}'"
          end
        end.join("\n            ")

        sql = <<-SQL
          CASE
            #{case_statements}
            ELSE ''
          END #{direction}
        SQL
        scope.left_joins(:registry_activity).order(Arel.sql(sql))
      when 'creation_date'
        scope.left_joins(:registry_activity).order("registry_activities.creation_date #{direction}")
      when 'creator'
        scope.left_joins(registry_activity: :created_by).order("users.first_name #{direction}, users.last_name #{direction}")
      else
        scope.order("#{scope.model.table_name}.#{key} #{direction}")
      end
    end
  end
end
