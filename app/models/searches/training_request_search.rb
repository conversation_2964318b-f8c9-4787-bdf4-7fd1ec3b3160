module Searches
  class TrainingRequestSearch
    include SearchObject.module(:model)
    include Concerns::DepartmentSearchable
    include ::Searches::Concerns::Sorting

    def self.sort_by
      %i[kind starts_on ends_on updated_at]
    end

    option :date_from do |scope, value|
      value.present? ? scope.where(starts_on: value..) : scope
    end

    option :date_to do |scope, value|
      value.present? ? scope.where(ends_on: ..value) : scope
    end

    option :user_id do |scope, value|
      value.present? ? scope.where(user_id: value) : scope
    end

    option :departments_ids do |scope, value|
      value.present? ? scope.joins(:user).where(users: { department_id: searchable_department_ids(value) }) : scope
    end

    option :state do |scope, value|
      value.present? ? scope.where(state: value) : scope
    end
  end
end
