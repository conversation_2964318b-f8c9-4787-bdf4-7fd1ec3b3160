module Searches
  module B2B
    class CostInvoiceSearch
      include SearchObject.module(:model)
      include Searches::Concerns::CostInvoiceCommons

      option :state do |scope, value|
        scope.where(state: value)
      end

      option :mpk_number_id do |scope, value|
        if value.blank?
          scope
        else
          scope.joins(contractor: { user: { department: :mpk_number } })
               .where(mpk_numbers: { id: value })
        end
      end

      option :contractor_username do |scope, value|
        value.present? ? scope.joins(contractor: :user).where(users: { username: value }) : scope
      end
    end
  end
end
