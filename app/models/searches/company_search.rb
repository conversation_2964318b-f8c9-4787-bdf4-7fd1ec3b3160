# https://github.com/RStankov/SearchObject/tree/master/example
module Searches
  class CompanySearch
    include SearchObject.module(:model)
    include Sanitizers
    include ::Searches::Concerns::Sorting

    option :term do |scope, value|
      sql = <<-SQL
        companies.name LIKE :term
      SQL
      scope.where sql, term: escape_search_term(value)
    end

    option :created_after do |scope, value|
      date = parse_date value
      scope.where('DATE(companies.created_at) >= ?', date) if date.present?
    end

    option :created_before do |scope, value|
      date = parse_date value
      scope.where('DATE(companies.created_at) <= ?', date) if date.present?
    end

    option :native do |scope, value|
      value == 'true' && scope.native
    end
  end
end
