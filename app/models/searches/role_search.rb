# https://github.com/RStankov/SearchObject/tree/master/example
module Searches
  class RoleSearch
    include SearchObject.module(:model)
    include Sanitizers
    include ::Searches::Concerns::Sorting

    def self.sort_by
      %w[id name created_at updated_at docs_ldap chat_ldap ldap permissions]
    end

    option :term do |scope, value|
      sql = <<-SQL.squish
        roles.name LIKE :term
        OR roles.redmine_id LIKE :term
      SQL
      scope.where sql, term: escape_search_term(value)
    end

    option :created_after do |scope, value|
      date = parse_date value
      scope.where('DATE(roles.created_at) >= ?', date) if date.present?
    end

    option :created_before do |scope, value|
      date = parse_date value
      scope.where('DATE(roles.created_at) <= ?', date) if date.present?
    end
  end
end
