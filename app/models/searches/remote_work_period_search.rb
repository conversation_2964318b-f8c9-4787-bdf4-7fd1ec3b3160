module Searches
  class RemoteWorkPeriodSearch
    include SearchObject.module(:model)
    include Concerns::DepartmentSearchable

    option :date_from do |scope, value|
      value.present? ? scope.where(ends_on: value..) : scope
    end

    option :date_to do |scope, value|
      value.present? ? scope.where(starts_on: ..value) : scope
    end

    option :user_id do |scope, value|
      value.present? ? scope.where(user_id: value) : scope
    end

    option :state do |scope, value|
      value.present? ? scope.where(state: value) : scope
    end

    option :department_ids do |scope, value|
      return scope if value.blank?

      scope.joins(:user).where(users: { department_id: searchable_department_ids(value) })
    end
  end
end
