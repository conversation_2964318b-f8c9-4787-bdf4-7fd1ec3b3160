module Searches
  class InvoiceSearch
    include SearchObject.module(:model)

    option :state do |scope, value|
      case value
      when 'issued'
        scope.issued.order(invoice_date: :desc, number: :desc)
      when 'pending'
        scope.pending.order(invoice_date: :asc)
      when 'accepted'
        scope.accepted.order(invoice_date: :desc, number: :desc)
      else
        scope
      end
    end

    option :kind do |scope, value|
      case value
      when 'invoice'
        scope.where.not(kind: :accounting_note)
      when *Invoice.kinds.keys
        scope.where(kind: value)
      else
        scope
      end
    end

    option :project_id do |scope, value|
      scope.joins(payment: :payment_schedule).where(payment_schedules: { project_id: value })
    end

    option :company_id do |scope, value|
      scope.joins(payment: { payment_schedule: :project }).where(projects: { company_id: value })
    end

    option :client_id do |scope, value|
      scope.joins(payment: { payment_schedule: :project }).where(projects: { client_id: value })
    end

    option :date_from do |scope, value|
      value.present? ? scope.where('invoices.invoice_date >= ?', value) : scope
    end

    option :date_to do |scope, value|
      value.present? ? scope.where('invoices.invoice_date <= ?', value) : scope
    end

    option :acceptor_id do |scope, value|
      value.present? ? to_user_accept(scope, User.find_by(id: value)) : scope
    end

    option :user_id do |scope, value|
      value.present? ? scope.where(user_id: value) : scope
    end

    private

    def to_user_accept(scope, user)
      return scope.none unless user && Pundit.policy(user, Invoice).accept?

      scope.pending
    end
  end
end
