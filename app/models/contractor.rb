class Contractor < ApplicationRecord
  include AASM
  extend BankAccountable
  include <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  has_paper_trail

  belongs_to :user, optional: true
  belongs_to :created_by, class_name: 'User', optional: true
  has_many :cost_invoices, dependent: :destroy

  validates :name, :vat_number, :country, presence: true
  validates :user_id, uniqueness: true, allow_nil: true
  validates :vat_number, uniqueness: { case_sensitive: false }

  enum state: { pending: 0, active: 1 }

  ibanize :account_number

  before_validation :adjust_vat_number
  before_validation :download_gus_data_and_validate,
                    if: -> { download_from_gus? && vat_number.present? }
  after_commit :notify, on: :create, if: :pending?

  aasm column: :state, enum: true do
    state :pending, initial: true
    state :active

    event :activate do
      transitions from: :pending, to: :active
    end
  end

  def all_cost_invoices_deleted?
    cost_invoices.empty? || (dms_cost_invoices_deleted? && b2b_cost_invoices_rejected?)
  end

  private

  def dms_cost_invoices_deleted?
    dms_cost_invoices = cost_invoices.where(type: 'Dms::CostInvoice')
    dms_cost_invoices.empty? || dms_cost_invoices.all?(&:deleted?)
  end

  def b2b_cost_invoices_rejected?
    b2b_cost_invoices = cost_invoices.where(type: 'B2B::CostInvoice')
    b2b_cost_invoices.empty? || b2b_cost_invoices.all?(&:rejected?)
  end

  def notify
    ContractorsMailer.to_accept(self).deliver_later
  end
end
