class InventoryItem < ApplicationRecord
  include AASM

  enum state: { pending: 0, active: 1, closed: 2, rejected: 3 }
  enum location: { office: 0, remote: 1 }

  belongs_to :user
  belongs_to :requester, class_name: 'User'
  belongs_to :company, optional: true

  validates :name, :description, presence: true

  default_scope -> { where('inventory_items.state != 3') }

  aasm column: :state, enum: true, timestamps: true, whiny_transitions: false do
    state :pending, initial: true
    state :active, :closed, :rejected

    event :activate do
      transitions from: :pending, to: :active
    end

    event :close do
      transitions from: :active, to: :closed
    end

    event :reject do
      transitions from: :pending, to: :rejected
    end
  end
end
