class Role < ApplicationRecord
  include Documentation::RoleModel
  include RedmineSynchronizable

  # NOTE: list of strings (symbols in Redmine)
  serialize :permissions, Array

  SUPPORTED = %w[add_subprojects edit_project close_project manage_members manage_payment_schedule
                 show_payment_schedule show_regression_stats manage_docs_files].freeze

  RM_ATTRIBUTES = [:name].freeze

  validates :name, presence: true, uniqueness: { case_sensitive: false }

  has_many :membership_roles, dependent: :restrict_with_error

  before_validation :exclude_unsupported_permissions

  scope :financial_controlling, -> { where(financial_controlling: true) }

  def self.default
    find_by(id: Settings.new_project_user_role_id) || first
  end

  private

  def exclude_unsupported_permissions
    self.permissions = permissions.to_a.map(&:to_s) & SUPPORTED.map(&:to_s)
  end

  def redmine_synchronization?
    true
  end
end
