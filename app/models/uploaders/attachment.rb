module Uploaders
  class Attachment < Shrine
    plugin :default_storage, cache: :cache, store: :store
    plugin :activerecord
    plugin :cached_attachment_data # for retaining the cached file across form redisplays
    plugin :restore_cached_data # re-extract metadata when attaching a cached file
    plugin :validation_helpers
    plugin :determine_mime_type, analyzer: :mimemagic

    Attacher.validate do
      validate_max_size 24 * 1024 * 1024
    end
  end
end
