class TrainingRequest < ApplicationRecord
  include AASM

  has_paper_trail

  belongs_to :user

  validates :state, :kind, :provider, :starts_on, :ends_on, :place, :mode, :description, presence: true
  validates :tickets_price, :transportation_price, :accommodation_price, numericality: { greater_than_or_equal_to: 0 }
  validates :provider, length: { maximum: 255 }
  validate :budget_exceeding, if: -> { !force && state_changed? && (accepted? || pending_hr?) }

  enum state: { pending_chief: 0, pending_hr: 1, accepted: 2, rejected: 3 }
  enum kind: { workshop: 0, conference: 1, course: 2, study: 3 }
  enum place: { office: 0, provider_hq: 1, online: 2 }
  enum mode: { stationary: 0, remote: 1 }

  scope :for_year, ->(year) { where(starts_on: Date.new(year)..Date.new(year).end_of_year) }
  scope :pending, -> { where(state: %i[pending_chief pending_hr]) }

  attr_accessor :force

  aasm column: :state, enum: true do
    state :pending_chief, initial: true
    state :pending_hr, :accepted, :rejected

    event :accept, before: proc { |force| self.force = force } do
      transitions from: :pending_chief, to: :pending_hr
      transitions from: %i[pending_hr rejected], to: :accepted
    end

    event :reject do
      transitions from: %i[pending_hr pending_chief accepted], to: :rejected
    end
  end

  def total_price
    tickets_price + transportation_price + accommodation_price
  end

  private

  def budget_exceeding
    return unless user

    exceeded_by = current_budget_usage + total_price - training_budget

    return unless exceeded_by.positive?

    add_budget_exceeded_errors(exceeded_by)
  end

  def current_budget_usage
    user.department
        .training_requests
        .for_year(starts_on.year)
        .where.not(id:)
        .sum('tickets_price + transportation_price + accommodation_price')
  end

  def add_budget_exceeded_errors(exceeded_by)
    exceeded_by = ActionController::Base.helpers.number_to_currency(exceeded_by, format: '%n %u', unit: 'PLN')
    errors.add(:tickets_price, :budget_exceeded, exceeded_by:)
    errors.add(:transportation_price, :budget_exceeded, exceeded_by:)
    errors.add(:accommodation_price, :budget_exceeded, exceeded_by:)
  end

  def training_budget
    user.department&.training_budget(starts_on.year) || 0
  end
end
