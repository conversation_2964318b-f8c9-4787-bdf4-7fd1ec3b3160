require "httpi"
require 'exceptions/ldap_exception'
class LdapData
  def initialize(connection)
    @connection = connection
  end

  def create_entry_in_ldap_server(entry)
    @connection.add(dn: entry.dn, attributes: entry.ldap_attributes)
    validate_request(entry.dn)
  end

  def destroy_entry_in_ldap_server(dn)
    @connection.delete dn: dn
    validate_request(dn)
  end

  def rename_entry_in_ldap_server(changes)
    @connection.rename olddn: changes[0], newrdn: changes[1].split(',').first
    validate_request(changes[1])
  end

  def modify_entry_in_ldap_server(dn, changes)
    diff = changes_diff(changes)
    modify_params = diff.map do |key, value|
      [:replace, key, value]
    end
    return if modify_params.empty?

    @connection.modify(dn: dn, operations: modify_params)
    validate_request(dn)
  end

  private

  def validate_request(distinguished_name)
    results = @connection.get_operation_result
    Sidekiq.logger.info("LDAP operation result for #{distinguished_name}: #{results.message}")

    return if results.error_message.blank?

    raise Exceptions::LdapException,
          "code: #{results.code}, msg: #{results.message}, error_msg: #{results.error_message}"
  end

  def changes_diff(changes)
    previous, current = changes
    return [] unless previous

    diff = current.to_a - previous.to_a
    (previous.keys - current.keys).each do |gone_key|
      diff << [gone_key, nil]
    end
    diff
  end

  def logger
    @logger ||= Rails.env.test? ? Rails.logger : Sidekiq.logger
  end
end
