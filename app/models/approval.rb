class Approval < ApplicationRecord
  belongs_to :user
  belongs_to :approvable, polymorphic: true
  belongs_to :project_agreement, lambda {
    select('project_agreements.*, projects.name as project_name')
      .joins(:project)
  }, foreign_key: :approvable_id, inverse_of: :approvals, optional: true

  validates :user, :approvable, presence: true
  validates :approvable_id, uniqueness: { scope: %i[user_id approvable_type] }

  after_commit :set_user_has_approvals
  after_commit :send_notification, on: :create

  scope :not_accepted, -> { where(accepted: false) }
  scope :accepted, -> { where(accepted: true) }

  def accept
    update(accepted: true, accepted_at: Time.zone.now)
  end

  private

  def send_notification
    Rodo::RodoMailer.new_approval(self).deliver_later
  end

  def set_user_has_approvals
    user.update_approvals_cache
  end
end
