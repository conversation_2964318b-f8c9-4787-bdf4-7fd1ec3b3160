module Base
  class SerializableDestroyedModelDecorator < SimpleDelegator
    include GlobalID::Identification
    include ActiveModel::Serializers::JSON

    def self.find(id)
      from_json = JSON.parse(id)
      new_class = from_json.keys.first.classify.constantize
      new_object = new_class.new(from_json.values.first)
      new_object.instance_variable_set(:@destroyed, true)
      new_object.readonly!
      new_object.freeze
    end

    def id
      to_json(root: true)
    end
  end
end
