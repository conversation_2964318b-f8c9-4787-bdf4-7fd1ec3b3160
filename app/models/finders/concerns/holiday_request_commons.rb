module Finders
  module Concerns
    module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> # rubocop:disable Metrics/ModuleLength
      extend ActiveSupport::Concern

      COMPOUND_FILTER_PARAMS = {
        conditional_status_filter: %i[visible pending accepted rejected not_confirmed],
        conditional_departments_ids_filter: %i[departments_ids applicant_id],
        conditional_date_filter: %i[starts_on_after ends_on_before year_month year]
      }.freeze

      included do
        include Sanitizers
        include ::Finders::Concerns::FilterableScoping

        filterable_scopes :created_before,
                          :created_after,
                          :category,
                          :term,
                          :project_id,
                          :overuse_of_holidays,
                          conditional_status_filter: COMPOUND_FILTER_PARAMS[:conditional_status_filter],
                          conditional_departments_ids_filter: COMPOUND_FILTER_PARAMS[:conditional_departments_ids_filter],
                          conditional_date_filter: COMPOUND_FILTER_PARAMS[:conditional_date_filter],
                          filterable_scope_for: :regular_user
      end

      def call(params)
        @params = params || {}
        filterable_scope(@scope)
      end

      def created_before
        safe_date = parse_date(@params[:created_before])
        @scope = @scope.where('DATE(holiday_requests.created_at) <= ?', safe_date) if safe_date.present?
        @scope
      end

      def created_after
        safe_date = parse_date(@params[:created_after])
        @scope = @scope.where('DATE(holiday_requests.created_at) >= ?', safe_date) if safe_date.present?
        @scope
      end

      def category
        safe_category = ::HolidayRequest.categories[@params[:category]]
        @scope = @scope.where(holiday_requests: { category: safe_category }) if safe_category.present?
        @scope
      end

      def term # rubocop:disable Metrics/MethodLength
        if @params[:term].present?
          sql = ''
          @params[:term].to_s.split(' ').each_with_index do |word, index|
            # multiple LIKEs work like ANDs
            sql << if index.zero?
                     <<-SQL
                       lower(users.first_name) LIKE :term
                       OR lower(users.last_name) LIKE :term
                     SQL
                   else
                     <<-SQL
                       OR lower(users.first_name) LIKE :term
                       OR lower(users.last_name) LIKE :term
                     SQL
                   end
            @scope = @scope.where(sql, term: escape_search_term(word))
          end
        end
        @scope
      end

      def conditional_status_filter # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
        filters = @params[:conditional_status_filter] || {}
        if filters[:visible].present?
          # `true` - Accepted, `false` - Pending & Rejected
          @scope = @scope.where(holiday_requests: { visible: filters[:visible].to_s == 'true' })
        else
          is_accepted = filters[:accepted].to_s == 'true'
          is_not_confirmed = filters[:not_confirmed].to_s == 'true'
          is_pending = filters[:pending].to_s == 'true'
          is_rejected = filters[:rejected].to_s == 'true'

          sql_conditions = add_status_filter_condition(is_accepted, is_pending, is_rejected)

          @scope = @scope.where(sql_conditions.size > 0 ? sql_conditions.join(' OR ') : sql_conditions) if sql_conditions.any?

          @scope = @scope.where(holiday_requests: { confirmed: !is_not_confirmed }) if is_accepted && is_not_confirmed
        end
        @scope
      end

      def add_status_filter_condition(is_accepted, is_pending, is_rejected)
        sql_conditions = []
        sql_conditions << '(holiday_requests.accepted_at IS NULL AND holiday_requests.rejected_at IS NULL)' if is_pending
        sql_conditions << 'holiday_requests.accepted_at IS NOT NULL' if is_accepted
        sql_conditions << 'holiday_requests.rejected_at IS NOT NULL' if is_rejected
        sql_conditions
      end

      def conditional_date_filter # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity, Metrics/MethodLength
        filters = @params[:conditional_date_filter] || {}
        if filters[:starts_on_after].present? && filters[:ends_on_before].present?
          starts_on = parse_date(filters[:starts_on_after])
          ends_on = parse_date(filters[:ends_on_before])
          if starts_on.present? && ends_on.present?
            if filters[:year_month]
              @scope = @scope.where(
                "DATE_FORMAT(holiday_requests.starts_on, '%Y-%m') = :starts_on OR
                 DATE_FORMAT(holiday_requests.ends_on, '%Y-%m') = :ends_on OR
                 (DATE_FORMAT(holiday_requests.starts_on, '%Y-%m') < :starts_on AND
                  DATE_FORMAT(holiday_requests.ends_on, '%Y-%m') > :ends_on)",
                starts_on: starts_on.to_date.strftime('%Y-%m'),
                ends_on: ends_on.to_date.strftime('%Y-%m')
              )
            elsif filters[:year] # FIXME: not used + depends on sidekiq
              @scope = @scope.joins(:absences).where("DATE_FORMAT(absences.date, '%Y') = ?",
                                                     starts_on.to_date.strftime('%Y')).group('holiday_requests.id')
            else
              sql = <<-SQL
                ((DATEDIFF(starts_on, :ends_on) * DATEDIFF(:starts_on, ends_on)) >= 0)
              SQL
              @scope = @scope.where(sql, starts_on: starts_on, ends_on: ends_on)
            end
          end
        elsif filters[:starts_on_after].present?
          date = parse_date(filters[:starts_on_after])
          @scope = @scope.where('DATE(holiday_requests.starts_on) >= ?', date) if date.present?
        elsif filters[:ends_on_before].present?
          date = parse_date(filters[:ends_on_before])
          @scope = @scope.where('DATE(holiday_requests.ends_on) <= ?', date) if date.present?
        end
        @scope
      end
    end
  end
end
