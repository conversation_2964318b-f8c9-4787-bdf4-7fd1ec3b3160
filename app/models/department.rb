class Department < ApplicationRecord
  include Documentation::DepartmentModel

  belongs_to :company, optional: true
  belongs_to :uber_chief,
             class_name: 'User',
             inverse_of: :departments_as_uber_chief,
             optional: true
  belongs_to :chief,
             class_name: 'User',
             inverse_of: :departments_as_chief
  belongs_to :substitute_chief,
             class_name: 'User',
             inverse_of: :departments_as_substitute_chief,
             optional: true
  belongs_to :supervisor,
             class_name: 'User',
             inverse_of: :departments_as_supervisor,
             optional: true
  belongs_to :mpk_number
  belongs_to :role, optional: true
  has_many :agreement_departments
  has_many :agreements, through: :agreement_departments
  has_many :cost_projects, dependent: :destroy
  has_many :cost_allocation_template_positions, dependent: :destroy
  has_many :training_budgets, dependent: :destroy
  has_many :users, dependent: :nullify
  has_many :active_users, -> { active }, class_name: 'User', inverse_of: :department
  has_many :training_requests, through: :users

  validates :name, presence: true,
                   uniqueness: { scope: :company_id,
                                 case_sensitive: false }

  validates :chief_id, presence: true

  scope :not_locked, -> { where(locked: false) }

  scope :in_chief_or_substitute_chief, lambda { |user_id|
    where(chief_id: user_id).or(where(substitute_chief_id: user_id)).or(where(uber_chief_id: user_id)).or(
      where(supervisor_id: user_id)
    )
  }

  scope :member_or_in_chief_or_substitute_chief, (lambda do |department_ids, user_id|
    where('departments.id IN (:ids) OR chief_id = :user_id OR substitute_chief_id = :user_id',
          ids: department_ids,
          user_id: user_id)
  end)

  scope :chief_on_holiday, -> { joins(chief: :absences).where(absences: { date: Date.current, hours: nil }) }
  scope :uber_chief_on_holiday, -> { joins(uber_chief: :absences).where(absences: { date: Date.current, hours: nil }) }

  def chief_on_holiday?
    chief&.absences&.current_absence&.any?
  end

  def uber_chief_on_holiday?
    uber_chief&.absences&.current_absence&.any?
  end

  def training_budget(year)
    ((training_budgets.find_by(year: year) || TrainingBudget.find_by(year: year))&.amount || 0) *
      users_active_in_year(year)
  end

  def chief_or_uber_chief?(user_id)
    [chief_id, uber_chief_id].include?(user_id)
  end

  private

  def users_active_in_year(year)
    year_beginning = Date.new(year, 1, 1)
    year_end = Date.new(year, 12, 31)
    users.joins(:user_contracts).merge(UserContract.overlapping(year_beginning, year_end)).distinct.count
  end
end
