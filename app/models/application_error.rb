class ApplicationError < StandardError
  include Documentation::ApplicationErrorModel

  attr_reader :status, :errors, :backtrace

  def initialize(msg)
    if msg.respond_to?(:[])
      opts = msg
      message = message.is_a?(Symbol) ? I18n.t(message).to_s : opts[:message]
      @backtrace = opts[:backtrace] if opts[:backtrace]
      @status = opts[:status] if opts[:status]
    else
      message = msg
    end
    @status ||= :internal_server_error
    @errors = { base: [message] }
  end
end
