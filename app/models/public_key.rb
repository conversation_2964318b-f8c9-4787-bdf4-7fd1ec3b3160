class PublicKey < ApplicationRecord
  include Documentation::PublicKeyModel
  include RedmineSynchronizable

  IDENTIFIER_LENGTH_LIMIT = 60

  belongs_to :user, touch: true

  validates :user, presence: true
  validates :identifier, presence: true,
                         uniqueness: { case_sensitive: false, scope: :user_id },
                         length: { maximum: IDENTIFIER_LENGTH_LIMIT },
                         format: /\A[a-z0-9_\-]*\z/i
  validates :key, presence: true

  validate :not_changed
  validate :key_correctness
  validate :key_uniqueness

  before_validation :strip_whitespace
  before_validation :remove_control_characters

  private

  def rm_attr_changed?
    redmine_id.blank?
  end

  # Strip leading and trailing whitespace
  # Don't mess with existing keys (since cannot change key text anyway)
  #
  def strip_whitespace
    return unless new_record?
    self.identifier = identifier.try(:strip) || ''
    self.key = key.try(:strip) || ''
  end

  def set_fingerprint
    self.fingerprint = SSHValidator.ssh_fingerprint(key)
  end

  def not_changed
    return if new_record?
    %w(identifier key user_id fingerprint).each do |attribute|
      method = "#{attribute}_changed?"
      errors.add(attribute, :cannot_change) if send(method)
    end
  end

  # Test correctness of ssh key
  def key_correctness
    return false if key.nil?
    if SSHKey.valid_ssh_public_key?(key)
      self.fingerprint = SSHKey.fingerprint(key)
    else
      errors.add(:key, :corrupted)
      return false
    end
  end

  def key_uniqueness
    return unless new_record?
    existing = PublicKey.find_by(fingerprint: fingerprint)
    if existing
      if existing.user == user
        errors.add(:key, :taken_by_you, name: existing.identifier)
      else
        errors.add(:key, :taken_by_someone)
      end
    end
  end

  def remove_control_characters
    return unless new_record?
    # First -- let the first control char or space stand (to divide key type from key)
    # Really, this is catching a special case in which there is a \n between type and key.
    # Most common case turns first space back into space....
    self.key = key.sub(/[ \r\n\t]/, ' ')

    # Next, if comment divided from key by control char, let that one stand as well
    # We can only tell this if there is an "=" in the key. So, won't help 1/3 times.
    self.key = key.sub(/=[ \r\n\t]/, '= ')

    # Delete any remaining control characters....
    self.key = key.gsub(/[\a\r\n\t]/, '').strip
  end

  def redmine_synchronization?
    true
  end
end
