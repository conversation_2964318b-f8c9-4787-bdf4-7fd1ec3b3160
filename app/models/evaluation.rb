class Evaluation < ApplicationRecord
  # TODO: remove all old tables

  belongs_to :user, touch: true
  belongs_to :created_by, class_name: 'User', inverse_of: 'created_evaluations'
  belongs_to :main_survey, class_name: 'Survey', inverse_of: 'evaluations_as_main'
  belongs_to :additional_survey, class_name: 'Survey', inverse_of: 'evaluations_as_additional',
                                 optional: true
  has_many :evaluation_additional_users, dependent: :destroy
  has_many :additional_users, through: :evaluation_additional_users,
                              source: :user
  has_many :survey_answers, dependent: :destroy
  has_many :answer_comments, -> { order(:id) }, dependent: :destroy, inverse_of: :evaluation

  validates :starts_on, presence: true
  validates :user, presence: true
  validates :name, presence: true
  validates :main_survey, presence: true
  validate :max_additional_users

  accepts_nested_attributes_for :main_survey
  accepts_nested_attributes_for :answer_comments, reject_if: :new_answer_comment?

  enum state: %i[pending discarded in_progress completed]

  scope :with_survey_answers_count, lambda {
    select('evaluations.*, COALESCE(all_survey_answers.count, 0) AS survey_answers_count,
                           COALESCE(completed_survey_answers.count, 0)
                             AS completed_survey_answers_count')
      .joins('LEFT JOIN (SELECT survey_answers.evaluation_id, count(*) AS count
                FROM survey_answers
                GROUP BY survey_answers.evaluation_id)
              all_survey_answers on all_survey_answers.evaluation_id = evaluations.id')
      .joins('LEFT JOIN (SELECT survey_answers.evaluation_id, count(*) AS count
                FROM survey_answers
                WHERE survey_answers.state = 1
                GROUP BY survey_answers.evaluation_id)
              completed_survey_answers ON completed_survey_answers.evaluation_id = evaluations.id')
      .distinct
  }

  scope :active, -> { where(state: [0, 2]) }

  def start!
    self.state = :in_progress
    self.starts_on = Time.zone.today
    initialize_connected_records
    save!
    Evaluations::EvaluationMailer.created(user).deliver_later
    return if additional_users.empty?
    Evaluations::EvaluationMailer.additional_users(additional_users.to_a, user).deliver_later
  end

  def stop(params)
    assign_attributes(params)
    self.state = :completed
    self.ends_on = Time.zone.today
    save
    survey_answers.pending.each(&:discarded!)
  end

  def discard!
    self.state = :discarded
    save!
    survey_answers.each(&:discarded!)
  end

  private

  def new_answer_comment?(answer_comment_hash)
    answer_comment_id = answer_comment_hash[:id]
    answer_comment_id.blank? || answer_comments.where(id: answer_comment_id).empty?
  end

  def initialize_connected_records
    initialize_survey_answers
    initialize_answer_comments
  end

  def initialize_survey_answers
    # main survey:
    [user, chief].each do |u|
      survey_answers.build(survey: main_survey, user: u, kind: 'main')
    end
    # additional survey:
    additional_users.each do |u|
      survey_answers.build(survey: additional_survey, user: u,
                           kind: 'additional')
    end
  end

  def initialize_answer_comments
    main_survey.survey_questions.count.times do
      answer_comments.build
    end
  end

  def chief
    department_chief = user.department.chief
    department_chief == user ? user.department.uber_chief : department_chief
  end

  def max_additional_users
    return if additional_users.size <= 5
    errors.add(:additional_users, :more_than_five)
  end
end
