class MpkPosition < ApplicationRecord
  include Mpk::Positionable

  belongs_to :invoice, inverse_of: :mpk_positions
  belongs_to :project, optional: true

  validates :amount, numericality: { greater_than_or_equal_to: 0,
                                     if: -> { invoice && !invoice.draft? } }
  validate :same_company_and_client_as_main_project, if: -> { invoice && project }

  before_save :save_invoice_allocation_version, if: :changed?

  private

  def same_company_and_client_as_main_project
    return if project.company_id == invoice.project.company_id &&
              project.client_id == invoice.project.client_id

    errors.add(:project, :must_belong_to_same_company_and_client)
  end

  def save_invoice_allocation_version
    invoice.save_allocation_version = true
  end
end
