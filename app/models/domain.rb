class Domain < Asset
  DOMAIN_REGEX = /\A(?:[a-z0-9*](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]\z/

  belongs_to :project, dependent: :destroy
  belongs_to :user, optional: true

  validates :name, presence: true, format: { with: DOMAIN_REGEX }, on: %i[create process]
  enum ssl_type: %i[empty encrypt our client]

  validates :wildcard, presence: true, if: :our_ssl?

  def our_ssl?
    ssl_type == 'our'
  end

  def admin_request_content
    "Request for new #{type}
requester #{requester.email},
sponsor: #{passed_to_execution_by.email},
name: #{name},
wildcard: #{wildcard},
internally managed: #{internally_managed},
internally purchased #{internally_purchased}"
  end

  def admin_decommission_request_content
    "#{type} decommission request,
name: #{name},
id: #{id},
decommission comment: #{decommission_comment}"
  end
end
