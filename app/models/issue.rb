class Issue < ApplicationRecord
  CATEGORY_EXPRESSION = <<~SQL.squish.freeze
    CASE
      WHEN issues.tracker LIKE '%bug%' THEN 'bugs'
      WHEN issues.subject LIKE '%komunikacja wewnętrzna: statusy, daily%' THEN 'internal_communication'
      WHEN issues.subject LIKE '%komunikacja zewnętrzna: rytuały, spotkania i warsztaty z klientem%' THEN 'external_communication'
      WHEN issues.subject LIKE '%deployment, ci/cd%' THEN 'deployment'
      ELSE 'development'
    END
  SQL

  belongs_to :project

  has_many :resource_time_entries, dependent: :destroy
  has_many :spent_time_entries
  has_many :scheduled_time_entries

  validates :redmine_id, presence: true, uniqueness: true, numericality: { only_integer: true }, if: -> { click_up_id.nil? }
  validates :click_up_id, presence: true, uniqueness: true, if: -> { redmine_id.nil? }

  validates :estimated_hours, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true

  scope :with_categories, -> { select("issues.*, #{CATEGORY_EXPRESSION} AS category") }
  scope :with_category, ->(category) { where("#{CATEGORY_EXPRESSION} = ?", category) }
end
