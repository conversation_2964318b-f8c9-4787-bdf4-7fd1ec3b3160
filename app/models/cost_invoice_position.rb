class CostInvoicePosition < ApplicationRecord
  include InvoicePositionable
  include LiberalEnum

  belongs_to :cost_invoice, inverse_of: :cost_invoice_positions

  liberal_enum :tax_rate

  validates :cost_invoice, :name, presence: true
  validates :amount, :unit_price, :net_value,
            numericality: true, presence: true
  validates :tax_rate, inclusion: { in: tax_rates.keys }
  validates :tax_rate, presence: true
  validates :name, length: { maximum: 255 }

  before_validation :set_net_value, if: -> { unit_price.present? && amount.present? }

  private

  def set_net_value
    self.net_value = unit_price * amount if !net_value || calculate_net_value?
  end

  def calculate_net_value?
    abs_amount = amount.abs

    !((abs_amount >= 1 && (net_value / amount).round(2) == unit_price) ||
      (abs_amount < 1 && (unit_price * amount).round(2) == net_value))
  end
end
