class Server < Asset
  belongs_to :project, dependent: :destroy
  belongs_to :user, optional: true

  enum machine_type: %i[vm dedicated shared_host]
  enum db_type: %i[no_db mysql psql other_type]
  # technology, hosting and backup are kept for backward compatibility
  enum technology: %i[no_tech tomcat8 tomcat9 puma nodejs wordpress php other_technology apache_php nginx_php apache nginx]
  enum hosting: %i[on_site client external_provider]
  enum environment: %i[dev prod test accept]
  enum backup: %i[standard no_backup custom]

  validates :technology, :environment, :ram_count, :storage, :db_type, presence: true
  validates :ram_count, :storage, numericality: { greater_than: 0 }
  validates :db_size, presence: true, numericality: { greater_than: 0 }, unless: :no_db?
  validates :notes, presence: true, if: :notes_required?
  validates :receivers_addresses, presence: true, if: :send_emails

  validates :host, presence: true, if: -> { state_changed? && active? && state_was == 'in_progress' }

  def no_db?
    db_type == 'no_db'
  end

  def no_tech?
    technology == 'no_tech'
  end

  def notes_required?
    hosting == 'external_provider' || technology == 'other_technology'
  end

  # rubocop:disable Metrics/AbcSize, Metrics/MethodLength

  def admin_request_content
    "Request nr (#{id}) for new #{type}
in #{project.name} (id: #{project.id}),
requester: #{requester.email},
sponsor: #{passed_to_execution_by.email},
environment: #{environment},
technology: #{technology},
elasticsearch: #{elastic_search},
ram: #{ram_count}GB,
storage: #{storage}GB,
sends emails: #{send_emails},
sender_address(es): #{receivers_addresses},
database type: #{db_type},
database size: #{db_size}MB,
notes: #{notes}"
  end
  # rubocop:enable Metrics/AbcSize, Metrics/MethodLength

  def admin_decommission_request_content
    "#{type} decommission request
in #{project.name} (id: #{project.id}),
id: #{id},
host: #{host},
decommission comment: #{decommission_comment}"
  end
end
