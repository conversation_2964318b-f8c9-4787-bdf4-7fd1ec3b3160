class Client < ApplicationRecord
  include AASM
  extend ClientsSummary
  include VATFormattable
  include GusDataHelper

  validates :name, :street_number, :city, :postcode, :post, :country, :vat_number, :invoice_sending_method,
            presence: true
  validates :invoice_sending_email, presence: true, format: {
    with: /\A[\w+\-.]+@[a-z\d-]+(\.[a-z\d-]+)*\.[a-z]+\s*(,\s*[\w+\-.]+@[a-z\d-]+(\.[a-z\d-]+)*\.[a-z]+\s*)*\z/
  }, if: :electronic_invoice_sending_method?
  # rubocop:disable Rails/UniqueValidationWithoutIndex
  validates :name, :vat_number, uniqueness: { case_sensitive: false }
  # rubocop:enable Rails/UniqueValidationWithoutIndex

  has_many :projects, dependent: :restrict_with_error
  has_many :client_addresses

  enum invoice_sending_method: { paper: 0, electronic: 1 }, _suffix: true
  enum state: { pending: 0, active: 1 }

  before_validation :download_gus_data_and_validate,
                    if: -> { download_from_gus? && vat_number.present? }
  after_create :notify, if: :pending?

  has_paper_trail on: %i[create update destroy]

  aasm column: :state, enum: true do
    state :pending, initial: true
    state :active

    event :activate do
      transitions from: :pending, to: :active
    end
  end

  private

  def notify
    ClientsMailer.to_accept(self).deliver_later
  end
end
