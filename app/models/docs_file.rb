require 'owncloud_provider'

class DocsFile < ApplicationRecord
  include LiberalEnum

  belongs_to :project
  belongs_to :created_by, class_name: 'User', optional: true

  has_many :docs_file_comments, dependent: :destroy

  enum category: { '00. Przetarg NB': 0, '01. Umowa i NDA': 1, '02. Dokumentacja Projektowa': 2,
                   '03. Finalne produkty': 3, '04. Referencje i Case Study': 4,
                   '05. Faktury i załączniki do faktur': 5 }
  liberal_enum :category

  validates :category, presence: true, inclusion: { in: categories.keys }
  validates :file_name, uniqueness: { scope: %i[project_id category], case_sensitive: false }
  validates :file, presence: true, unless: :exists_in_docs

  before_validation :set_file_name, if: :file
  before_save :send_to_docs, if: :file

  attr_accessor :file, :exists_in_docs

  private

  def set_file_name
    self.file_name = file.original_filename
  end

  def send_to_docs
    return unless owncloud_provider.config_valid? && project.docs_cloud?

    path = "#{project.identifier}/#{category}"
    owncloud_provider.send_file(file, path, file_name)
    find_file_id
  end

  def find_file_id
    file_infos = owncloud_provider.get_project_files(project.identifier, category)
    file_info = file_infos.detect { |info| info.file_name == file_name }
    self.docs_file_id = file_info.id
  end

  def owncloud_provider
    @owncloud_provider ||= OwncloudProvider.new('docscloud')
  end
end
