class ScheduledTimeEntry < ResourceTimeEntry
  validates :date_to, date: { after_or_equal_to: :date_from }, allow_nil: true

  scope :overlapping, lambda { |date_from, date_to|
    where(
      'DATEDIFF(resource_time_entries.date_from, :date_to) *
       DATEDIFF(:date_from, COALESCE(resource_time_entries.date_to, resource_time_entries.date_from)) >= 0',
      date_from: date_from, date_to: date_to
    )
  }
end
