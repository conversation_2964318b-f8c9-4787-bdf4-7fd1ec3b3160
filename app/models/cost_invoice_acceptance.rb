class CostInvoiceAcceptance < ApplicationRecord
  include CostInvoicesReplacementHelper
  acts_as_paranoid

  belongs_to :cost_invoice, class_name: 'Dms::CostInvoice'
  belongs_to :user, optional: true
  belongs_to :department, optional: true

  enum kind: { chief: 0, uber: 1 }

  scope :unaccepted, -> { where(accepted_at: nil) }

  def acceptors(controllers = nil)
    case kind
    when 'chief'
      chief_acceptor(department)
    when 'uber'
      uber_chief_acceptor(department)
    else
      controllers || User.active.joins(:global_roles)
                         .where(global_roles: { notify_dms_controller_acceptances: true })
                         .distinct.to_a
    end
  end
end
