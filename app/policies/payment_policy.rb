class PaymentPolicy < ApplicationPolicy
  class Scope < Scope
    def resolve
      if global_admin_programmer? || global_index? || board_member?
        scope
      else
        scope_for_responsible(scope)
      end
    end

    private

    def global_index?
      allowed_activity?('payment_schedules:global_manage') ||
        allowed_activity?('payment_schedules:global_index')
    end

    def scope_for_responsible(scope)
      base_scope = scope.left_joins(:payment_schedule, :mpk_positions)
      base_scope.where(payment_schedules: { project_id: supervised_project_ids }).or(
        base_scope.where(payment_mpk_positions: { project_id: supervised_project_ids })
      ).distinct
    end

    def supervised_users_ids
      [user.id] + User.joins(:department).where(departments: { chief_id: user.id }).pluck(:id)
    end

    def supervised_project_ids
      @supervised_project_ids ||= Membership.joins(membership_roles: :role)
                                            .where(roles: { responsible: true },
                                                   member_id: supervised_users_ids, member_type: 'User')
                                            .pluck(:project_id)
    end
  end

  def index?
    signed_in? && (board_member? || allowed_activity?('invoices:index'))
  end

  def show?
    project_policy.show_payment_schedule?
  end

  def create?
    project_policy.manage_payment_schedule?
  end

  def update?
    project_policy.manage_payment_schedule?
  end

  def manage_after_date?
    global_admin? || allowed_activity?('payment_schedules:manage_after_date')
  end

  private

  def project_policy
    ProjectPolicy.new(user, Project)
  end
end
