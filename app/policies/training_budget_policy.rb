class TrainingBudgetPolicy < ApplicationPolicy
  def index?
    allowed_activity?('training_requests:global_manage')
  end

  def show?
    allowed_activity?('training_requests:global_manage')
  end

  def create?
    allowed_activity?('training_requests:global_manage')
  end

  def update?
    allowed_activity?('training_requests:global_manage')
  end

  def destroy?
    allowed_activity?('training_requests:global_manage')
  end
end
