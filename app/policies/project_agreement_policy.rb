class ProjectAgreementPolicy < ApplicationPolicy
  ALL_AVAILABLE_ATTRIBUTES = [:content, :business_to_business, :project_id,
                              :company_id, :published_at, :confirmation_button_text,
                              :state, attachment_ids: []].freeze

  class Scope < Scope
    def resolve
      scope
    end
  end

  def index?
    signed_in? && allowed_activity?('project_agreements:index')
  end

  def selected?
    signed_in? && allowed_activity?('project_agreements:selected')
  end

  def show?
    signed_in? && allowed_activity?('project_agreements:show')
  end

  def update?
    signed_in? && allowed_activity?('project_agreements:update')
  end

  def permitted_attributes
    ALL_AVAILABLE_ATTRIBUTES
  end
end
