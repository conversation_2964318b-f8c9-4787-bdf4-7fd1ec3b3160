module B2B
  class CostInvoicePolicy < ApplicationPolicy
    bulk_permissions :update, :accept, :reject, :withdraw, :recall

    class Scope < Scope
      def resolve
        global_index? ? controller_scope : regular_user_scope
      end

      private

      def global_index?
        allowed_activity?('cost_invoices:global_manage') ||
          allowed_activity?('cost_invoices:global_index')
      end

      def controller_scope
        scope.where.not(state: :draft).or(scope.where(user: user))
      end

      def regular_user_scope
        scope.left_joins(:snapshots, contractor: { user: :department }).where(contractors: { user: user })
             .or(chief_scope).or(uber_chief_scope).or(substitute_chief_scope).or(board_member_scope)
             .or(replacement_chief_or_uber_chief_scope).distinct
      end

      def replacement_chief_or_uber_chief_scope
        scope.where.not(state: :draft).where(
          'departments.chief_id IN (:user_ids) OR departments.uber_chief_id IN (:user_ids)
           OR departments.supervisor_id IN (:user_ids)',
          user_ids: user.replaced_chiefs.map(&:id)
        )
      end

      def chief_scope
        scope.left_joins(:snapshots, contractor: { user: :department })
             .where(departments: { chief_id: user.id })
             .where.not(state: :draft)
             .where.not(contractors: { user_id: uber_or_supervisor_chiefs + [user.id] })
      end

      def substitute_chief_scope
        scope.left_joins(:snapshots, contractor: { user: :department })
             .where(departments: { substitute_chief_id: user.id })
             .where("cost_invoices.state = 4 OR (snapshots.identifier LIKE 'accept_%' AND snapshots.user_id = #{user.id})")
             .where.not(state: :draft)
             .where.not(contractors: { user_id: substitute_chiefs + [user.id] })
      end

      def uber_chief_scope
        base_scope = uber_chief_base_scope
        base_scope.where(departments: { uber_chief_id: user.id }).or(
          base_scope.where(departments: { supervisor_id: user.id })
        )
      end

      def uber_chief_base_scope
        scope.left_joins(:snapshots, contractor: { user: :department })
             .where(departments: { uber_chief_id: user.id })
             .where.not(state: :draft)
             .where.not(contractors: { user_id: supervisor_chiefs + [user.id] })
      end

      def board_member_scope
        result_scope = scope.left_joins(:snapshots, contractor: { user: :department })
        if user.board_member?
          result_scope.where.not(state: :draft).where(users: { id: uber_or_supervisor_chiefs })
        else
          result_scope.none
        end
      end

      def substitute_chiefs
        @substitute_chiefs_to_exclude ||= Department.distinct.pluck(:substitute_chief_id)
      end

      def uber_or_supervisor_chiefs
        @uber_or_supervisor_chiefs_to_exclude ||= Department.distinct.pluck(:uber_chief_id, :supervisor_id).flatten.uniq
      end

      def supervisor_chiefs
        @uber_chiefs_to_exclude ||= Department.distinct.pluck(:supervisor_id).flatten.uniq
      end
    end

    def new?
      access_to_cost_invoices?
    end

    def index?
      access_to_cost_invoices?
    end

    def show?
      access_to_cost_invoices?
    end

    def create?
      allowed_activity?('cost_invoices:global_manage') ||
        allowed_activity?('cost_invoices:global_create') ||
        user.contractor.present?
    end

    def global_create?
      allowed_activity?('cost_invoices:global_create')
    end

    def update?
      (manage_acceptation? || record.user_id == user.id) && record.editable?
    end

    def document?
      access_to_cost_invoices?
    end

    def accept?
      manage_acceptation? && record.pending_acceptation?
    end

    def reject?
      (manage_acceptation? || record.user_id == user.id) && !record.accepted?
    end

    def withdraw?
      manage_acceptation? && record.pending_acceptation?
    end

    def recall?
      manage_acceptation? && record.may_recall?
    end

    def history?
      access_to_cost_invoices?
    end

    def perform_daily_worker?
      allowed_activity?('cost_invoices:global_manage')
    end

    def ocr?
      access_to_cost_invoices?
    end

    def permitted_attributes
      attributes_list = [:company_id, :sell_date, :due_date, :invoice_date, :accounting_number_id,
                         :auto_cost_projects, :number, :document, :hours_worked, {
                           cost_invoice_positions_attributes: %i[
                             id name amount unit_price tax_rate _destroy net_value
                           ]
                         }]
      attributes_list.push(:contractor_id) if global_create?
      attributes_list
    end

    private

    def manage_acceptation?
      return false if public?

      allowed_activity?('cost_invoices:global_manage') ||
        (record.pending_department? && (manage_as_chief? || manage_as_uber? || manage_as_board? ||
         manage_as_replacement?))
    end

    def manage_as_replacement? # rubocop:disable Metrics/AbcSize
      department = contractor_user.department
      return false unless department

      user.replaced_chiefs.include?(department.chief) ||
        user.replaced_chiefs.include?(department.uber_chief) ||
        user.replaced_chiefs.include?(department.supervisor) ||
        user.replaced_chiefs.include?(department.substitute_chief)
    end

    def manage_as_chief?
      contractor_user.department&.chief_id == user.id && !self_or_uber_chief?
    end

    def manage_as_uber?
      user_in_uber_or_supervisor_role? && contractor_user.department&.chief_id == contractor_user.id &&
        !self_or_uber_chief?
    end

    def user_in_uber_or_supervisor_role?
      user.id.in?([contractor_user.department&.uber_chief_id, contractor_user.department&.supervisor_id])
    end

    def self_or_uber_chief?
      contractor_user.departments_as_uber_chief.any? || contractor_user.departments_as_supervisor.any? ||
        contractor_user == user
    end

    def manage_as_board?
      user.board_member? && contractor_user.departments_as_uber_chief.any?
    end

    def contractor_user
      @contractor_user ||= record.contractor.user
    end

    def access_to_cost_invoices?
      return false if public?

      create? || allowed_activity?('cost_invoices:global_index') ||
        user.departments_as_chief.any? || user.departments_as_uber_chief.any? ||
        user.departments_as_supervisor.any? || user.board_member?
    end
  end
end
