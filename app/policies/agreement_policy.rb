class AgreementPolicy < ApplicationPolicy
  ALL_AVAILABLE_ATTRIBUTES = [:name, :content, :confirmation_button_text, :published_at, :mandate_contract,
                              :kind, :state, :contract_of_employment, :business_to_business, :contract_work,
                              :framework_agreement, :managerial_contract,
                              { company_ids: [], department_ids: [], attachment_ids: [] }].freeze

  class Scope < Scope
    def resolve
      scope
    end
  end

  def index?
    signed_in? && allowed_activity?('agreements:index')
  end

  def show?
    signed_in? &&
      (allowed_activity?('agreements:show') || user_is_connected_with_agreement?)
  end

  def form_data?
    signed_in? && allowed_activity?('agreements:form_data')
  end

  def create?
    signed_in? && allowed_activity?('agreements:create')
  end

  def update?
    signed_in? && allowed_activity?('agreements:update')
  end

  def destroy?
    signed_in? && allowed_activity?('agreements:destroy')
  end

  def file?
    signed_in? && allowed_activity?('agreements:show')
  end

  def user_is_connected_with_agreement?
    @user.approvals.exists?(approvable: @record)
  end

  def permitted_attributes
    if global_admin?
      ALL_AVAILABLE_ATTRIBUTES
    else
      []
    end
  end
end
