class MembershipPolicy < ProjectPolicy
  ALL_AVAILABLE_ATTRIBUTES = [:member_id, :member_type, direct_role_ids: [], group_ids: [],
                                                        user_ids: []].freeze

  class Scope < Scope
    def resolve
      if global_admin? || uber_project_manager_user?
        scope
      elsif user
        if scope.to_sql =~ /memberships\.project_id/ || scope.to_sql =~ /`memberships`\.`project_id`/
          scope
        else
          # nie uzywane na froncie, to tylko <PERSON>e przed wscibskimi
          scope.of_user_and_groups(user)
        end
      else
        scope.none
      end
    end
  end

  def index?
    signed_in? && allowed_activity?('memberships:index')
  end

  def show?
    signed_in? && allowed_activity?('memberships:show')
  end

  def create?
    signed_in? && allowed_activity?('memberships:create')
  end

  def update?
    signed_in? && allowed_activity?('memberships:update')
  end

  def destroy?
    signed_in? && allowed_activity?('memberships:destroy')
  end

  def permitted_attributes
    ALL_AVAILABLE_ATTRIBUTES
  end
end
