class RolePolicy < ApplicationPolicy
  ALL_AVAILABLE_ATTRIBUTES = [:name, :ldap, :docs_ldap, :chat_ldap, :responsible, :assets_visible,
                              :cluster_keys_visible, { permissions: [] }].freeze

  class Scope < Scope
    def resolve
      scope
    end
  end

  def index?
    signed_in? && allowed_activity?('roles:index')
  end

  def show?
    signed_in? && allowed_activity?('roles:show')
  end

  def create?
    global_admin? || (uber_project_manager_user? && allowed_activity?('roles:create'))
  end

  def update?
    global_admin? || (uber_project_manager_user? && allowed_activity?('roles:update'))
  end

  def destroy?
    global_admin? || (uber_project_manager_user? && allowed_activity?('roles:destroy'))
  end

  def permitted_attributes
    if global_admin? || uber_project_manager_user?
      ALL_AVAILABLE_ATTRIBUTES
    else
      []
    end
  end
end
