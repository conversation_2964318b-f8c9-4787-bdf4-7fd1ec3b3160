class DashboardPolicy < ApplicationPolicy
  def show?
    board_member? || financial_controller? || global_admin?
  end

  def invoices
    Invoice.where(state: %i[pending accepted])
  end

  def cost_invoices
    Dms::CostInvoice.where(state: %i[pending_department pending_department_uber pending_controller for_correction])
  end

  private

  def financial_controller?
    allowed_activity?('dms-cost_invoices:global_manage') &&
      user.global_roles.where(notify_dms_controller_acceptances: true).any? &&
      allowed_activity?('payment_schedules:global_manage')
  end
end
