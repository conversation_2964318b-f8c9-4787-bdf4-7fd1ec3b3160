class EvaluationUserPolicy < ApplicationPolicy
  class Scope < Scope
    def resolve
      if user.board_member?
        scope
      else
        scope.joins(:department).where(departments: { chief_id: user.id })
      end
    end
  end

  def users_index?
    Department.where(chief: user).any? || user.board_member?
  end

  def users_show?
    access_to?(record)
  end

  def evaluations_new?
    access_to?(record)
  end

  def evaluations_create?
    access_to?(record)
  end

  def evaluations_update?
    access_to?(record)
  end

  def evaluations_show?
    access_to?(record)
  end

  def evaluations_start?
    access_to?(record)
  end

  def evaluations_stop?
    access_to?(record)
  end

  def evaluations_discard?
    access_to?(record)
  end

  private

  def access_to?(record)
    return true if user.board_member?

    department = record.department
    department && department.chief_id == user.id
  end
end
