class AssetPolicy < ApplicationPolicy
  class Scope < Scope
    def resolve
      return all if global_admin? || global_asset_manager?

      for_project_or_department
    end

    private

    def all
      scope.order(Arel.sql('assets.expiry_date desc, ISNULL(assets.expiry_date)'))
    end

    def for_project_or_department
      pm_projects_ids = user.memberships
                            .joins(:roles)
                            .where(roles: { assets_visible: true }).pluck(:project_id)

      scope.not_rejected.where(project_id: pm_projects_ids).or(
        scope.where(user_id: users_from_departments_ids, project_id: nil)
      ).order(Arel.sql('assets.expiry_date desc, ISNULL(assets.expiry_date)'))
    end

    def users_from_departments_ids
      User.where(
        department_id:
          user.departments_as_chief_ids + user.departments_as_uber_chief_ids + user.departments_as_supervisor_ids
      )
    end
  end

  def stats?
    allowed_activity?('assets:index')
  end

  def index?
    allowed_activity?('assets:index')
  end

  def show?
    allowed_activity?('assets:show')
  end

  def history?
    allowed_activity?('assets:show')
  end

  def update?
    allowed_activity?('assets:manage')
  end

  def create?
    allowed_activity?('assets:create')
  end

  def assets_requests_index?
    allowed_activity?('assets:manage') || user.departments_as_chief.present? || user.departments_as_uber_chief.present?
  end

  def my_request?
    allowed_activity?('assets:create')
  end

  def my_projects_assets?
    allowed_activity?('assets:manage')
  end

  def asset_orders?
    allowed_activity?('assets:orders')
  end

  def allow_execution?
    allowed_activity?('assets:allow_execution') || manage_request_as_chief?
  end

  def activate?
    allowed_activity?('assets:manage')
  end

  def reject?
    allowed_activity?('assets:manage') || manage_request_as_chief?
  end

  def close?
    allowed_activity?('assets:manage')
  end

  def decommission?
    allowed_activity?('assets:manage_all')
  end

  private

  def manage_request_as_chief?
    department = record.user&.department
    return unless department

    !record.project_id && chief_or_uber?(department)
  end

  def chief_or_uber?(department)
    department.chief_id == user.id || department.uber_chief_id == user.id || department.supervisor_id == user.id
  end
end
