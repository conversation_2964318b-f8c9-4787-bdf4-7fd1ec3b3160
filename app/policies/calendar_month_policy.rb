class CalendarMonthPolicy < ApplicationPolicy
  class Scope < Scope
    def resolve
      if allowed_activity?('calendar_months:global_manage')
        scope
      elsif department_chef_or_uber?
        scope.joins(:user)
             .where(users: { department_id: user.departments_as_chief.or(
               user.departments_as_uber_chief
             ).or(user.departments_as_supervisor).ids })
      end
    end

    def users_scope
      if allowed_activity?('calendar_months:global_manage')
        scope
      elsif department_chef_or_uber?
        scope.where(department_id: user.departments_as_chief.or(user.departments_as_uber_chief).or(
          user.departments_as_supervisor
        ).ids)
      end
    end
  end

  def index?
    allowed_activity?('calendar_months:global_manage') || department_chef_or_uber?
  end

  def form_data?
    allowed_activity?('calendar_months:global_manage') || department_chef_or_uber?
  end

  def bulk_update?
    allowed_activity?('calendar_months:global_manage') || department_chef_or_uber?
  end

  def update? # rubocop:disable Metrics/AbcSize
    if Date.new(record.year, record.month, -1) < Date.current
      allowed_activity?('calendar_months:global_manage')
    else
      allowed_activity?('calendar_months:global_manage') ||
        user.departments_as_chief.or(user.departments_as_uber_chief).or(
          user.departments_as_supervisor
        ).include?(record.user.department)
    end
  end
end
