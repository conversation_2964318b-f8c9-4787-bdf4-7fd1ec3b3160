class PositionPolicy < ApplicationPolicy
  class Scope < Scope
    def resolve
      scope
    end
  end

  def index?
    signed_in? && allowed_activity?('positions:index')
  end

  def create?
    signed_in? && allowed_activity?('positions:create')
  end

  def update?
    signed_in? && allowed_activity?('positions:update')
  end

  def show?
    signed_in? && allowed_activity?('positions:show')
  end

  def destroy?
    signed_in? && allowed_activity?('positions:destroy')
  end
end
