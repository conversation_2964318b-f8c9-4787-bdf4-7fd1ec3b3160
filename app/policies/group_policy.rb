class GroupPolicy < ApplicationPolicy
  ALL_AVAILABLE_ATTRIBUTES = [:name, :redmine_id, :chat, { user_ids: [] }].freeze

  class Scope < Scope
    def resolve
      if global_admin? || privileged_user?
        scope
      else
        scope.where(id: user.group_ids)
      end
    end
  end

  def index?
    signed_in? && allowed_activity?('groups:index')
  end

  def show?
    signed_in? && allowed_activity?('groups:show')
  end

  def create?
    signed_in? && allowed_activity?('groups:create')
  end

  def update?
    signed_in? && allowed_activity?('groups:update')
  end

  def destroy?
    signed_in? && allowed_activity?('groups:destroy')
  end

  def permitted_attributes
    if global_admin?
      ALL_AVAILABLE_ATTRIBUTES
    else
      ALL_AVAILABLE_ATTRIBUTES.reject { |i| [:name].include?(i) }
    end
  end
end
