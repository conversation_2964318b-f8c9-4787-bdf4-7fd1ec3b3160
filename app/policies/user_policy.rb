class UserPolicy < ApplicationPolicy
  ALL_AVAILABLE_ATTRIBUTES = [:first_name, :last_name, :email, :company_id, :position_id, :state,
                              :password, :password_confirmation, :cloud, :docs_cloud, :absence_quota, :absence_balance,
                              :sick_absence_quota, :redmine, :chat, :system, :monitoring, :svn, :activates_on,
                              :position_id, :remote, :time_reports_not_required, :profile_comment,
                              :part_time, :remote_allowed, :remote_yearly_limit, :activity_validation_disabled,
                              :contract_of_employment,
                              { global_role_ids: [], group_ids: [], department_ids: [],
                                cards_attributes: %i[id company_id code active expires_on] }].freeze

  class Scope < Scope
    def resolve
      if global_admin? || privileged_user?
        scope
      else
        group_user_ids = user.groups.select('users.id AS group_user_id').joins(:users).group('group_user_id').map(&:group_user_id)
        scope.where(id: group_user_ids.any? ? group_user_ids : user.id)
      end
    end
  end

  def index?
    signed_in? && (board_member? || allowed_activity?('users:index'))
  end

  def show?
    signed_in? && (board_member? || allowed_activity?('users:show'))
  end

  def history?
    allowed_activity?('users:history')
  end

  def create?
    signed_in? && allowed_activity?('users:create')
  end

  def update?
    signed_in? && allowed_activity?('users:update')
  end

  def resend_confirmation_instructions?
    signed_in? && allowed_activity?('users:resend_confirmation_instructions')
  end

  def destroy?
    signed_in? && !record.confirmed? && (global_admin? || global_admin_programmer? || allowed_activity?('users:destroy'))
  end

  def impersonate?
    global_admin_programmer?
  end

  def permitted_attributes # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/MethodLength, Metrics/PerceivedComplexity
    if global_admin?
      ALL_AVAILABLE_ATTRIBUTES
    else
      allowed_attributes = ALL_AVAILABLE_ATTRIBUTES.dup
      allowed_attributes.delete(:company_id) unless global_admin? || hr_user?
      allowed_attributes.delete(:activity_validation_disabled) unless global_admin?
      allowed_attributes.reject! do |attribute|
        !allowed_activity?('users:update_password') &&
          %i[password password_confirmation].include?(attribute)
      end
      allowed_attributes = allowed_attributes.reject do |attribute|
        !allowed_activity?('groups:update') && [:group_ids].include?(attribute)
      end
      allowed_attributes = allowed_attributes.reject do |attribute|
        !hr_user? && %i[state absence_quota absence_balance sick_absence_quota department_ids].include?(attribute)
      end
      allowed_attributes.reject do |attribute|
        !privileged_user? && %i[global_role_ids cloud docs_cloud].include?(attribute)
      end

    end
  end
end
