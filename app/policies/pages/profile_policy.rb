module Pages
  class ProfilePolicy < ::UserPolicy
    ALL_AVAILABLE_ATTRIBUTES = %i[password password_confirmation owncloud_password
                                  delete_owncloud_password dev_password delete_dev_password
                                  dismiss_onboarding].freeze

    def index?
      raise NotImplementedError
    end

    def show?
      user == record.user
    end

    def create?
      raise NotImplementedError
    end

    def update?
      user == record.user
    end

    alias save_preferences? update?
    alias dismiss_onboarding? update?

    def destroy?
      raise NotImplementedError
    end

    def permitted_attributes(company = nil)
      if signed_in?
        banned_attributes = %i[state global_role_ids group_ids company_id redmine_id]
        if company
          ALL_AVAILABLE_ATTRIBUTES.reject { |attribute| (banned_attributes + [:email]).include?(attribute) }
        else
          ALL_AVAILABLE_ATTRIBUTES.reject { |attribute| banned_attributes.include?(attribute) }
        end
      else
        []
      end
    end
  end
end
