class BookingResourcePolicy < ApplicationPolicy
  def index?
    signed_in? && allowed_activity?('booking_resources:index')
  end

  def create?
    signed_in? && allowed_activity?('booking_resources:create')
  end

  def update?
    signed_in? && allowed_activity?('booking_resources:update')
  end

  def show?
    signed_in? && allowed_activity?('booking_resources:show')
  end

  def destroy?
    signed_in? && allowed_activity?('booking_resources:destroy')
  end

  def form_data?
    signed_in? && allowed_activity?('booking_resources:form_data')
  end
end
