class DepartmentPolicy < ApplicationPolicy
  ALL_AVAILABLE_ATTRIBUTES = %i[name company_id locked chief_id substitute_chief_id
                                uber_chief_id supervisor_id time_reports_not_required mpk_number_id role_id].freeze

  class Scope < Scope
    def resolve
      if global_admin? || privileged_user?
        scope
      else
        scope.includes(:users).where(users: { id: user.id })
      end
    end
  end

  def index?
    signed_in? && allowed_activity?('departments:index')
  end

  def show?
    (signed_in? && record_related_via_company?) || global_admin? || allowed_activity?('departments:show')
  end

  def create?
    global_admin? || allowed_activity?('departments:create')
  end

  def update?
    global_admin? || allowed_activity?('departments:update')
  end

  def destroy?
    global_admin? || (hr_user? && allowed_activity?('departments:destroy'))
  end

  def permitted_attributes
    global_admin? ? ALL_AVAILABLE_ATTRIBUTES : []
  end

  def show_training_stats?
    return true if allowed_activity?('training_requests:global_manage')

    chief_or_uber?
  end

  private

  def chief_or_uber?
    user_id = user.id
    record.substitute_chief_id == user_id || record.chief_id == user_id || record.uber_chief_id == user_id ||
      record.supervisor_id == user_id
  end

  def record_related_via_company?
    user && record.respond_to?(:id) && record.company_id == user.company_id
  end
end
