class ContractorPolicy < ApplicationPolicy
  class Scope < Scope
    def resolve
      scope.all
    end
  end

  def index?
    allowed_activity?('contractors:index')
  end

  def show?
    manage?
  end

  def create?
    allowed_activity?('contractors:create')
  end

  def update?
    manage?
  end

  def activate?
    manage?
  end

  def destroy?
    global_admin? && allowed_activity?('contractors:destroy')
  end

  private

  def manage?
    allowed_activity?('contractors:manage')
  end
end
