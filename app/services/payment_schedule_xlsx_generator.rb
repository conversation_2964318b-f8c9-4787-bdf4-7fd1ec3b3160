class PaymentScheduleXlsxGenerator
  def initialize(payment_schedule, payments)
    @payment_schedule = payment_schedule
    @payments = payments.includes(:mpk_positions)
  end

  def generate
    p = Axlsx::Package.new
    wb = p.workbook
    add_styles(wb)
    add_worksheet(wb)

    p.to_stream
  end

  private

  def add_worksheet(workbook)
    workbook.add_worksheet(name: @payment_schedule.project.identifier.truncate(30)) do |sheet|
      add_header(sheet)

      @payments.each do |payment|
        add_payment(sheet, payment)
      end

      add_dimensions(sheet)
      add_pane(sheet)
    end
  end

  def add_header(sheet) # rubocop:disable Metrics/AbcSize
    sheet.add_row(['Issued on', 'Sell date', 'Predicted amount', 'Currency', 'Description',
                   'MPK numbers', *Array.new(mpk_numbers.length - 1, ''),
                   'Cyclic', 'Cycle length', 'Ends on', 'Autogenerated', 'Shared'],
                  style: @header)
    mpk_number_columns = ('F'..).first(mpk_numbers.length)
    sheet.merge_cells("#{mpk_number_columns.first}#{current_index(sheet)}:#{mpk_number_columns.last}#{current_index(sheet)}")
    sheet.add_row(mpk_numbers.map(&:key), offset: 5, style: @header)
    sheet.add_row(mpk_numbers.map(&:name), offset: 5, style: @header)
    (column_names - mpk_number_columns).each do |column|
      sheet.merge_cells("#{column}#{current_index(sheet) - 2}:#{column}#{current_index(sheet)}")
    end
  end

  def add_payment(sheet, payment) # rubocop:disable Metrics/AbcSize
    sheet.add_row([payment.issued_on, payment.sell_date, payment.predicted_amount / 100.0, payment.currency,
                   payment.description, *mpk_divisions(payment), boolean_value(payment.cyclic), payment.cycle_length,
                   payment.ends_on, boolean_value(payment.originator_id.present?),
                   boolean_value(payment.payment_schedule_id != @payment_schedule.id)],
                  style: [@date, @date, @amount, @text, @text, *Array.new(mpk_numbers.length, @amount), @text, @text,
                          @date, @text, @text])
  end

  def column_names
    @column_names ||= ('A'..).first(mpk_numbers.length + 11)
  end

  def mpk_numbers
    @mpk_numbers ||= MpkNumber.all.to_a.sort_by { |n| n.key.to_i }
  end

  def boolean_value(value)
    value ? 'TRUE' : 'FALSE'
  end

  def mpk_divisions(payment)
    mpk_numbers.map do |mpk_number|
      positions = payment.mpk_positions.select do |mp|
        mp.mpk_number_id == mpk_number.id
      end
      next if positions.empty?

      amount_for_mpk_number(positions)
    end
  end

  def amount_for_mpk_number(positions)
    positions.sum(0, &:amount) / 100.0
  end

  def add_styles(workbook)
    workbook.styles do |s|
      default = { font_name: 'Arial', sz: 11, border: { style: :thin, color: '000000' } }
      standard = default.merge(bg_color: 'f5f5f5')

      @header = s.add_style(default.merge(b: true, bg_color: 'e3e3e3',
                                          alignment: { horizontal: :center, vertical: :center, wrap_text: true }))
      @text = s.add_style(standard)
      @date = s.add_style(standard.merge(format_code: 'YYYY-MM-DD'))
      @amount = s.add_style(standard.merge(format_code: '#,##0.00'))
    end
  end

  def add_dimensions(sheet)
    sheet.column_widths(15, 15, 20, 15, 30,
                        *Array.new(mpk_numbers.length, 15),
                        15, 15, 15, 15, 15, 20)

    sheet.rows[0].height = 25
    sheet.rows[1].height = 20
    sheet.rows[2].height = 20
  end

  def add_pane(sheet)
    sheet.sheet_view.pane do |pane|
      pane.top_left_cell = 'A4'
      pane.state = :frozen_split
      pane.y_split = 3
    end
  end

  def current_index(sheet)
    sheet.rows.length
  end
end
