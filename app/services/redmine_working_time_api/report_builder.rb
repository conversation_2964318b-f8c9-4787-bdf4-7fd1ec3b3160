module RedmineWorkingTimeApi
  class ReportBuilder
    include RedmineWorkingTimeApi::Concerns::BuilderUtils
    include ClickUpWorkingTimeApi::Concerns::BuilderUtils

    def self.generate_summary(*args)
      new(*args).generate_summary
    end

    def initialize(year, month, company, scope = nil)
      @year = year
      @month = month
      @company = company
      @scope = scope
      @click_up_counter = 0
    end

    def generate_summary
      summarized_data = summarize_employees(fetch_data)
      workbook = Spreadsheet::Workbook.new
      add_worksheet(workbook, summarized_data)

      workbook
    end

    private

    def fetch_data
      importer.month(@month, @year).import
    end

    def fetch_click_up_data(user)
      @click_up_counter += 1
      sleep(60) if (@click_up_counter % 50).zero?

      period = time_period(user, @month, @year)
      options = range_options(period.from, period.to).merge(assignee_options(user))
      data = fetch_click_up_time_entries(@company.click_up_workspace_id, options)
      data = filter_entries_with_folder_id(data)
      filter_with_current_range(data, options[:start_date], options[:end_date])
    end

    def summarize_employee(data, user)
      click_up_data, total_click_up_hours = process_click_up(user)
      selected_entries = select_entries(data, user.username)
      total_redmine_hours = sum_hours(selected_entries)

      hours_per_project = reduce_project_hours(selected_entries + click_up_data)

      total_reported_hours = total_redmine_hours + total_click_up_hours
      add_missing_hours_if_needed(user, total_reported_hours, hours_per_project)
      hours_per_project
    end

    def process_click_up(user)
      return [[], 0] unless user.click_up_id && @company.click_up_workspace_id.present?

      data = fetch_click_up_data(user)
      total = sum_click_up_hours(data)
      [data, total]
    end

    def add_missing_hours_if_needed(user, total_reported_hours, project_hours)
      missing_hours = user.monthly_working_hours(@year, @month) - total_reported_hours
      return unless missing_hours.positive? && !user.part_time?

      project_hours[placeholder_project_identifier(user)] = missing_hours
    end

    def select_entries(data, username)
      data.select { |entry| entry['user']['login'] == username }
    end

    def scope
      start_date = Date.new(@year, @month, 1)
      scope = @scope || User.internal.joins(:user_contracts).merge(user_contracts_scope(start_date))
                            .where(company: @company)
                            .where('activates_on <= ?', start_date.end_of_month)
                            .distinct
      scope.includes(department: :mpk_number)
    end

    def user_contracts_scope(date)
      # We treat mandate and contract_work as employment for the purpose of this report
      UserContract.overlapping(date, date.end_of_month)
                  .where(agreement_type: %i[employment mandate contract_work framework_agreement managerial_contract])
    end

    def summarize_employees(data)
      summary = scope.map do |emp|
        [emp, summarize_employee(data, emp)]
      end

      summary.select { |_user, employer_summary| employer_summary }
    end

    def add_worksheet(workbook, summarized_data)
      sheet = workbook.create_worksheet(name: 'Czas_zestawienia')
      add_row(sheet, 0, %w[Kod ID Nazwisko Imie PozycjaZestawienia DataOd DataDo CzasPracyGodz
                           CzasPracyDni Nadgodziny50 Nadgodziny100 GodzinyNocne Strefa Wydzial
                           Wydzial_adres_wezla Projekt Projekt_adres_wezla])
      fill_summarized_data(sheet, summarized_data)
    end

    def add_row(sheet, index, row)
      sheet.row(index).concat(row)
    end

    def fill_summarized_data(sheet, summarized_data)
      index = 0
      summarized_data.each do |user, hours_per_project|
        next if hours_per_project.empty?

        hours_per_accounting_number(hours_per_project).each do |accounting_number, hours|
          row = build_row(user, index, accounting_number, hours)
          add_row(sheet, index + 1, row)
          index += 1
        end
      end
    end

    def hours_per_accounting_number(hours_per_project)
      data = Hash.new(0)
      hours_per_project.each do |identifier, hours|
        project = Project.find_by!(identifier: identifier)
        accounting_number = project.accounting_number

        data[accounting_number] += hours
      end
      data
    end

    def build_row(user, index, accounting_number, hours)
      [user.username.upcase, user.id, user.last_name, user.first_name, index, date_from,
       date_to, working_time(hours), user.monthly_working_days(@year, @month), '0:00', '0:00',
       '0:00', 'praca.pdst', user.mpk_number.try(:key), nil, accounting_number&.number, nil]
    end

    def working_time(floaty_hours)
      hours, minutes = (floaty_hours * 60).round.divmod(60)
      format('%02d:%02d', hours, minutes)
    end

    def date_from
      @date_from ||= Date.new(@year, @month)
    end

    def date_to
      @date_to ||= date_from.end_of_month
    end
  end
end
