module RedmineWorkingTimeApi
  class Importer
    DEFAULT_STEP = 1000
    RESPONSE_KEY = 'time_entries'.freeze

    include RedmineImporter
    include HTTParty

    read_timeout 1800
    open_timeout 1800

    base_uri Settings.redmine_working_time.base_url if Settings.redmine_working_time
    attr_reader :options

    def initialize
      super
      set_default_options
    end

    def http
      self.class
    end

    def custom_headers
      headers = {
        'User-Agent' => 'timereports_importer',
        'X-Redmine-Timeapi-Key' => Settings.redmine_working_time.api_key
      }
      host = Settings.redmine_working_time.host_header
      headers['Host'] = host if host
      headers
    end

    def between(start, finish)
      merge_options build_redmine_options(:spent_on, '><', start, finish)
    end

    def month(month, year = nil)
      year ||= Time.zone.today.year
      start = Date.new(year, month, 1)
      finish = start.end_of_month
      between(start, finish)
    end

    def project(redmine_project_id)
      merge_options build_redmine_options(:project_id, '=', redmine_project_id)
    end

    def issue(redmine_issue_id)
      merge_options build_redmine_options(:issue_id, '=', redmine_issue_id)
    end

    def login(username)
      merge_options build_redmine_options(:login, '=', username)
    end

    private

    def set_default_options
      basic_auth = Settings.redmine_working_time.basic_auth
      return if basic_auth.blank?

      @options[:basic_auth] = basic_auth.to_h.symbolize_keys
    end

    def fetch(additional_options = {})
      http.get('/timeapi.json', headers: custom_headers,
                                query: @options.merge(additional_options)).tap do |resp|
        Rails.logger.info("fetched #{resp[RESPONSE_KEY].size}")
      end
    end
  end
end
