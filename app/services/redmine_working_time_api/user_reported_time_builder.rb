require 'date_utils'

module RedmineWorkingTimeApi
  module UserReportedTimeBuilder
    class << self
      include EmployeesHoursHelper

      def generate_summary(user, month = nil, year = nil)
        return unless user

        selected_year  = year || Time.zone.today.year
        selected_month = month || Time.zone.today.month

        period        = time_period(user, selected_month, selected_year)
        required_time = required_reported_time(user, period)
        reported_time = actual_reported_time(selected_year, selected_month, user.username)
        filled        = reported_time >= required_time

        OpenStruct.new(
          reported: reported_time, required: required_time,
          date_from: period.from, date_to: period.to,
          issuer_redmine_id: user.redmine_id, filled?: filled
        )
      end

      private

      def importer
        RedmineWorkingTimeApi::Importer.new
      end

      def fetch_data(year, month, username)
        importer.month(month, year).login(username).import
      end

      def actual_reported_time(year, month, username)
        data = fetch_data(year, month, username)
        reject_overtime(data).sum { |x| x['hours'] }.round(2)
      end
    end
  end
end
