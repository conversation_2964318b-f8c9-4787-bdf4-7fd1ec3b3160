module MicrosoftGraphApi
  class Base
    class << self
      private

      SCOPES = ['https://graph.microsoft.com/.default'].freeze

      def client
        context = MicrosoftKiotaAuthenticationOAuth::ClientCredentialContext.new(credentials.tenant_id, credentials.client_id,
                                                                                 credentials.client_secret)
        authentication_provider = MicrosoftGraphCore::Authentication::OAuthAuthenticationProvider.new(context, nil, SCOPES)
        adapter = MicrosoftGraph::GraphRequestAdapter.new(authentication_provider)

        MicrosoftGraph::GraphServiceClient.new(adapter)
      end

      def credentials
        @credentials ||= Settings.microsoft_graph_api
      end
    end
  end
end
