module HolidayRequests
  class AdjustHolidayRequestParams
    class Conditional
      # NOTE: the order is important
      CONDITIONS =
        {
          handle_niedostepnosc_ch: [
            :holiday_request_params__convert_to__equal__niedostepnosc_ch?
          ],
          handle_niedostepnosc_o: [
            :holiday_request_params__convert_to__equal__niedostepnosc_o?
          ],
          handle_accept: [
            :holiday_request_params__accept__equal__true?
          ],
          handle_reject: [
            :holiday_request_params__reject__equal__true?
          ],
          handle_else: [
            :true?
          ]
        }.freeze

      def initialize(opts = {})
        @answers_cache = {}
        @params = opts.fetch(:params)
      end

      def call
        CONDITIONS.detect do |aggregate|
          questions = aggregate.last
          questions.all? { |question| ask(question) }
        end.try(:first)
      end

      private

      attr_reader :params

      def ask(question)
        if @answers_cache.key?(question)
          @answers_cache[question]
        else
          @answers_cache[question] = send(question)
        end
      end

      def holiday_request_params__convert_to__equal__niedostepnosc_ch?
        params[:convert_to] == 'Niedostępność/Ch'
      end

      def holiday_request_params__convert_to__equal__niedostepnosc_o?
        params[:convert_to] == 'Niedostępność/O'
      end

      def holiday_request_params__accept__equal__true?
        params[:accept].to_s == 'true'
      end

      def holiday_request_params__reject__equal__true?
        params[:reject].to_s == 'true'
      end

      def true?
        true
      end
    end
    private_constant :Conditional

    include Base::PersistableService

    def call
      @mutated_params = params.dup
      method_to_call = Conditional.new(all_opts).call
      send(method_to_call) if method_to_call
    end

    private

    def handle_niedostepnosc_ch
      mutate_params!(HolidayRequest.convert_to_sick_params)
      add_examiner_params!
    end

    def handle_niedostepnosc_o
      mutate_params!(HolidayRequest.convert_to_occasional_params)
      add_examiner_params!
    end

    def handle_accept
      mutate_params!(HolidayRequest.accept_params)
      add_examiner_params!
    end

    def handle_reject
      mutate_params!(HolidayRequest.reject_params)
      add_examiner_params!
    end

    def handle_else
      @mutated_params
    end

    def mutate_params!(hash_to_merge)
      @mutated_params.merge!(hash_to_merge)
    end

    def add_examiner_params!
      @mutated_params.merge!(examiner_id: actor.id)
    end
  end
end
