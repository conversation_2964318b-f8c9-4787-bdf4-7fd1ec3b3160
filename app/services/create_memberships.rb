class CreateMemberships
  # http://geekdirt.com/blog/nested-transations-in-rails/
  def call(project, membership_params)
    bulk_params = prepare_bulk_params(membership_params)
    membership = nil
    bulk_params.map do |params|
      membership = project.memberships.find_or_initialize_by(params)
      membership.assign_attributes(membership_params)
      return membership if membership.invalid?

      membership
    end.each(&:save)
    membership
  end

  def prepare_bulk_params(membership_params)
    group_ids = membership_params.delete(:group_ids)
    user_ids = membership_params.delete(:user_ids)
    prepare_bulk_user_params(user_ids) + prepare_bulk_group_params(group_ids)
  end

  def prepare_bulk_user_params(user_ids)
    return [] unless user_ids.is_a?(Array)

    user_ids.map do |id|
      { member_id: id, member_type: 'User' }
    end
  end

  def prepare_bulk_group_params(group_ids)
    return [] unless group_ids.is_a?(Array)

    group_ids.map do |id|
      { member_id: id, member_type: 'Group' }
    end
  end
end
