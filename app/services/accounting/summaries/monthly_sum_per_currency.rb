module Accounting
  module Summaries
    class MonthlySumPerCurrency
      def initialize(project)
        @project = project
      end

      def call
        scheduled_payments = ScheduledPayment.select(
          'SUM(amount) AS amount, currency, EXTRACT(YEAR_MONTH FROM issued_on) AS payment_month'
        ).where(project_id: @project.id).group('currency, payment_month').order('payment_month DESC')
        payments_hash = scheduled_payments.map do |month_currency|
          {
            amount: month_currency.amount,
            payment_month: month_currency.payment_month,
            currency: month_currency.currency
          }
        end
        payments_hash.group_by { |x| x[:payment_month] }
      end
    end
  end
end
