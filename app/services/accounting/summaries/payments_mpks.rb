module Accounting
  module Summaries
    class PaymentsMpks
      attr_reader :payments

      def initialize(project)
        @project = project
      end

      def call
        summary
      end

      private

      def summary # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
        result = summary_query.map do |mpk_number|
          {
            id: mpk_number.id,
            mpk_name: mpk_number.name,
            mpk_key: mpk_number.key,
            currency: Payment.currencies.map(&:reverse).to_h[mpk_number.currency],
            paid: mpk_number.issued_sum,
            scheduled_payments: mpk_number.scheduled_sum,
            balance: mpk_number.balance,
            issued_on: mpk_number.payment_month
          }
        end

        result.group_by { |x| x[:issued_on] }
      end

      def select_clause
        <<~SQL.squish
          mpk_numbers.id, mpk_numbers.name, mpk_numbers.key,
          COALESCE(scheduled.payment_month, completed.payment_month) AS payment_month,
          COALESCE(scheduled.currency, completed.currency) AS currency,
          COALESCE(scheduled.amount, 0) AS scheduled_sum,
          COALESCE(completed.amount, 0) AS issued_sum,
          COALESCE(completed.amount, 0) - COALESCE(scheduled.amount, 0) AS balance
        SQL
      end

      def where_condition
        <<~SQL.squish
          WHERE issued_on <= '#{Arel.sql(Time.zone.now.end_of_month.to_date.to_fs(:db))}'
          AND project_id = #{@project.id}
        SQL
      end

      def join_clause(*tables) # rubocop:disable Metrics/MethodLength
        tables.map.with_index do |table, index|
          clause = <<~SQL.squish
            LEFT JOIN (
              SELECT id, EXTRACT(YEAR_MONTH FROM issued_on) AS payment_month, mpk_number_id, currency,
              SUM(amount) AS amount
              FROM #{table}_payments
              #{where_condition}
              GROUP BY EXTRACT(YEAR_MONTH FROM issued_on), mpk_number_id, currency
            ) AS #{table} ON #{table}.mpk_number_id = mpk_numbers.id
          SQL
          if index.positive?
            clause += "
              AND #{table}.currency = #{tables.first}.currency
              AND #{table}.payment_month = #{tables.first}.payment_month
            "
          end
          clause
        end.join(' ')
      end

      def summary_query
        scheduled_left_relation = MpkNumber.select(select_clause).joins(join_clause(:scheduled, :completed))
        completed_left_relation = MpkNumber.select(select_clause).joins(join_clause(:completed, :scheduled))

        results = <<~SQL.squish
          SELECT * from scheduled_left_relation UNION SELECT * FROM completed_left_relation
        SQL

        MpkNumber.with(scheduled_left_relation:, completed_left_relation:, results:)
                 .joins('INNER JOIN results ON results.id = mpk_numbers.id')
                 .select('results.*')
                 .order(payment_month: :desc)
      end
    end
  end
end
