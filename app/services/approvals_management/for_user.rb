module ApprovalsManagement
  class ForUser
    attr_reader :user
    delegate :approvals, to: :user

    def initialize(user)
      @user = user
    end

    def call
      Approval.transaction do
        if user.active?
          user.approvals.create!(
            to_create_approvables.map do |id|
              { approvable_id: id.first, approvable_type: id.last }
            end
          )
        end
        remove_obsolete_user_approvals(obsolete_approvables)
      end
    end

    private

    def remove_obsolete_user_approvals(obsolete_approvables)
      if user.locked?
        not_accepted_approvals.destroy_all
        return
      end
      agreement_approvable_ids, project_agreement_approvable_ids = obsolete_approvable_ids(obsolete_approvables)
      not_accepted_approvals.where(
        approvable_id: agreement_approvable_ids, approvable_type: 'Agreement'
      ).destroy_all
      not_accepted_approvals.where(
        approvable_id: project_agreement_approvable_ids, approvable_type: 'ProjectAgreement'
      ).destroy_all
    end

    def not_accepted_approvals
      approvals.not_accepted
    end

    def to_create_approvables
      required_approvables - existing_approvables
    end

    def obsolete_approvables
      existing_approvables - required_approvables
    end

    def obsolete_approvable_ids(obsolete_approvables)
      agreement_approvables_ids = obsolete_approvables.select { |approvable| approvable.last == 'Agreement' }
                                                      .map(&:first)
      project_agreement_approvables_ids = obsolete_approvables.select { |approvable| approvable.last == 'ProjectAgreement' }
                                                              .map(&:first)
      [agreement_approvables_ids, project_agreement_approvables_ids]
    end

    def existing_approvables
      @existing_approvables ||= user.approvals.pluck(:approvable_id, :approvable_type)
    end

    def required_approvables
      required_agreements = fetch_required_agreements
      required_project_agreements = fetch_required_project_agreements
      @required_approvables ||= required_agreements + required_project_agreements
    end

    def contract_type_params
      key = user.contract_of_employment ? :contract_of_employment : :business_to_business
      { key => true }
    end

    def fetch_required_agreements
      required_agreement_ids = Agreement.joins(:companies, :departments)
                                        .published
                                        .where({
                                          companies: { id: user.company_id },
                                          departments: { id: user.department_id }
                                        }.merge(contract_type_params))
                                        .pluck(:id)
      required_agreement_ids.map { |id| [id, 'Agreement'] }
    end

    def fetch_required_project_agreements
      required_project_agreement_ids = ProjectAgreement.joins(project: :memberships)
                                                       .published
                                                       .where(company_id: user.company_id,
                                                              memberships: { member_id: user.id, member_type: 'User' })
                                                       .where.not(business_to_business: user.contract_of_employment)
                                                       .pluck(:id)
      required_project_agreement_ids.map { |id| [id, 'ProjectAgreement'] }
    end
  end
end
