module ApprovalsManagement
  class ForProjectAgreement
    attr_reader :project_agreement

    def initialize(project_agreement)
      @project_agreement = project_agreement
    end

    def call
      Approval.transaction do
        project_agreement.approvals.create!(to_create_user_ids.map { |id| { user_id: id } })
        project_agreement.approvals.not_accepted.where(user_id: obsolete_user_ids).destroy_all
      end
    end

    private

    def to_create_user_ids
      required_user_ids - existing_user_ids
    end

    def obsolete_user_ids
      existing_user_ids - required_user_ids
    end

    def existing_user_ids
      @existing_user_ids ||= project_agreement.approvals.pluck(:user_id)
    end

    def required_user_ids
      @required_user_ids ||= User.active.joins(:memberships)
                                 .where(memberships: { project_id: project_agreement.project_id },
                                        company_id: project_agreement.company_id)
                                 .where.not(contract_of_employment: project_agreement.business_to_business)
                                 .distinct.pluck(:id)
    end
  end
end
