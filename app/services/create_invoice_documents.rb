class CreateInvoiceDocuments
  def call(date)
    return unless share_path

    Company.native.each do |company|
      file_paths(company, date).each do |file_path|
        invoice = find_invoice(company, file_path)

        next unless invoice

        create_invoice_document(invoice, file_path)
        send_to_client(invoice)
      end
    end
  end

  private

  def find_invoice(company, file_path)
    file_name = File.basename(file_path, File.extname(file_path))
    invoice_number_regex = "^#{file_name.sub('_', '[-\\/]').tr('_', '\/')}$"
    invoice = Invoice.accepted.of_company(company)
                     .find_by('invoices.number REGEXP ?', invoice_number_regex)

    invoice if invoice && !invoice.invoice_document
  end

  def create_invoice_document(invoice, file_path)
    invoice.create_invoice_document(document: File.open(file_path, binmode: true))
  end

  def send_to_client(invoice)
    user = User.find_by(id: Settings.inbox_client_id) if Settings.inbox_client_id
    invoice.issue!(user) if invoice.electronic_invoice_sending_method? && invoice.invoice_document
  end

  def file_paths(company, date)
    Dir[File.join(directory_path(company), '**', '*.{pdf,PDF}')].select do |file_path|
      File.ctime(file_path).getutc.to_date == date
    end
  end

  def directory_path(company)
    File.join(share_path, company.name, 'KSIĘGOWOŚĆ', 'sprzedaż')
  end

  def share_path
    @share_path ||= Settings.invoices_integration.estelligence_share_path
  end
end
