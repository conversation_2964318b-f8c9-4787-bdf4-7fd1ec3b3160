# rubocop:disable Metrics/BlockLength,Metrics/MethodLength,Metrics/AbcSize,Metrics/ModuleLength

module DictionariesGenerator
  class << self
    def generate(company)
      xml = Builder::XmlMarkup.new
      xml.ROOT do
        workers(company, xml)
        departments(company, xml)
        projects(company, xml)
        categories(company, xml)
        clients(company, xml)
        definitions(company, xml)
      end
    end

    private

    def definitions(company, xml)
      xml.DEFINICJE_DOKUMENTOW do
        xml.WERSJA '2.00'
        xml.BAZA_ZRD_ID company.name == 'Efigence' ? 'SPEFI' : 'SPART'
        xml.BAZA_DOC_ID company.name == 'Efigence' ? 'KSEFI' : 'KSART'
        xml.DEFINICJA_DOKUMENTU do
          xml.ID_ZRODLA 1
          xml.KLASA 'Faktura sprzedaży'
          xml.SYMBOL
          xml.NAZWA 'Faktura sprzedaży'
          xml.NUMERACJA '@rok_kal@miesiac@numerS@rejestr'
          xml.SERIA_OPEARTORA 'Nie'
          xml.NIEAKTYWNA 'Nie'
        end
      end
    end

    def categories(company, xml)
      xml.KATEGORIE do
        xml.WERSJA '2.00'
        xml.BAZA_ZRD_ID company.name == 'Efigence' ? 'SPEFI' : 'SPART'
        xml.BAZA_DOC_ID company.name == 'Efigence' ? 'KSEFI' : 'KSART'
        xml.KATEGORIA do
          xml.ID_ZRODLA '4286D372-1279-4EC1-86C5-7E0B8151D1FC'
          xml.KOD_OGOLNY '401.401-28'
          xml.POZIOM 'ogólna'
          xml.TYP 'koszt'
          xml.OPIS
          xml.STAWKA_VAT 0
          xml.STATUS_VAT
          xml.KOLUMNA_KPR 'nie księgować'
          xml.KOLUMNA_RYCZALT 'nie księgować'
          xml.ODLICZENIA_VAT 'nie'
          xml.PODZIEL_ODLICZENIA 'Nie'
          xml.PODZIEL_ODLICZENIA_PROCENT 50
          xml.RODZAJ_ZAKUPU
          xml.FISKALNA 'Nie'
          xml.DETAL 'Nie'
          xml.BUDZET 0
          xml.NIEAKTYWNA 'Nie'
          xml.ELIXIRO1
          xml.ELIXIRO2
          xml.ELIXIRO3
          xml.ELIXIRO4
          xml.KONTOWN
          xml.KONTOMA
          xml.KSIEGUJ_W_KOSZTY 'Nie'
          xml.KSIEGUJ_W_KOSZTY_PROCENT 75
        end
        xml.KATEGORIA do
          xml.ID_ZRODLA '23614D7B-159A-49FE-99F5-88365D3AE706'
          xml.KOD_OGOLNY '702.702-04'
          xml.POZIOM 'ogólna'
          xml.TYP 'przychód'
          xml.OPIS
          xml.STAWKA_VAT 0
          xml.STATUS_VAT
          xml.KOLUMNA_KPR 'nie księgować'
          xml.KOLUMNA_RYCZALT 'nie księgować'
          xml.ODLICZENIA_VAT 'nie'
          xml.PODZIEL_ODLICZENIA 'Nie'
          xml.PODZIEL_ODLICZENIA_PROCENT 50
          xml.RODZAJ_ZAKUPU
          xml.FISKALNA 'Nie'
          xml.DETAL 'Nie'
          xml.BUDZET 0
          xml.NIEAKTYWNA 'Nie'
          xml.ELIXIRO1
          xml.ELIXIRO2
          xml.ELIXIRO3
          xml.ELIXIRO4
          xml.KONTOWN
          xml.KONTOMA
          xml.KSIEGUJ_W_KOSZTY 'Nie'
          xml.KSIEGUJ_W_KOSZTY_PROCENT 75
        end
      end
    end

    def clients(company, xml)
      xml.KONTRAHENCI do
        xml.WERSJA '2.00'
        xml.BAZA_ZRD_ID company.name == 'Efigence' ? 'SPEFI' : 'SPART'
        xml.BAZA_DOC_ID company.name == 'Efigence' ? 'KSEFI' : 'KSART'
        vat_numbers = []
        Client.where.not(vat_number: nil).find_each do |client|
          vat_number = client.vat_number.gsub(/\W/, '')
          next if vat_numbers.include?(vat_number)

          vat_numbers << vat_number
          xml.KONTRAHENT do
            xml.ID_ZRODLA vat_number
            xml.AKRONIM vat_number
            xml.ZEZWOLENIE
            xml.OPIS
            xml.CHRONIONY 'Tak'
            xml.RODZAJ 'odbiorca'
            xml.EKSPORT client.country == 'Polska' ? 'krajowy' : 'poza terytorium kraju'
            xml.FINALNY 'Nie'
            xml.PLATNIK_VAT 'Tak'
            xml.MEDIALNY 'Nie'
            xml.POWIAZANY_UOV 'Nie'
            xml.NIEAKTYWNY 'Nie'
            xml.ROLNIK 'Nie'
            xml.NIE_NALICZAJ_ODSETEK 'Nie'
            xml.METODA_KASOWA 'Nie'
            xml.METODA_KASOWA_SPR 'Nie'
            xml.KONTOODB
            xml.KONTODOST
            xml.FORMA_PLATNOSCI 'przelew'
            xml.FORMA_PLATNOSCI_ID
            xml.MAX_ZWLOKA 0
            xml.CENY 'domyślna'
            xml.JEST_LIMIT_KREDYTU 'Nie'
            xml.LIMIT_KREDYTU 0
            xml.NIE_PODLEGA_ROZLICZENIU 'Nie'
            xml.KOMORNIK 'Nie'
            xml.UPUST 0
            xml.INFORMACJE 'Nie'
            xml.INDYWIDUALNY_TERMIN 'Nie'
            xml.TERMIN 0
            xml.KAUCJE_PLATNOSCI 'Nie'
            xml.KAUCJE_TERMIN 60
            xml.KOD_TRANSAKCJI
            xml.BLOKADA_DOKUMENTOW 'Nie'
            xml.LIMIT_PRZETERMINOWANY 'Nie'
            xml.LIMIT_PRZETERMINOWANY_WARTOSC 0
            xml.KRAJ_ISO
            xml.OPIEKUN
            xml.ADRESY do
              xml.ADRES do
                xml.STATUS 'aktualny'
                xml.EAN
                xml.GLN
                xml.NAZWA1 client.name&.tr('&', '_')&.[](0..49)
                xml.NAZWA2 client.name&.tr('&', '_')&.[](50..99)
                xml.NAZWA3 client.name&.tr('&', '_')&.[](100..-1)
                xml.KRAJ client.country&.tr('&', '_')
                xml.WOJEWODZTWO client.voivodeship&.tr('&', '_')
                xml.POWIAT client.district&.tr('&', '_')
                xml.GMINA client.community&.tr('&', '_')
                xml.ULICA client.street&.tr('&', '_')
                xml.NR_DOMU client.street_number&.tr('&', '_')
                xml.NR_LOKALU client.apartment&.tr('&', '_')
                xml.MIASTO client.city&.tr('&', '_')
                xml.KOD_POCZTOWY client.postcode&.tr('&', '_')
                xml.POCZTA client.post&.tr('&', '_')
                xml.DODATKOWE client.additional_address&.tr('&', '_')
                xml.NIP_KRAJ
                xml.NIP client.vat_number&.tr('&', '_')
                xml.REGON
                xml.PESEL
                xml.TELEFON1
                xml.TELEFON2
                xml.FAX
                xml.URL
                xml.EMAIL
              end
            end
            xml.GRUPY do
              xml.GRUPA do
                xml.NAZWA
              end
            end
            xml.WALUTA
            xml.ALGORYTM 'netto'
            xml.NIEUWZGLVATZD 'Nie'
            xml.ANONIMIZACJA 'Nie'
            xml.SPLIT_PAYMENT 'Nie'
            xml.PRZEDSTAWICIELE
            xml.ODBIORCY
            xml.WERYF_STAT_HIST
            xml.WERYF_RACH_HIST
            xml.ATRYBUTY
            xml.KNT_RACHUNKI
            xml.KODY_JPK
          end
        end
        ClientAddress.where.not(vat_number: nil).find_each do |client_address|
          vat_number = client_address.vat_number.gsub(/\W/, '')
          next if vat_numbers.include?(vat_number)

          vat_numbers << vat_number
          xml.KONTRAHENT do
            xml.ID_ZRODLA vat_number
            xml.AKRONIM vat_number
            xml.ZEZWOLENIE
            xml.OPIS
            xml.CHRONIONY 'Tak'
            xml.RODZAJ 'odbiorca'
            xml.EKSPORT client_address.country == 'Polska' ? 'krajowy' : 'poza terytorium kraju'
            xml.FINALNY 'Nie'
            xml.PLATNIK_VAT 'Tak'
            xml.MEDIALNY 'Nie'
            xml.POWIAZANY_UOV 'Nie'
            xml.NIEAKTYWNY 'Nie'
            xml.ROLNIK 'Nie'
            xml.NIE_NALICZAJ_ODSETEK 'Nie'
            xml.METODA_KASOWA 'Nie'
            xml.METODA_KASOWA_SPR 'Nie'
            xml.KONTOODB
            xml.KONTODOST
            xml.FORMA_PLATNOSCI 'przelew'
            xml.FORMA_PLATNOSCI_ID
            xml.MAX_ZWLOKA 0
            xml.CENY 'domyślna'
            xml.JEST_LIMIT_KREDYTU 'Nie'
            xml.LIMIT_KREDYTU 0
            xml.NIE_PODLEGA_ROZLICZENIU 'Nie'
            xml.KOMORNIK 'Nie'
            xml.UPUST 0
            xml.INFORMACJE 'Nie'
            xml.INDYWIDUALNY_TERMIN 'Nie'
            xml.TERMIN 0
            xml.KAUCJE_PLATNOSCI 'Nie'
            xml.KAUCJE_TERMIN 60
            xml.KOD_TRANSAKCJI
            xml.BLOKADA_DOKUMENTOW 'Nie'
            xml.LIMIT_PRZETERMINOWANY 'Nie'
            xml.LIMIT_PRZETERMINOWANY_WARTOSC 0
            xml.KRAJ_ISO
            xml.OPIEKUN
            xml.ADRESY do
              xml.ADRES do
                xml.STATUS 'aktualny'
                xml.EAN
                xml.GLN
                xml.NAZWA1 client_address.name&.tr('&', '_')&.[](0..49)
                xml.NAZWA2 client_address.name&.tr('&', '_')&.[](50..99)
                xml.NAZWA3 client_address.name&.tr('&', '_')&.[](100..-1)
                xml.KRAJ client_address.country&.tr('&', '_')
                xml.WOJEWODZTWO client_address.voivodeship&.tr('&', '_')
                xml.POWIAT client_address.district&.tr('&', '_')
                xml.GMINA client_address.community&.tr('&', '_')
                xml.ULICA client_address.street&.tr('&', '_')
                xml.NR_DOMU client_address.street_number&.tr('&', '_')
                xml.NR_LOKALU client_address.apartment&.tr('&', '_')
                xml.MIASTO client_address.city&.tr('&', '_')
                xml.KOD_POCZTOWY client_address.postcode&.tr('&', '_')
                xml.POCZTA client_address.post&.tr('&', '_')
                xml.DODATKOWE client_address.additional_address&.tr('&', '_')
                xml.NIP_KRAJ
                xml.NIP vat_number&.tr('&', '_')
                xml.REGON
                xml.PESEL
                xml.TELEFON1
                xml.TELEFON2
                xml.FAX
                xml.URL
                xml.EMAIL
              end
            end
            xml.GRUPY do
              xml.GRUPA do
                xml.NAZWA
              end
            end
            xml.WALUTA
            xml.ALGORYTM 'netto'
            xml.NIEUWZGLVATZD 'Nie'
            xml.ANONIMIZACJA 'Nie'
            xml.SPLIT_PAYMENT 'Nie'
            xml.PRZEDSTAWICIELE
            xml.ODBIORCY
            xml.WERYF_STAT_HIST
            xml.WERYF_RACH_HIST
            xml.ATRYBUTY
            xml.KNT_RACHUNKI
            xml.KODY_JPK
          end
        end
        Contractor.where.not(vat_number: nil).find_each do |contractor|
          vat_number = contractor.vat_number.gsub(/\W/, '')
          next if vat_numbers.include?(vat_number)

          vat_numbers << vat_number
          xml.KONTRAHENT do
            xml.ID_ZRODLA vat_number
            xml.AKRONIM vat_number
            xml.ZEZWOLENIE
            xml.OPIS
            xml.CHRONIONY 'Tak'
            xml.RODZAJ 'odbiorca'
            xml.EKSPORT contractor.country == 'Polska' ? 'krajowy' : 'poza terytorium kraju'
            xml.FINALNY 'Nie'
            xml.PLATNIK_VAT 'Tak'
            xml.MEDIALNY 'Nie'
            xml.POWIAZANY_UOV 'Nie'
            xml.NIEAKTYWNY 'Nie'
            xml.ROLNIK 'Nie'
            xml.NIE_NALICZAJ_ODSETEK 'Nie'
            xml.METODA_KASOWA 'Nie'
            xml.METODA_KASOWA_SPR 'Nie'
            xml.KONTOODB
            xml.KONTODOST
            xml.FORMA_PLATNOSCI 'przelew'
            xml.FORMA_PLATNOSCI_ID
            xml.MAX_ZWLOKA 0
            xml.CENY 'domyślna'
            xml.JEST_LIMIT_KREDYTU 'Nie'
            xml.LIMIT_KREDYTU 0
            xml.NIE_PODLEGA_ROZLICZENIU 'Nie'
            xml.KOMORNIK 'Nie'
            xml.UPUST 0
            xml.INFORMACJE 'Nie'
            xml.INDYWIDUALNY_TERMIN 'Nie'
            xml.TERMIN 0
            xml.KAUCJE_PLATNOSCI 'Nie'
            xml.KAUCJE_TERMIN 60
            xml.KOD_TRANSAKCJI
            xml.BLOKADA_DOKUMENTOW 'Nie'
            xml.LIMIT_PRZETERMINOWANY 'Nie'
            xml.LIMIT_PRZETERMINOWANY_WARTOSC 0
            xml.KRAJ_ISO
            xml.OPIEKUN
            xml.ADRESY do
              xml.ADRES do
                xml.STATUS 'aktualny'
                xml.EAN
                xml.GLN
                xml.NAZWA1 contractor.name&.tr('&', '_')
                xml.NAZWA2
                xml.NAZWA3
                xml.KRAJ contractor.country&.tr('&', '_')
                xml.WOJEWODZTWO contractor.voivodeship&.tr('&', '_')
                xml.POWIAT contractor.district&.tr('&', '_')
                xml.GMINA contractor.community&.tr('&', '_')
                xml.ULICA contractor.street&.tr('&', '_')
                xml.NR_DOMU contractor.street_number&.tr('&', '_')
                xml.NR_LOKALU contractor.apartment&.tr('&', '_')
                xml.MIASTO contractor.city&.tr('&', '_')
                xml.KOD_POCZTOWY contractor.postcode&.tr('&', '_')
                xml.POCZTA contractor.post&.tr('&', '_')
                xml.DODATKOWE contractor.additional_address&.tr('&', '_')
                xml.NIP_KRAJ
                xml.NIP vat_number&.tr('&', '_')
                xml.REGON
                xml.PESEL
                xml.TELEFON1
                xml.TELEFON2
                xml.FAX
                xml.URL
                xml.EMAIL
              end
            end
            xml.GRUPY do
              xml.GRUPA do
                xml.NAZWA
              end
            end
            xml.WALUTA
            xml.ALGORYTM 'netto'
            xml.NIEUWZGLVATZD 'Nie'
            xml.ANONIMIZACJA 'Nie'
            xml.SPLIT_PAYMENT 'Nie'
            xml.PRZEDSTAWICIELE
            xml.ODBIORCY
            xml.WERYF_STAT_HIST
            xml.WERYF_RACH_HIST
            xml.ATRYBUTY
            xml.KNT_RACHUNKI do
              xml.KNT_RACHUNEK do
                xml.LP 1
                xml.SCHEM_FORMA_PLATNOSCI 'przelew'
                xml.SCHEM_FORMA_PLATNOSCI_ID
                xml.SCHEM_BANK_NR contractor.account_number ? contractor.account_number[4..11] : nil
                xml.SCHEM_BANK_ID
                xml.DOMYSLNY 'Tak'
                xml.RACHUNEK_IBAN 'Tak'
                xml.RACHUNEK_NUMER contractor.account_number
              end
            end
            xml.KODY_JPK
          end
        end
      end
    end

    def projects(company, xml)
      xml.PROJEKTY do
        xml.WERSJA '2.00'
        xml.BAZA_ZRD_ID company.name == 'Efigence' ? 'SPEFI' : 'SPART'
        xml.BAZA_DOC_ID company.name == 'Efigence' ? 'KSEFI' : 'KSART'
        AccountingNumber.find_each.with_index do |accounting_number, index|
          number = accounting_number.number.to_s
          xml.PROJEKT do
            xml.ID_ZRODLA number
            xml.KOD number
            xml.NAZWA accounting_number.description&.tr('&', '_')
            xml.SYMBOL '0'
            xml.POZIOM 1
            xml.WEZEL index + 2
            xml.LP index + 2
            xml.URL '0'
            xml.KONTO '0'
            xml.NIEAKTYWNY 'Nie'
            xml.OVERHEAD accounting_number.overhead? ? 'Tak' : 'Nie'
          end
        end
      end
    end

    def departments(company, xml)
      xml.WYDZIALY do
        xml.WERSJA '2.00'
        xml.BAZA_ZRD_ID company.name == 'Efigence' ? 'SPEFI' : 'SPART'
        xml.BAZA_DOC_ID company.name == 'Efigence' ? 'KSEFI' : 'KSART'
        xml.WYDZIAL do
          xml.ID_ZRODLA "FIRMA_#{company.id}"
          xml.KOD "FIRMA_#{company.id}"
          xml.NAZWA company.name&.tr('&', '_')
          xml.SYMBOL ''
          xml.POZIOM 1
          xml.WEZEL 1
          xml.LISC 0
          xml.LAST 0
          xml.LOKALIZACJA_ID
          xml.LOKALIZACJA_KOD 'brak'
          xml.ADDMETHOD 0
          xml.KONTO ''
          xml.IMP_ID 0
          xml.NIEAKTYWNY 'Nie'
          xml.WYDZIALY do
            MpkNumber.find_each.with_index do |mpk_number, index|
              leaf = index + 1
              xml.WYDZIAL do
                xml.ID_ZRODLA mpk_number.id
                xml.KOD mpk_number.key
                xml.NAZWA mpk_number.name&.tr('&', '_')
                xml.SYMBOL '0'
                xml.POZIOM 2
                xml.WEZEL "1.#{leaf}"
                xml.LISC leaf
                xml.LAST 0
                xml.LOKALIZACJA_ID
                xml.LOKALIZACJA_KOD 'brak'
                xml.ADDMETHOD 1
                xml.KONTO 0
                xml.IMP_ID 0
                xml.NIEAKTYWNY 'Nie'
              end
            end
          end
        end
      end
    end

    def workers(company, xml)
      xml.PRACOWNICY do
        xml.WERSJA '2.00'
        xml.BAZA_ZRD_ID company.name == 'Efigence' ? 'SPEFI' : 'SPART'
        xml.BAZA_DOC_ID company.name == 'Efigence' ? 'KSEFI' : 'KSART'
        User.where(contract_of_employment: true, company: company).find_each do |user|
          xml.PRACOWNIK do
            xml.ID_ZRODLA user.id
            xml.AKRONIM user.username.upcase
            xml.ARCHIWALNY user.active? ? 'Nie' : 'Tak'
            xml.NAZWISKO user.last_name
            xml.IMIE1 user.first_name
            xml.IMIE2
            xml.IMIE_OJCA
            xml.IMIE_MATKI
            xml.DATA_URODZENIA
            xml.MIEJSCE_URODZENIA
            xml.NAZWISKO_RODOWE
            xml.NAZWISKO_RODOWE_MATKI
            xml.PESEL
            xml.NIP_KRAJ
            xml.NIP
            xml.ADRESY
            xml.TELEFON1
            xml.DATA_ZATRUDNIENIA
            xml.WSPOLCZYNNIK_KOSZTY
            xml.WSPOLCZYNNIK_ULGA
            xml.URZĄD_SKARBOWY
            xml.URZĄD_SKARBOWY_ID
            xml.FORMA_PLATNOŚCI
            xml.FORMA_PLATNOŚCI_ID
            xml.KATEGORIA
            xml.KATEGORIA_ID
            xml.BANK_NR
            xml.BANK_ID
            xml.NR_RACHUNKU
            xml.IBAN
            xml.UWAGI
            xml.NIE_ROZLICZAC
            xml.NIP_ZAMIAST_PESEL_NA_DEKLARACJI
            xml.SCHEMATY_PLATNOSCI
          end
        end
      end
    end
  end
end

# rubocop:enable Metrics/BlockLength,Metrics/MethodLength,Metrics/AbcSize,Metrics/ModuleLength
