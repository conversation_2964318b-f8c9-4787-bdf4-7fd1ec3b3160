class InvoicesProcessedXlsxGenerator
  include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>

  def initialize(issued_scope, accepted_scope)
    @issued_scope = issued_scope
    @accepted_scope = accepted_scope
  end

  def generate
    p = Axlsx::Package.new
    wb = p.workbook
    add_styles(wb)
    add_worksheets(wb)

    p.to_stream
  end

  private

  def add_worksheets(workbook)
    workbook.add_worksheet(name: 'Issued') do |sheet|
      add_issued_header(sheet)

      @issued_scope.each do |invoice|
        add_issued_row(sheet, invoice.decorate)
      end
    end

    workbook.add_worksheet(name: 'Accepted') do |sheet|
      add_accepted_header(sheet)

      @accepted_scope.each do |invoice|
        add_accepted_row(sheet, invoice.decorate)
      end
    end
  end

  def add_issued_header(sheet)
    sheet.add_row(['ID', 'Project', 'Issuer', 'Invoice date', 'Sell date', 'Issued at', 'Type', 'Total', 'Currency',
                   'Invoice document', 'Paper invoice', 'E-mail invoice', 'Email sent'],
                  style: @header)
  end

  def add_accepted_header(sheet)
    sheet.add_row(['ID', 'Project', 'Issuer', 'Invoice date', 'Sell date', 'Accepted at', 'Type', 'Total', 'Currency',
                   'Invoice document', 'Paper invoice', 'E-mail invoice'], style: @header)
  end

  def add_issued_row(sheet, invoice) # rubocop:disable Metrics/AbcSize
    sheet.add_row([invoice.id, invoice.project.name, invoice.created_by, invoice.invoice_date, invoice.sell_date,
                   invoice.issued_at, invoice_kind(invoice), (invoice.total_amount / 100), invoice.payment.currency,
                   invoice.invoice_document&.document&.original_filename,
                   boolean_value(invoice.paper_invoice_sending_method?),
                   boolean_value(invoice.electronic_invoice_sending_method?), email_sent?(invoice)],
                  style: [@text_center, @text, @text_center, @date, @date, @date, @text_center, @amount, @text, @text,
                          @text_center, @text_center, @text_center])
  end

  def add_accepted_row(sheet, invoice) # rubocop:disable Metrics/AbcSize
    sheet.add_row([invoice.id, invoice.project.name, invoice.created_by, invoice.invoice_date, invoice.sell_date,
                   invoice.accepted_at, invoice_kind(invoice), (invoice.total_amount / 100), invoice.payment.currency,
                   invoice.invoice_document&.document&.original_filename,
                   boolean_value(invoice.paper_invoice_sending_method?),
                   boolean_value(invoice.electronic_invoice_sending_method?)],
                  style: [@text_center, @text, @text_center, @date, @date, @date, @text_center, @amount, @text, @text,
                          @text_center, @text_center])
  end

  def invoice_kind(invoice)
    return 'Amendment' if invoice.correction_reason.present?

    case invoice.kind
    when 'advance' then 'Advance'
    when 'advance_accounting' then 'Advance accounting'
    when 're_invoice' then 'Re-invoice'
    when 'accounting_note' then 'Note'
    when 'vat_barter' then 'Barter VAT invoice'
    else
      'VAT invoice'
    end
  end

  def email_sent?(invoice)
    boolean_value(invoice.snapshots
                         .where('identifier LIKE ? AND metadata LIKE ?', 'email%', '%Email to report an invoice%')
                         .present?)
  end

  def boolean_value(value)
    value ? 'TRUE' : 'FALSE'
  end

  def add_styles(workbook) # rubocop:disable Metrics/MethodLength
    workbook.styles do |s|
      default = { font_name: 'Arial', sz: 8, border: { style: :thin, color: '000000' },
                  alignment: { vertical: :center, wrap_text: true } }
      standard = default.merge(bg_color: 'dae3f3')

      @header = s.add_style(default.merge(b: true, bg_color: 'f4b183',
                                          alignment: { horizontal: :center, vertical: :center, wrap_text: true }))
      @text = s.add_style(standard)
      @text_center = s.add_style(standard.merge(alignment: { horizontal: :center, vertical: :center, wrap_text: true }))
      @date = s.add_style(standard.merge(format_code: 'YYYY-MM-DD',
                                         alignment: { horizontal: :center, vertical: :center, wrap_text: true }))
      @amount = s.add_style(standard.merge(format_code: '#,##0.00'))
    end
  end
end
