# rubocop:disable Metrics/BlockLength,Metrics/MethodLength,Metrics/AbcSize,Metrics/ModuleLength
# rubocop:disable Metrics/CyclomaticComplexity,Metrics/PerceivedComplexity
module InvoiceXmlGenerator
  class << self
    def generate(invoice)
      invoice = invoice.decorate
      xml = Builder::XmlMarkup.new
      xml.ROOT do
        attributes(invoice, xml)
        sell_register(invoice, xml)
      end
    end

    private

    def attributes(invoice, xml)
      company = invoice.project.company
      xml.ATRYBUTY do
        xml.WERSJA '2.00'
        xml.BAZA_ZRD_ID company.name == 'Efigence' ? 'SPEFI' : 'SPART'
        xml.BAZA_DOC_ID company.name == 'Efigence' ? 'KSEFI' : 'KSART'
        invoice.mpk_positions.find_each.with_index do |mpk_position, index|
          xml.ATRYBUT do
            xml.TYP 'dokumentu'
            xml.KOD "MPK#{index + 1}"
            xml.ID_ZRODLA ''
            xml.NAZWA mpk_position.mpk_number.name
            xml.FORMAT 'tekstowe'
            xml.ZALEZNY 'nie'
            xml.CZY_KOPIOWAC_FA 'nie'
            xml.CZY_KOPIOWAC_DO_VAT 'tak'
            xml.CZY_KOD 'nie'
            xml.CZY_PRZENOSIC 'nie'
            xml.CZY_DRUKOWAC 'nie'
            xml.CZY_PRZENOSIC_NA_DOSTAWY 'nie'
            xml.CZY_OBOWIAZKOWY_DOSTAWY 'nie'
            xml.JP_DOSTEPNY_FA 'nie'
            xml.JP_DOSTEPNY_VAT 'nie'
            xml.JP_DOSTEPNY_FAVAT 'nie'
            xml.ELEMENTY_LISTY
          end
          xml.ATRYBUT do
            xml.TYP 'dokumentu'
            xml.KOD "WARTOSC#{index + 1}"
            xml.ID_ZRODLA ''
            xml.NAZWA
            xml.FORMAT 'liczbowe'
            xml.ZALEZNY 'Nie'
            xml.CZY_KOPIOWAC_FA 'Nie'
            xml.CZY_KOPIOWAC_DO_VAT 'Tak'
            xml.CZY_KOD 'Nie'
            xml.CZY_PRZENOSIC 'Nie'
            xml.CZY_DRUKOWAC 'Nie'
            xml.CZY_PRZENOSIC_NA_DOSTAWY 'Nie'
            xml.CZY_OBOWIAZKOWY_DOSTAWY 'Nie'
            xml.JP_DOSTEPNY_FA 'Nie'
            xml.JP_DOSTEPNY_VAT 'Nie'
            xml.JP_DOSTEPNY_FAVAT 'Nie'
            xml.ELEMENTY_LISTY
          end
          xml.ATRYBUT do
            xml.TYP 'dokumentu'
            xml.KOD "PROJEKT#{index + 1}"
            xml.ID_ZRODLA ''
            xml.NAZWA (mpk_position.project || invoice.project).accounting_number.description&.tr('&', '_')
            xml.FORMAT 'tekstowe'
            xml.ZALEZNY 'nie'
            xml.CZY_KOPIOWAC_FA 'nie'
            xml.CZY_KOPIOWAC_DO_VAT 'tak'
            xml.CZY_KOD 'nie'
            xml.CZY_PRZENOSIC 'nie'
            xml.CZY_DRUKOWAC 'nie'
            xml.CZY_PRZENOSIC_NA_DOSTAWY 'nie'
            xml.CZY_OBOWIAZKOWY_DOSTAWY 'nie'
            xml.JP_DOSTEPNY_FA 'nie'
            xml.JP_DOSTEPNY_VAT 'nie'
            xml.JP_DOSTPENY_FAVAT 'nie'
            xml.ELEMENTY_LISTY
          end
          xml.ATRYBUT do
            xml.TYP 'dokumentu'
            xml.KOD "KONTO#{index + 1}"
            xml.ID_ZRODLA ''
            xml.NAZWA invoice.revenue_account.key
            xml.FORMAT 'tekstowe'
            xml.ZALEZNY 'Nie'
            xml.CZY_KOPIOWAC_FA 'Nie'
            xml.CZY_KOPIOWAC_DO_VAT 'Tak'
            xml.CZY_KOD 'Nie'
            xml.CZY_PRZENOSIC 'Nie'
            xml.CZY_DRUKOWAC 'Nie'
            xml.CZY_PRZENOSIC_NA_DOSTAWY 'Nie'
            xml.CZY_OBOWIAZKOWY_DOSTAWY 'Nie'
            xml.JP_DOSTEPNY_FA 'Nie'
            xml.JP_DOSTEPNY_VAT 'Nie'
            xml.JP_DOSTEPNY_FAVAT 'Nie'
            xml.ELEMENTY_LISTY
          end
        end
        invoice.invoice_positions.each_with_index do |_invoice_position, index|
          xml.ATRYBUT do
            xml.TYP 'dokumentu'
            xml.KOD "POZYCJA#{index + 1}"
            xml.ID_ZRODLA ''
            xml.NAZWA ''
            xml.FORMAT 'tekstowe'
            xml.ZALEZNY 'Nie'
            xml.CZY_KOPIOWAC_FA 'Nie'
            xml.CZY_KOPIOWAC_DO_VAT 'Tak'
            xml.CZY_KOD 'Nie'
            xml.CZY_PRZENOSIC 'Nie'
            xml.CZY_DRUKOWAC 'Nie'
            xml.CZY_PRZENOSIC_NA_DOSTAWY 'Nie'
            xml.CZY_OBOWIAZKOWY_DOSTAWY 'Nie'
            xml.JP_DOSTEPNY_FA 'Nie'
            xml.JP_DOSTEPNY_VAT 'Nie'
            xml.JP_DOSTEPNY_FAVAT 'Nie'
            xml.ELEMENTY_LISTY
          end
        end
        xml.ATRYBUT do
          xml.TYP 'dokumentu'
          xml.KOD 'ODBIORCA'
          xml.ID_ZRODLA 'ODBIORCA'
          xml.NAZWA ''
          xml.FORMAT 'tekstowe'
          xml.ZALEZNY 'Nie'
          xml.CZY_KOPIOWAC_FA 'Nie'
          xml.CZY_KOPIOWAC_DO_VAT 'Tak'
          xml.CZY_KOD 'Nie'
          xml.CZY_PRZENOSIC 'Nie'
          xml.CZY_DRUKOWAC 'Nie'
          xml.CZY_PRZENOSIC_NA_DOSTAWY 'Nie'
          xml.CZY_OBOWIAZKOWY_DOSTAWY 'Nie'
          xml.JP_DOSTEPNY_FA 'Nie'
          xml.JP_DOSTEPNY_VAT 'Nie'
          xml.JP_DOSTEPNY_FAVAT 'Nie'
          xml.ELEMENTY_LISTY
        end
      end
    end

    def sell_register(invoice, xml)
      company = invoice.project.company
      name = invoice.client_name || invoice.client_address&.name || invoice.project.client.name
      name = name&.tr('&', '_')
      vat_number = (invoice.client_address&.vat_number || invoice.client.vat_number)
      xml.REJESTRY_SPRZEDAZY_VAT do
        xml.WERSJA '2.00'
        xml.BAZA_ZRD_ID company.name == 'Efigence' ? 'SPEFI' : 'SPART'
        xml.BAZA_DOC_ID company.name == 'Efigence' ? 'KSEFI' : 'KSART'
        xml.REJESTR_SPRZEDAZY_VAT do
          xml.ID_ZRODLA invoice.id
          xml.MODUL 'Rejestr Vat'
          xml.TYP 'Rejestr sprzedazy'
          xml.REJESTR 'SPRZEDAŻ'
          xml.DATA_WYSTAWIENIA invoice.invoice_date.to_fs(:sql)
          xml.DATA_SPRZEDAZY invoice.sell_date.to_fs(:sql)
          xml.TERMIN invoice.due_date.to_fs(:sql)
          xml.DATA_DATAOBOWIAZKUPODATKOWEGO invoice.sell_date.to_fs(:sql)
          xml.DATA_DATAPRAWAODLICZENIA invoice.invoice_date.to_fs(:sql)
          xml.NUMER invoice.number
          xml.KOREKTA invoice.amendment? ? 'Tak' : 'Nie'
          xml.KOREKTA_NUMER invoice.amendment? ? invoice.amends.number : ''
          xml.WEWNETRZNA 'Nie'
          xml.METODA_KASOWA 'Nie'
          xml.FISKALNA 'Nie'
          xml.DETALICZNA 'Nie'
          xml.EKSPORT 'nie'
          xml.FINALNY 'Nie'
          xml.PODATNIK_CZYNNY 'Tak'
          xml.IDENTYFIKATOR_KSIEGOWY '1/20/RS'
          xml.TYP_PODMIOTU 'kontrahent'
          xml.PODMIOT (invoice.client_address&.vat_number || invoice.client.vat_number).gsub(/\W/, '')
          xml.PODMIOT_ID
          xml.PODMIOT_NIP vat_number.match(/\A[a-zA-Z]*(\d*)/)[1]
          xml.NAZWA1(
            name&.[](0..49)
          )
          xml.NAZWA2(
            name&.[](50..99).presence || ' '
          )
          xml.NAZWA3(
            name&.[](100..-1).presence || ' '
          )
          xml.NIP_KRAJ(vat_number.match(/\A([a-zA-Z]*)/)[1] || '')
          xml.NIP vat_number.match(/\A[a-zA-Z]*(\d*)/)[1]
          xml.KRAJ invoice.country || invoice.client_address&.country || invoice.project.client.country
          xml.WOJEWÓDZTWO invoice.voivodeship || invoice.client_address&.voivodeship || invoice.project.client.voivodeship
          xml.POWIAT invoice.district || invoice.client_address&.district || invoice.project.client.district
          xml.GMINA invoice.community || invoice.client_address&.community || invoice.project.client.community
          xml.ULICA invoice.street || invoice.client_address&.street || invoice.project.client.street
          xml.NR_DOMU invoice.street_number || invoice.client_address&.street_number || invoice.project.client.street_number
          xml.NR_LOKALU invoice.apartment || invoice.client_address&.apartment || invoice.project.client.apartment
          xml.MIASTO invoice.city || invoice.client_address&.city || invoice.project.client.city
          xml.KOD_POCZTOWY invoice.postcode || invoice.client_address&.postcode || invoice.project.client.postcode
          xml.POCZTA invoice.post || invoice.client_address&.post || invoice.project.client.post
          xml.DODATKOWE invoice.additional_address || invoice.client_address&.additional_address || invoice.project.client.additional_address
          xml.PESEL ''
          xml.TYP_PLATNIKA 'kontrahent'
          xml.PLATNIK (invoice.client_address&.vat_number || invoice.client.vat_number).gsub(/\W/, '')
          xml.PLATNIK_ID ''
          xml.PLATNIK_NIP vat_number
          xml.KATEGORIA 'PRZYCHODY'
          xml.KATEGORIA_ID
          xml.OPIS invoice.description.presence&.tr('&', '_') || ''
          xml.FORMA_PLATNOSCI 'przelew'
          xml.FORMA_PLATNOSCI_ID
          xml.DEKLARACJA_VAT7 invoice.sell_date.strftime('%Y-%m')
          xml.DEKLARACJA_VATUE (invoice.client_address || invoice.client).country.in?(%w[Polska Poland]) ? 'Tak' : 'Nie'
          xml.WALUTA do
            xml.cdata!(invoice.payment.currency == 'PLN' ? 'PLN' : invoice.payment.currency)
          end
          xml.KURS_WALUTY 'NBP'
          xml.NOTOWANIE_WALUTY_ILE invoice.payment.currency == 'PLN' ? 1 : nil
          xml.NOTOWANIE_WALUTY_ZA_ILE invoice.payment.currency == 'PLN' ? 1 : nil
          xml.DATA_KURSU invoice.sell_date.to_fs(:sql)
          xml.KURS_DO_KSIEGOWANIA 'Nie'
          xml.KURS_WALUTY_2 'NBP'
          xml.NOTOWANIE_WALUTY_ILE_2 invoice.payment.currency == 'PLN' ? 1 : nil
          xml.NOTOWANIE_WALUTY_ZA_ILE_2 invoice.payment.currency == 'PLN' ? 1 : nil
          xml.DATA_KURSU_2 invoice.sell_date.to_fs(:sql)
          xml.PLATNOSC_VAT_W_PLN 'Nie'
          xml.AKCYZA_NA_WEGIEL 0
          xml.AKCYZA_NA_WEGIEL_KOLUMNA_KPR 'nie księgować'
          xml.JPK_FA 'Tak'
          xml.DEKLARACJA_VAT27 'Nie'
          xml.FA_Z_PA 'Nie'
          xml.MPP 'Nie'
          xml.VAN_FA_Z_PA 'Nie'
          xml.VAN_RODZAJ 0
          xml.POZYCJE do
            invoice.invoice_positions.each_with_index do |position, index|
              xml.POZYCJA do
                xml.LP index + 1
                xml.KATEGORIA_POS '702.702-04'
                xml.KATEGORIA_ID_POS
                xml.STAWKA_VAT (position.tax_rate || 'np').upcase
                xml.STATUS_VAT vat_status(position)
                xml.NETTO position.net_value
                xml.VAT position.vat_value
                xml.NETTO_SYS position.net_value
                xml.VAT_SYS position.vat_value
                xml.NETTO_SYS2 position.net_value
                xml.VAT_SYS2 position.vat_value
                xml.RODZAJ_SPRZEDAZY 'usługi'
                xml.UWZ_W_PROPORCJI 'tak'
                xml.KOLUMNA_KPR 'Nie księgować'
                xml.KOLUMNA_RYCZALT 'Nie księgować'
                xml.OPIS_POZ position.name
                xml.OPIS_POZ_2 ''
              end
            end
          end
          xml.KWOTY_DODATKOWE
          xml.PLATNOSCI do
            xml.PLATNOSC do
              xml.ID_ZRODLA_PLAT
              xml.TERMIN_PLAT invoice.due_date.to_fs(:sql)
              xml.FORMA_PLATNOSCI_PLAT 'przelew'
              xml.FORMA_PLATNOSCI_ID_PLAT
              xml.KWOTA_PLAT invoice.gross_value
              xml.WALUTA_PLAT do
                xml.cdata!(invoice.payment.currency == 'PLN' ? '' : invoice.payment.currency)
              end
              xml.KURS_WALUTY_PLAT 'NBP'
              xml.NOTOWANIE_WALUTY_ILE_PLAT 1
              xml.NOTOWANIE_WALUTY_ZA_ILE_PLAT 1
              xml.KWOTA_PLN_PLAT invoice.gross_value
              xml.KIERUNEK 'przychód'
              xml.PODLEGA_ROZLICZENIU 'tak'
              xml.KONTO ''
              xml.NIE_NALICZAJ_ODSETEK 'Nie'
              xml.PRZELEW_SEPA 'Nie'
              xml.DATA_KURSU_PLAT invoice.sell_date.to_fs(:sql)
              xml.WALUTA_DOK do
                xml.cdata!(invoice.payment.currency == 'PLN' ? '' : invoice.payment.currency)
              end
              xml.PLATNOSC_TYP_PODMIOTU 'kontrahent'
              xml.PLATNOSC_PODMIOT (invoice.client_address&.vat_number || invoice.client.vat_number).gsub(/\W/, '')
              xml.PLATNOSC_PODMIOT_ID ''
              xml.PLATNOSC_PODMIOT_NIP vat_number
              xml.PLAT_KATEGORIA 'PRZYCHODY'
              xml.PLAT_KATEGORIA_ID ''
              xml.PLAT_ELIXIR_O1 "Zapłata za FS-#{invoice.number}"
              xml.PLAT_ELIXIR_O2 ''
              xml.PLAT_ELIXIR_O3 ''
              xml.PLAT_ELIXIR_O4 ''
              xml.PLAT_FA_Z_PA 'Nie'
              xml.PLAT_VAN_FA_Z_PA 'Nie'
              xml.PLAT_SPLIT_PAYMENT 'Nie'
              xml.PLAT_SPLIT_KWOTA_VAT ''
              xml.PLAT_SPLIT_NIP ''
              xml.PLAT_SPLIT_NR_DOKUMENTU ''
            end
          end
          xml.KODY_JPK do
            fetch_invoice_jpk_codes(invoice).each do |code|
              xml.KOD_JPK do
                xml.KOD do
                  xml.cdata!(code)
                end
              end
            end
          end
          xml.DOKUMENTY_OBD
          xml.ATRYBUTY do
            invoice.mpk_positions.includes(:mpk_number).find_each
                   .with_index do |mpk_position, index|
              xml.ATRYBUT do
                xml.KOD_ATR "MPK#{index + 1}"
                xml.ID_ZRODLA_ATR ''
                xml.WARTOSC mpk_position.mpk_number.id
              end
              xml.ATRYBUT do
                xml.KOD_ATR "WARTOSC#{index + 1}"
                xml.ID_ZRODLA_ATR ''
                xml.WARTOSC mpk_position.amount / 100.0
              end
              xml.ATRYBUT do
                xml.KOD_ATR "PROJEKT#{index + 1}"
                xml.ID_ZRODLA_ATR ''
                xml.WARTOSC (mpk_position.project || invoice.project).accounting_number.number
              end
              xml.ATRYBUT do
                xml.KOD_ATR "KONTO#{index + 1}"
                xml.ID_ZRODLA_ATR ''
                xml.WARTOSC invoice.revenue_account.key
              end
            end
            invoice.invoice_positions.each_with_index do |invoice_position, index|
              xml.ATRYBUT do
                xml.KOD_ATR "POZYCJA#{index + 1}"
                xml.ID_ZRODLA_ATR ''
                xml.WARTOSC invoice_position.name
              end
            end
            xml.ATRYBUT do
              xml.KOD_ATR 'ODBIORCA'
              xml.ID_ZRODLA_ATR 'ODBIORCA'
              xml.WARTOSC ''
            end
          end
        end
      end
    end

    def vat_status(position)
      case position.tax_rate
      when 'np', nil then 'nie podlega'
      when 'zw' then 'zwolniona'
      else 'opodatkowana'
      end
    end

    def fetch_invoice_jpk_codes(invoice)
      invoice.invoice_positions.reduce([]) { |acc, pos| acc.concat([pos.jpk_gtu, pos.jpk_transaction_code]) }
             .reject(&:blank?)
             .uniq
    end
  end
end
# rubocop:enable Metrics/BlockLength,Metrics/MethodLength,Metrics/AbcSize,Metrics/ModuleLength
# rubocop:enable Metrics/CyclomaticComplexity,Metrics/PerceivedComplexity
