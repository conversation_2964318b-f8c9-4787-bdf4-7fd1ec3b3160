class CreateUser
  def call(user_params)
    user = User.new(user_params)
    begin
      User.transaction do
        username, email, aliases = UsernameAndEmailGenerator.new.call(user)

        if [username, email, aliases].any?
          user.username = username
          user.email = email
        end
        generate_aliases(user, aliases)
        user.save!
        raise ActiveRecord::RecordInvalid if user.invalid?

        redirect_url = DeviseTokenAuth.default_confirm_success_url || Rails.application.routes.url_helpers.root_url
        user.send_confirmation_instructions(client_config: 'default', redirect_url: redirect_url)
      end
      user
    rescue ActiveRecord::RecordInvalid => e
      if user.errors.empty?
        # :nocov:
        user.errors.add(:base, e.message)
        user.errors.add(:_backtrace, e.backtrace.to_a.slice(0, 20).map{|i| i.gsub(Rails.root.to_s, '[FILTERED]')}.join(', ')) if Rails.env.development?
        ::ExceptionNotifier.notify_exception(e, data: {}) unless e.class.name =~ /ActiveRecord/
        # :nocov:
      end
      user
    end
  end

  private

  def generate_aliases(user, aliases)
    return unless aliases
    aliases.each do |email|
      user.email_aliases.build(email: email)
    end
  end
end
