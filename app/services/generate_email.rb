class GenerateEmail
  def call(user, username)
    domain = user.company.domain.downcase
    primary_email = username + '@' + domain
    [primary_email, generate_aliases(user, username)]
  end

  private

  def generate_aliases(user, username)
    return if username.include?('+')
    aliases = []
    usernames = generate_full_name_usernames(user)
    aliases << generate_full_name_aliases(usernames, user.company)
    aliases << generate_aliases_for_other_company(user, username, usernames)
    aliases.flatten
  end

  def generate_full_name_aliases(usernames, company)
    domain = company.is_a?(String) ? company : company.domain.downcase
    usernames.map { |u| "#{u}@#{domain}" }
  end

  def generate_aliases_for_other_company(user, username, usernames)
    original_company = user.company
    return [] unless original_company.name.in?(Company::NATIVE)
    companies = Company.where(name: Company::NATIVE - [original_company.name])
    companies.map do |company|
      domain = company.domain.downcase
      primary_alias = "#{username}@#{domain}"
      [primary_alias, *generate_full_name_aliases(usernames, domain)]
    end.flatten
  end

  def generate_full_name_usernames(user)
    first_name = user.first_name.parameterize
    last_name = user.last_name.parameterize
    domain = user.company.domain.downcase
    index = find_index(user, first_name, last_name, domain)

    usernames = ["#{first_name}.#{last_name}", "#{first_name}_#{last_name}"]

    usernames.map! { |u| "#{u}#{index}" } unless index.zero?

    usernames
  end

  def find_index(user, first_name, last_name, domain)
    email_username = "#{first_name}.#{last_name}"

    index = 0
    loop do
      email_temp_name = "#{email_username}#{index.zero? ? nil : index}"
      email = "#{email_temp_name}@#{domain}"
      break if EmailAlias.where(email: email).where.not(user: user).empty?
      index += 1
    end
    index
  end
end
