class InvoicesMonthlyReportGenerator
  ATTRIBUTES_WHITELIST = %i[invoice_date sell_date due_date].freeze

  class Error < StandardError; end
  class << self
    def generate(params)
      CSV.generate do |csv|
        csv << ['Sp<PERSON>ł<PERSON>', 'Numer pozycji', '<PERSON><PERSON><PERSON><PERSON>', 'Projekt', 'ID Projektu', 'Data sprzedaży', 'Data faktury',
                'Data płatności', '<PERSON><PERSON><PERSON> netto', 'W<PERSON>uta', 'Numer księgowy projektu', 'MPK', 'Status', 'Opis płatności',
                'Elementy faktury', 'Komentarz faktury', 'Data utworzenia', 'Data akceptacji', 'Data dodania faktury',
                'Data wystawienia', 'Miasto', 'Ulica', 'Numer Ulicy', 'Numer Domu', 'Kod pocztowy',
                '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Nazwa odbiorcy faktury', '<PERSON><PERSON><PERSON> faktury', '<PERSON><PERSON><PERSON> wysyłki faktury', 'Typ faktury',
                'Wysłano fakture papierową', 'Wysłano fakture elektorniczną', 'Adres e-mail', 'Tytuł faktury',
                'Nazwa konta', 'Numer konta', 'Nazwa banku', 'Klucz przychodu', 'Rachunek przychodu']

        add_invoices(params, csv)
        add_payments(params, csv)
      end
    end

    private

    def add_invoices(params, csv)
      invoices_scope(PendingPayment, params).find_each do |invoice|
        generate_row(invoice, csv)
      end
      invoices_scope(CompletedPayment, params).find_each do |invoice|
        generate_row(invoice, csv)
      end
    end

    def add_payments(params, csv)
      payments_scope(params).find_each do |payment|
        generate_payment_row(payment, csv)
      end
    end

    def invoices_scope(scope, params) # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      date_from = parse_date(params[:date_from])
      date_to = parse_date(params[:date_to])

      scope = scope.includes(:invoice, :mpk_number, :payment, project: %I[client company accounting_number])
                   .left_joins(payment: %I[current_invoice accepted_invoice pending_invoice])
      scope = scope.where(invoices: { determine_attribute_name(params[:attribute_name]) => date_from..date_to }).or(
        scope.where(accepted_invoices_payments: { determine_attribute_name(params[:attribute_name]) => date_from..date_to })
      ).or(
        scope.where(pending_invoices_payments: { determine_attribute_name(params[:attribute_name]) => date_from..date_to })
      )

      scope = accounting_number_scope(params[:accounting_number_id], scope) if params[:accounting_number_id].present?
      scope = project_scope(params[:project_id], scope) if params[:project_id].present?
      scope = client_scope(params[:client_id], scope) if params[:client_id].present?
      scope = company_scope(params[:company_id], scope) if params[:company_id].present?

      scope.distinct
    end

    def payments_scope(params) # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      date_from = parse_date(params[:date_from])
      date_to = parse_date(params[:date_to])

      scope = Payment.where.missing(:current_invoice)
                     .where.missing(:accepted_invoice)
                     .where.missing(:pending_invoice)
      scope = filter_payments_by_date(date_from, date_to, params[:attribute_name], scope)

      scheduled_payments_scope = ScheduledPayment.where(payment_id: scope.ids)
                                                 .includes(:mpk_number, payment: :pending_invoice,
                                                                        project: %I[client company accounting_number])

      if params[:accounting_number_id].present?
        scheduled_payments_scope = accounting_number_scope(params[:accounting_number_id], scheduled_payments_scope)
      end
      scheduled_payments_scope = project_scope(params[:project_id], scheduled_payments_scope) if params[:project_id].present?
      scheduled_payments_scope = client_scope(params[:client_id], scheduled_payments_scope) if params[:client_id].present?
      scheduled_payments_scope = company_scope(params[:company_id], scheduled_payments_scope) if params[:company_id].present?

      scheduled_payments_scope
    end

    def filter_payments_by_date(date_from, date_to, attribute_name, scope)
      case attribute_name
      when 'sell_date'
        scope.where(sell_date: date_from..date_to)
      when 'due_date'
        scope.left_joins(payment_schedule: :project)
             .where('DATE_ADD(payments.issued_on, INTERVAL projects.days_to_payment DAY) BETWEEN ? AND ?',
                    date_from, date_to)
      else
        scope.where(issued_on: date_from..date_to)
      end
    end

    def accounting_number_scope(accounting_number_id, scope)
      scope.where(projects: { accounting_number_id: accounting_number_id })
    end

    def project_scope(project_id, scope)
      scope.where(projects: { id: project_id })
    end

    def client_scope(client_id, scope)
      scope.where(projects: { client_id: client_id })
    end

    def company_scope(company_id, scope)
      scope.where(projects: { company_id: company_id })
    end

    def parse_date(date)
      Date.parse(date)
    rescue ArgumentError
      raise Error
    end

    def determine_attribute_name(attribute_name)
      attribute_name = attribute_name.to_sym
      attribute_name.in?(ATTRIBUTES_WHITELIST) ? attribute_name : ATTRIBUTES_WHITELIST.first
    end

    # rubocop:disable Metrics/AbcSize, Metrics/MethodLength, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
    def generate_row(invoice_payment, csv)
      csv << [
        invoice_payment.project.company.name, invoice_payment.invoice&.number, invoice_payment.project.client.name,
        invoice_payment.project.name, invoice_payment.project.id, invoice_payment.invoice&.sell_date,
        invoice_payment.invoice&.invoice_date, invoice_payment.invoice&.due_date, invoice_payment.amount / 100.0,
        invoice_payment.currency, invoice_payment.project.accounting_number&.number, invoice_payment.mpk_number.key,
        invoice_status(invoice_payment.invoice), invoice_payment.payment.description,
        invoice_payment.invoice&.invoice_positions&.pluck(:name)&.join('; '), invoice_payment.invoice&.description,
        format_date(invoice_payment.created_at), format_date(invoice_payment.invoice&.accepted_at),
        format_date(invoice_payment.invoice&.invoice_document&.created_at), format_date(invoice_payment.invoice&.issued_at),
        invoice_payment.project.city, invoice_payment.project.street, invoice_payment.project.street_number,
        invoice_payment.project.apartment, invoice_payment.project.postcode, invoice_payment.project.post,
        invoice_payment.project.country, invoice_payment.invoice&.receiver_name, invoice_kind(invoice_payment.invoice&.kind),
        invoice_sending_method(invoice_payment.project.client.invoice_sending_method),
        payment_kind(invoice_payment.payment.kind),
        paper_invoice_send(invoice_payment.invoice, invoice_payment.project.client),
        electronic_invoice_send(invoice_payment.invoice, invoice_payment.project.client),
        client_email_address(invoice_payment.invoice), invoice_payment.invoice&.title,
        invoice_payment.project.bank_account&.name, format_account_number(invoice_payment.project.bank_account&.account_number),
        invoice_payment.project.bank_account&.bank_name, invoice_payment.invoice&.revenue_account&.key,
        invoice_payment.invoice&.revenue_account&.name
      ]
    end

    def generate_payment_row(scheduled_payment, csv)
      invoice = scheduled_payment.payment.invoices.last

      csv << [
        scheduled_payment.project.company.name, invoice&.number, scheduled_payment.project.client.name,
        scheduled_payment.project.name, scheduled_payment.project.id, scheduled_payment.payment.sell_date,
        scheduled_payment.payment.issued_on, payment_due_date(scheduled_payment.payment), scheduled_payment.amount / 100.0,
        scheduled_payment.currency, scheduled_payment.project.accounting_number&.number, scheduled_payment.mpk_number&.key,
        payment_invoice_status(scheduled_payment.payment), scheduled_payment.payment.description,
        invoice&.invoice_positions&.pluck(:name)&.join('; '), invoice&.description,
        format_date(scheduled_payment.created_at), format_date(invoice&.accepted_at),
        format_date(invoice&.invoice_document&.created_at), format_date(invoice&.issued_at), scheduled_payment.project.city,
        scheduled_payment.project.street, scheduled_payment.project.street_number, scheduled_payment.project.apartment,
        scheduled_payment.project.postcode, scheduled_payment.project.post, scheduled_payment.project.country,
        invoice&.receiver_name, invoice_kind(invoice&.kind),
        invoice_sending_method(scheduled_payment.project.client.invoice_sending_method),
        payment_kind(scheduled_payment.payment.kind),
        paper_invoice_send(invoice, scheduled_payment.project.client),
        electronic_invoice_send(invoice, scheduled_payment.project.client),
        client_email_address(invoice), invoice&.title, scheduled_payment.project.bank_account&.name,
        format_account_number(scheduled_payment.project.bank_account&.account_number),
        scheduled_payment.project.bank_account&.bank_name, invoice&.revenue_account&.key, invoice&.revenue_account&.name
      ]
    end
    # rubocop:enable Metrics/AbcSize, Metrics/MethodLength, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity

    def payment_due_date(payment)
      payment.issued_on + payment.payment_schedule.project.days_to_payment.days
    end

    def invoice_status(invoice)
      case invoice&.state
      when 'accepted' then 'zaakceptowana'
      when 'issued' then 'wystawiona'
      else
        ''
      end
    end

    def payment_invoice_status(payment)
      payment.pending_invoice ? 'oczekuje na akceptację' : 'niewystawiona'
    end

    def invoice_sending_method(invoice_sending_method)
      case invoice_sending_method
      when 'paper' then 'Faktura papierowa'
      when 'electronic' then 'Faktura elektronicza'
      else
        ''
      end
    end

    def invoice_kind(kind)
      case kind
      when 'advance' then 'Zaliczkowa'
      when 'advance_accounting' then 'Zaliczkowa rozliczająca'
      when 're_invoice' then 'Refaktura'
      when 'accounting_note' then 'Nota księgowa'
      when 'vat' then 'VAT'
      else
        ''
      end
    end

    def payment_kind(kind)
      case kind
      when 'invoice' then 'Faktura'
      when 'accounting_note' then 'Nota'
      else
        ''
      end
    end

    def paper_invoice_send(invoice, client)
      if client.paper_invoice_sending_method?
        invoice&.invoice_document.present?.to_s.upcase
      else
        '-'
      end
    end

    def electronic_invoice_send(invoice, client)
      if client.electronic_invoice_sending_method?
        invoice_issued(invoice).to_s.upcase
      else
        '-'
      end
    end

    def invoice_issued(invoice)
      return false if invoice.nil?

      invoice.issued?
    end

    def client_email_address(invoice)
      if invoice_issued(invoice)
        if invoice.invoice_sending_method_fallback_to_client?
          invoice.client.invoice_sending_email
        else
          invoice.client_address.invoice_sending_email
        end
      else
        '-'
      end
    end

    def format_account_number(input)
      if input.is_a?(Numeric) || input.to_s =~ /\A\d+\z/
        input.to_s.gsub(/(\d{4})(?=\d)/, '\1 ').strip
      else
        input
      end
    end

    def format_date(date)
      date.present? ? date.strftime('%Y-%m-%d') : ''
    end
  end
end
