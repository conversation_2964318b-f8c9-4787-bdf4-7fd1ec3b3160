require 'date_utils'

module ClickUpWorkingTimeApi
  module UserReportedTimeBuilder
    class << self
      include EmployeesHoursHelper
      include ClickUpWorkingTimeApi::Concerns::BuilderUtils

      def generate_summary(user, month = nil, year = nil) # rubocop:disable Metrics/AbcSize
        return unless user&.click_up_id

        team_id = user.company.click_up_workspace_id
        return if team_id.blank?

        selected_year  = year || Time.zone.today.year
        selected_month = month || Time.zone.today.month

        period        = time_period(user, selected_month, selected_year)
        options       = range_options(period.from, period.to).merge(assignee_options(user))
        required_time = required_reported_time(user, period)
        reported_time = actual_reported_time(team_id, options)
        filled        = reported_time >= required_time

        date_range = Struct.new(:reported, :required, :date_from, :date_to, :issuer_click_up, :filled?)
        date_range.new(reported_time, required_time, period.from, period.to, user.click_up_id, filled)
      end

      private

      def actual_reported_time(id, options)
        data = fetch_click_up_time_entries(id, options)
        data = filter_entries_with_folder_id(data)
        data = filter_with_current_range(data, options[:start_date], options[:end_date])
        sum_click_up_hours(data)
      end
    end
  end
end
