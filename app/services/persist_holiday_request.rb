class PersistHolidayRequest
  include Base::PersistableService

  def call
    form = HolidayRequestForm.new(object)
    form.assign_attributes(params) if params.present?
    form.actor = actor
    form.policy = keyrest[:policy]
    if form.persist_object
      PostProcessHolidayRequest.async_call(
        actor: actor,
        action: form.form_action.to_s,
        object: form.object,
        notable_changes: form.notable_changes.as_json
      )
    end
    form
  end
end
