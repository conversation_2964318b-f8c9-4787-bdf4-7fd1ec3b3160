class InvoiceXlsxGenerator
  include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>

  def initialize(invoice)
    @invoice = invoice.decorate
  end

  def generate
    p = Axlsx::Package.new
    wb = p.workbook
    add_styles(wb)
    add_worksheet(wb)

    p.to_stream
  end

  private

  # rubocop:disable Metrics/AbcSize, Metrics/MethodLength, Metrics/CyclomaticComplexity,
  # rubocop:disable Metrics/PerceivedComplexity

  def add_worksheet(workbook)
    workbook.add_worksheet(name: 'Zlecenie faktury') do |sheet| # rubocop:disable Metrics/BlockLength
      r = sheet.add_row([title(@invoice),
                         *Array.new(mpk_numbers.count + 1, '')],
                        style: @header, height: 22)
      sheet.merge_cells("A#{index(r)}:#{table_ending}#{index(r)}")

      sheet.add_row([])

      current_number = 1

      r = sheet.add_row([current_number, 'Numer projektu', project.account_number, *empty_cells],
                        style: [@standard, @row_title, @acc_number, *empty_cells_styles])
      merge_data_cells(sheet, index(r))
      current_number += 1

      r = sheet.add_row([current_number, 'Numer faktury', @invoice.number, *empty_cells],
                        style: [@standard, @row_title, @acc_number, *empty_cells_styles])
      merge_data_cells(sheet, index(r))
      current_number += 1

      if @invoice.amendment?
        r = sheet.add_row([current_number, 'Numer faktury oryginalnej', @invoice.amends.number, *empty_cells],
                          style: [@standard, @row_title, @acc_number, *empty_cells_styles],
                          height: 30)
        merge_data_cells(sheet, index(r))
        current_number += 1
      end

      if @invoice.advance_accounting?
        r = sheet.add_row([current_number, 'Numer faktury zaliczkowej',
                           @invoice.associated_advance_invoice.number, *empty_cells],
                          style: [@standard, @row_title, @acc_number, *empty_cells_styles],
                          height: 30)
        merge_data_cells(sheet, index(r))
        current_number += 1
      end

      r = sheet.add_row([current_number, 'Numer MPK', mpk_numbers.map(&:key)].flatten,
                        style: [@standard, @row_title,
                                Array.new(mpk_numbers.count) { @standard }].flatten)
      sheet.add_row(['', '', mpk_numbers.map(&:name)].flatten, style: @mpk_number, height: 33)
      sheet.merge_cells("A#{index(r)}:A#{index(r) + 1}")
      sheet.merge_cells("B#{index(r)}:B#{index(r) + 1}")
      current_number += 1

      r = sheet.add_row([current_number, division_title, mpk_divisions].flatten,
                        style: [@standard, @row_title,
                                Array.new(mpk_numbers.count) { @numbers }].flatten,
                        height: 44)
      mpk_numbers_row = index(r)
      current_number += 1

      r = add_data_row(sheet, current_number, 'Typ faktury', invoice_type(@invoice))
      current_number += 1
      merge_area_start = index(r)

      add_data_row(sheet, current_number, 'Podmiot', client_id(@invoice))
      current_number += 1

      add_data_row(sheet, current_number, 'NIP', @invoice.vat_number)
      current_number += 1

      add_data_row(sheet, current_number, 'Dane odbiorcy (kontrahenta)', format_address(@invoice),
                   height: 66)
      current_number += 1

      add_data_row(sheet, current_number, 'Numer konta przychodów', revenue_account, height: 25)
      current_number += 1

      add_data_row(sheet, current_number, 'Wystawca faktury', project.company.name, height: 22)
      current_number += 1

      add_positions(sheet, current_number)
      current_number += 1

      if @invoice.amendment?
        subtrahend = add_data_row(sheet, "#{current_number} a", 'Kwota netto przed korektą',
                                  @invoice.previous_amount / 100.0,
                                  height: 30, style: [@standard, @row_title, @currency])
        minuend = add_data_row(sheet, "#{current_number} b", 'Kwota netto po korekcie',
                               "=SUM(C#{mpk_numbers_row}:#{table_ending}#{mpk_numbers_row})",
                               height: 22, style: [@standard, @row_title, @currency])
        add_data_row(sheet, "#{current_number} c", 'Kwota netto korekty',
                     "=C#{minuend.row_index + 1}-C#{subtrahend.row_index + 1}",
                     height: 22, style: [@standard, @row_title, @currency])
      elsif @invoice.advance?
        add_data_row(sheet, "#{current_number} a", 'Kwota netto pobranej zaliczki',
                     "=SUM(C#{mpk_numbers_row}:#{table_ending}#{mpk_numbers_row})",
                     height: 33, style: [@standard, @row_title, @currency])
        add_data_row(sheet, "#{current_number} b", 'Kwota netto całego zamówienia',
                     @invoice.total_order_amount / 100.0, height: 30,
                                                          style: [@standard, @row_title, @currency])
      elsif @invoice.advance_accounting?
        add_data_row(sheet, "#{current_number} a", 'Kwota netto pobranej zaliczki',
                     "=SUM(C#{mpk_numbers_row}:#{table_ending}#{mpk_numbers_row})",
                     height: 33, style: [@standard, @row_title, @currency])
        add_data_row(sheet, "#{current_number} b", 'Kwota netto całego zamówienia',
                     @invoice.associated_advance_invoice.total_order_amount / 100.0,
                     height: 30, style: [@standard, @row_title, @currency])
      else
        add_data_row(sheet, current_number, 'Kwota netto',
                     "=SUM(C#{mpk_numbers_row}:#{table_ending}#{mpk_numbers_row})",
                     height: 22, style: [@standard, @row_title, @currency])
      end
      current_number += 1

      add_data_row(sheet, current_number, 'Nr rachunku bankowego',
                   @invoice.bank_account&.account_number, types: %i[string string string])
      current_number += 1

      sheet.add_row([current_number, 'Data wystawienia', @invoice.invoice_date, *empty_cells],
                    style: [@standard, @row_title, @date_format, *empty_cells_styles])
      current_number += 1

      sheet.add_row([current_number, 'Data sprzedaży (zakończenia usługi)',
                     @invoice.sell_date, *empty_cells],
                    style: [@standard, @row_title, @date_format, *empty_cells_styles], height: 33)
      current_number += 1

      sheet.add_row([current_number, 'Data płatności', @invoice.due_date, *empty_cells],
                    style: [@standard, @row_title, @date_format, *empty_cells_styles])
      current_number += 1

      r = add_data_row(sheet, current_number, 'Komentarz', @invoice.description, height: 33)
      current_number += 1

      if @invoice.amendment?
        r = add_data_row(sheet, current_number, 'Przyczyna korekty',
                         @invoice.correction_reason, height: 33)
      end

      merge_data_cells(sheet, merge_area_start..index(r))

      sheet.column_widths(mpk_numbers_row, 25, *Array.new(mpk_numbers.count) { 12 })

      prepare_page_setup(sheet)
    end
  end
  # rubocop:enable Metrics/AbcSize, Metrics/MethodLength, Metrics/CyclomaticComplexity,
  # rubocop:enable Metrics/PerceivedComplexity

  def add_positions(sheet, current_number) # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
    @invoice.invoice_positions.each_with_index do |invoice_position, index|
      number = index + 1
      add_data_row(sheet, "#{current_number}.#{number}.1", 'Nazwa pozycji', invoice_position.name)
      add_data_row(sheet, "#{current_number}.#{number}.2", 'Cena netto',
                   invoice_position.unit_price, height: 22,
                                                style: [@standard, @row_title, @currency])
      add_data_row(sheet, "#{current_number}.#{number}.3", 'Ilość', invoice_position.amount,
                   height: 22)
      add_data_row(sheet, "#{current_number}.#{number}.4", 'Wartość netto',
                   invoice_position.net_value, height: 22,
                                               style: [@standard, @row_title, @currency])
      add_data_row(sheet, "#{current_number}.#{number}.5", 'Stawka VAT',
                   InvoicePosition.human_attribute_name("tax_rate.#{invoice_position.tax_rate || :np}"),
                   height: 22)
      add_data_row(sheet, "#{current_number}.#{number}.6", 'Kod GTU', invoice_position.jpk_gtu,
                   height: 22)
      add_data_row(sheet, "#{current_number}.#{number}.7", 'Kod transakcji JPK',
                   invoice_position.jpk_transaction_code, height: 22)
    end
  end

  def prepare_page_setup(sheet)
    sheet.page_setup.set(fit_to_height: 1, fit_to_width: 1, scale: 100,
                         orientation: :landscape, paper_size: 9)
    sheet.page_margins.set(bottom: 0.39375, top: 0.39375, left: 0.39375,
                           right: 0.39375, header: 0.511805555555555,
                           footer: 0.511805555555555)
  end

  def add_data_row(sheet, order, title, data, options = {})
    default_options = { style: [@standard, @row_title, @standard,
                                *empty_cells_styles] }
    sheet.add_row([order, title, data, *empty_cells],
                  default_options.merge(options))
  end

  def empty_cells_count
    @empty_cells_count ||= mpk_numbers.count - 1
  end

  def empty_cells_styles
    @empty_cells_styles ||= Array.new(empty_cells_count, @standard)
  end

  def empty_cells
    @empty_cells ||= Array.new(empty_cells_count, '')
  end

  def add_styles(workbook) # rubocop:disable Metrics/MethodLength
    workbook.styles do |s|
      default_border = { style: :thin, color: '000000' }
      @header = s.add_style(bg_color: '8cc1ff', b: true, sz: 18, alignment: {
                              horizontal: :center, vertical: :center
                            }, border: default_border)
      @acc_number = s.add_style(bg_color: 'ffed00', b: true, sz: 16,
                                alignment: { horizontal: :center,
                                             vertical: :center },
                                border: default_border)
      @standard = s.add_style(sz: 12, b: true, alignment: {
                                horizontal: :center, vertical: :center,
                                wrap_text: true
                              }, border: default_border)
      @numbers = s.add_style(sz: 12, b: true, alignment: {
                               horizontal: :center, vertical: :center,
                               wrap_text: true
                             }, border: default_border,
                             format_code: '#,##0.00')
      @row_title = s.add_style(b: true, sz: 12, alignment: {
                                 horizontal: :left, vertical: :center,
                                 wrap_text: true
                               }, border: default_border)
      @mpk_number = s.add_style(sz: 12, alignment:
                                  { horizontal: :center, vertical: :center,
                                    wrap_text: true }, border: default_border)
      @date_format = s.add_style(sz: 12, b: true, format_code: 'DD/MM/YYYY',
                                 alignment: { horizontal: :center,
                                              vertical: :center },
                                 border: default_border)
      cur = @invoice.payment.currency

      @currency = s.add_style(
        sz: 12, b: true, format_code: "#,##0.00 [$#{cur}];-#,##0.00 [$#{cur}]",
        alignment: { horizontal: :center, vertical: :center },
        border: default_border
      )
    end
  end

  def revenue_account
    account = @invoice.revenue_account
    "#{account.key} #{account.name}"
  end

  def merge_data_cells(sheet, indices)
    indices = [indices] unless indices.respond_to?(:each)
    indices.each do |index|
      sheet.merge_cells("C#{index}:#{table_ending}#{index}")
    end
  end

  def format_address(invoice) # rubocop:disable Metrics/AbcSize
    address = "#{invoice.client_name}\n"
    address += invoice.city
    address += " #{invoice.street}" if invoice.street.present?
    address += if invoice.apartment.present?
                 " #{invoice.street_number}/#{invoice.apartment}\n"
               else
                 " #{invoice.street_number}\n"
               end
    address += "#{invoice.additional_address}\n" if invoice.additional_address
    address + "#{invoice.postcode} #{invoice.post}"
  end

  def mpk_positions
    @mpk_positions ||= @invoice.mpk_positions.to_a
  end

  def project
    @project ||= @invoice.payment.payment_schedule.project
  end

  def mpk_divisions
    mpk_numbers.map do |mpk_number|
      positions = mpk_positions.select do |mp|
        mp.mpk_number_id == mpk_number.id
      end
      next if positions.empty?

      amount_for_mpk_number(positions)
    end
  end

  def amount_for_mpk_number(positions)
    amount = positions.inject(nil) do |sum, mp|
      (sum || 0) + mp.amount
    end
    amount.present? ? amount / 100.0 : 'x'
  end

  def index(row)
    row.row_index + 1
  end

  def division_title
    if @invoice.amendment?
      'Podział kwoty netto po korekcie na MPK'
    else
      'Podział kwoty netto na MPK'
    end
  end

  def table_ending
    return @table_ending if @table_ending

    columns_count = MpkNumber.count + 2
    @table_ending = column_letter(columns_count)
  end

  def column_letter(number)
    alphabet = ('A'..'Z').to_a
    return '' if number < 1

    string = ''
    loop do
      number, remainder = (number - 1).divmod(26)
      string.prepend(alphabet[remainder])
      break if number.zero?
    end
    string
  end

  def mpk_numbers
    @mpk_numbers ||= MpkNumber.all.to_a.sort_by { |n| n.key.to_i }
  end

  def invoice_type(invoice) # rubocop:disable Metrics/CyclomaticComplexity
    return 'Korekta faktury' if invoice.amendment?

    case invoice.kind # rubocop:disable Style/HashLikeCase
    when 're_invoice' then 'Refaktura'
    when 'advance' then 'Zaliczkowa'
    when 'advance_accounting' then 'Zaliczkowa rozliczająca'
    when 'accounting_note' then 'Nota księgowa'
    when 'vat' then 'VAT'
    when 'vat_barter' then 'Barter z VAT'
    end
  end

  def title(invoice)
    return 'Zlecenie korekta' if invoice.amendment?

    case invoice.kind # rubocop:disable Style/HashLikeCase
    when 're_invoice' then 'Zlecenie refaktura'
    when 'advance' then 'Zlecenie faktura zaliczkowa'
    when 'advance_accounting' then 'Zlecenie faktura zaliczkowa rozliczająca'
    when 'accounting_note' then 'Zlecenie nota księgowa'
    when 'vat_barter' then 'Zlecenie barteru'
    end
  end

  def client_id(invoice)
    (invoice.client_address&.vat_number || invoice.client.vat_number).gsub(/\W/, '')
  end
end
