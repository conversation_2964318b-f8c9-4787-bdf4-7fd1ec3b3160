class PostProcessDestroyedHolidayRequest
  include Base::PostProcessableService
  include HolidayRequests::NotifierCommons

  def call
    process(all_opts.delete(:object), all_opts)
  end

  private

  def process(holiday_request, opts = {})
    opts[:last_editor_id] = holiday_request.updated_by_user_id
    schedule_dispatch(holiday_request, opts)
  end

  # @overload
  def gather_receipients(holiday_request, opts = {})
    receipients = []
    receipients = add_applicant_to_receipients(receipients, holiday_request, opts)
    receipients.uniq
  end
end
