class UsernameAndEmailGenerator
  def call(user)
    return unless ((user.first_name_changed? || user.last_name_changed? ||
                    user.company_id_changed?) || user.new_record? ||
                    (user.email.present? && user.email != user.email_was)) &&
                  (user.company_id || user.email) && user.first_name && user.last_name
    id = user.id
    first_name = user.first_name.to_s.parameterize
    last_name = parametrized_last_name(user.last_name)
    username = user.company ? first_name.first + last_name.gsub(/\+{1,}/, '') : user.email
    if user.company
      username = GenerateUsername.new.call(username, id)
      email, email_aliases = GenerateEmail.new.call(
        user, username_for_email(first_name, last_name, username)
      )
    else
      email = user.email
    end
    [username, email, email_aliases].compact
  end

  private

  def username_for_email(first_name, last_name, username)
    last_name.include?('+') ? first_name.first + last_name : username
  end

  def parametrized_last_name(last_name)
    last_name.to_s.gsub(/\++/, '+').split('+').map(&:parameterize).join('+')
             .gsub(/-.*/, '')
  end
end
