class UsersXlsxGenerator
  def initialize(users)
    @users = users.includes(:position, :company, :cards, versions: :item, department: :mpk_number)
  end

  def generate
    p = Axlsx::Package.new
    wb = p.workbook
    add_styles(wb)
    add_worksheet(wb)

    p.to_stream
  end

  private

  def add_worksheet(workbook)
    # worksheet names cannot contain more than 31 characters or contain any of the following characters: / \ ? * : [ ]
    timestamp = Time.current.localtime.to_fs(:db).tr(':', '_')
    workbook.add_worksheet(name: "Users #{timestamp}") do |sheet|
      add_header(sheet)

      @users.each do |user|
        add_user(sheet, user)
      end

      add_dimensions(sheet)
      add_auto_filter(sheet)
      add_pane(sheet)
    end
  end

  def add_header(sheet)
    sheet.add_row(['First name', 'Last name', 'Login', 'ID', 'Email', 'Position', 'Department', 'Company', 'MPK Number',
                   'State', 'Locked at', 'Contract of employment', 'Remote', 'Part-time', 'System user', 'Creation date',
                   'Activation date', 'Uid number', 'Profile comment', 'Absence quota', 'Absence balance',
                   'Sick absence quota', 'Sick absence balance', 'Has a business card', 'Cloudshare', 'Redmine',
                   'Chat', 'Monitoring', 'SVN', 'Docs cloud', 'Time reports not required',
                   'Entry card ID', 'Internal'],
                  style: @header)
  end

  def add_user(sheet, user) # rubocop:disable Metrics/AbcSize
    sheet.add_row([user.first_name, user.last_name, user.username, user.id, user.email, user.position&.name,
                   user.department&.name, user.company&.name, user.mpk_number&.decorate&.full_name, user.state,
                   locked_at(user), boolean_value(user.contract_of_employment), boolean_value(user.remote),
                   boolean_value(user.part_time), boolean_value(user.system), user.created_at, user.activates_on,
                   user.uid_number, user.profile_comment, user.absence_quota, user.absence_balance, user.sick_absence_quota,
                   user.sick_absence_balance, boolean_value(user.cards.any?), boolean_value(user.cloud),
                   boolean_value(user.redmine), boolean_value(user.chat), boolean_value(user.monitoring),
                   boolean_value(user.svn), boolean_value(user.docs_cloud), boolean_value(user.time_reports_not_required),
                   user.current_entry_card&.card_number, boolean_value(user.internal?)],
                  style: [*Array.new(10, @text), @date, *Array.new(4, @text), @date, @date, *Array.new(6, @text),
                          *Array.new(10, @text)])
  end

  def locked_at(user)
    return unless user.locked?

    user.versions.reverse_each.find { |version| version.changeset >= { 'state' => %w[active locked] } }&.created_at
  end

  def boolean_value(value)
    value ? 'TRUE' : 'FALSE'
  end

  def add_styles(workbook)
    workbook.styles do |s|
      default = { font_name: 'Arial', sz: 10, border: { style: :thin, color: '000000' } }
      standard = default.merge(bg_color: 'ecf1f9')

      @header = s.add_style(default.merge(b: true, bg_color: 'c5d4ec',
                                          alignment: { horizontal: :center, vertical: :center, wrap_text: true }))
      @text = s.add_style(standard)
      @date = s.add_style(standard.merge(format_code: 'YYYY-MM-DD'))
    end
  end

  def add_dimensions(sheet)
    sheet.column_widths(15, 15, 10, 8, 30, 30, 30, 15, 30, 10,
                        15, 23, 12, 12, 12, 15, 15, 15, 20, 15,
                        15, 20, 20, 20, 12, 12, 12, 12, 12, 12,
                        25, 20, 12)

    sheet.rows[0].height = 20
    sheet.rows[1..].each do |r|
      r.height = 15
    end
  end

  def add_auto_filter(sheet)
    sheet.auto_filter = "A1:AG#{sheet.rows.length}"
  end

  def add_pane(sheet)
    sheet.sheet_view.pane do |pane|
      pane.top_left_cell = 'A2'
      pane.state = :frozen_split
      pane.y_split = 1
    end
  end
end
