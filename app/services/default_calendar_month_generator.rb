class DefaultCalendarMonthGenerator
  def initialize(year, month)
    raise ArgumentError unless Date.valid_date?(year, month, 1)

    @year = year
    @month = month
  end

  def pl_full_time
    @pl_full_time ||= build_calendar_month.tap do |calendar_month|
      remove_days(calendar_month, &:saturday?)
      remove_days(calendar_month, &:sunday?)
      remove_days(calendar_month) { |date| Holidays.on(date, :pl).any? }
      set_hours(calendar_month, 8)
    end
  end

  def pl_half_time
    @pl_half_time ||= build_calendar_month.tap do |calendar_month|
      remove_days(calendar_month, &:saturday?)
      remove_days(calendar_month, &:sunday?)
      remove_days(calendar_month) { |date| Holidays.on(date, :pl).any? }
      set_hours(calendar_month, 4)
    end
  end

  def rb_full_time
    @rb_full_time ||= build_calendar_month.tap do |calendar_month|
      remove_days(calendar_month, &:friday?)
      remove_days(calendar_month, &:saturday?)
      set_hours(calendar_month, 8)
    end
  end

  private

  attr_reader :year, :month

  def build_calendar_month
    CalendarMonth.new(year:, month:, days:)
  end

  def days
    dates.to_h { |date| [date.day, nil] }
  end

  def dates
    @dates ||= (Date.new(year, month, 1)..Date.new(year, month, -1)).to_a
  end

  def remove_days(calendar_month, &)
    dates.select(&).map(&:day).each do |day|
      calendar_month.days.delete(day)
    end
  end

  def set_hours(calendar_month, hours)
    calendar_month.days.transform_values! { hours }
    calendar_month.total_hours = calendar_month.days.values.sum
  end
end
