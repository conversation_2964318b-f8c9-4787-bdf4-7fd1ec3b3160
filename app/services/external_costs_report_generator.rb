class ExternalCostsReportGenerator
  def self.generate(scope)
    new(scope).generate
  end

  def initialize(scope)
    @scope = scope.includes(:project)
  end

  def generate
    p = Axlsx::Package.new
    wb = p.workbook
    add_styles(wb)
    add_worksheet(wb)

    p.to_stream
  end

  private

  def add_worksheet(workbook)
    timestamp = Time.current.to_date
    workbook.add_worksheet(name: "External costs #{timestamp}") do |sheet|
      add_header(sheet)

      @scope.find_each do |external_cost|
        add_row(sheet, external_cost)
      end

      add_dimensions(sheet)
      add_auto_filter(sheet)
      add_pane(sheet)
    end
  end

  def add_header(sheet)
    sheet.add_row(['ID', 'Company', 'Client', 'Accounting number', 'Project', 'MPK number', 'MPK name', 'Contractor',
                   'Cost date', 'Amount', 'Currency', 'Description', 'Comment', 'Created at', 'City', 'Street',
                   'Street number', 'Apartment', 'Postcode', 'Post', 'Country', 'Bank account name', 'Account number',
                   'Created by'],
                  style: @header)
  end

  def add_row(sheet, external_cost) # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
    sheet.add_row([external_cost.id, external_cost.project.company.name, external_cost.project&.client&.name,
                   external_cost.project.accounting_number&.number, external_cost.project&.name, external_cost.mpk_number&.key,
                   external_cost.mpk_number&.name, external_cost.contractor&.name, external_cost.cost_date,
                   (external_cost.amount / 100.0), external_cost.currency, external_cost.description,
                   external_cost.comment, external_cost.created_at.strftime('%Y-%m-%d'), external_cost.contractor&.city,
                   external_cost.contractor&.street, external_cost.contractor&.street_number, external_cost.contractor&.apartment,
                   external_cost.contractor&.postcode, external_cost.contractor&.post, external_cost.contractor&.country,
                   external_cost.project.bank_account&.name,
                   format_account_number(external_cost.project.bank_account&.account_number),
                   external_cost.created_by&.full_name],
                  style: [*Array.new(8, @text), @date, @amount, *Array.new(3, @text), @date, *Array.new(10, @text)])
  end

  def format_account_number(input)
    if input.is_a?(Numeric) || input.to_s =~ /\A\d+\z/
      input.to_s.gsub(/(\d{4})(?=\d)/, '\1 ').strip
    else
      input
    end
  end

  def add_styles(workbook)
    workbook.styles do |s|
      default = { font_name: 'Arial', sz: 8, border: { style: :thin, color: '000000' } }
      standard = default.merge(bg_color: 'dae3f3')

      @header = s.add_style(default.merge(b: true, bg_color: 'f4b183',
                                          alignment: { horizontal: :center, vertical: :center, wrap_text: true }))
      @text = s.add_style(standard)
      @date = s.add_style(standard.merge(format_code: 'YYYY-MM-DD'))
      @amount = s.add_style(standard.merge(format_code: '#,##0.00'))
    end
  end

  def add_dimensions(sheet)
    sheet.rows[0].height = 20
    sheet.rows[1..].each do |r|
      r.height = 15
    end
  end

  def add_pane(sheet)
    sheet.sheet_view.pane do |pane|
      pane.top_left_cell = 'A2'
      pane.state = :frozen_split
      pane.y_split = 1
    end
  end

  def add_auto_filter(sheet)
    sheet.auto_filter = "A1:X#{sheet.rows.length}"
  end
end
