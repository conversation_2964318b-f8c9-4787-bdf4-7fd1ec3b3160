module ClickUpImporter
  DEFAULT_STEP = 100

  def initialize
    @options = Hash.new { |h, k| h[k] = [] }
  end

  def limit(count)
    merge_options limit: count
  end

  def import(id = nil)
    params = default_limit
    params = params.merge(id: id) if id.present?
    fetch(params)
  end

  private

  def default_limit
    if @options[:limit].present?
      { limit: @options[:limit] }
    else
      { limit: self.class::DEFAULT_STEP }
    end
  end

  def merge_options(opts)
    @options.merge! opts do |_key, oldval, newval|
      if oldval.is_a? Array
        oldval + newval
      else
        [oldval, newval]
      end
    end
    self
  end
end
