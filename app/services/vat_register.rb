module VATRegister
  ADDRESS = 'https://wl-api.mf.gov.pl/api/search/nip/'.freeze

  class << self
    def vat_subject?(tax_number) # rubocop:disable Metrics/AbcSize
      conn = Faraday.new(ADDRESS) do |f|
        f.response :json
        f.headers['Accept'] = 'application/json'
        f.headers['User-Agent'] = 'Mozilla/5.0 (compatible; VATChecker/1.0)'
      end

      response = conn.get(tax_number, { date: Time.zone.today })

      Rails.logger.info "VATRegister-vat_subject: Status: #{response.status}"
      Rails.logger.info "VATRegister-vat_subject: Content-Type: #{response.headers['content-type']}"
      Rails.logger.info "VATRegister-vat_subject: Body: #{response.body}"

      response.body.dig('result', 'subject', 'statusVat') == 'Czynny'
    end
  end
end
