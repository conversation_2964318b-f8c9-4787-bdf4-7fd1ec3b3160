module PermissionAdvisor
  class PermissionKit
    ALL = {
      'EDIT_USER' => { activities: Set['users:update', 'groups:index', 'groups:update'] },
      'LIST_PROJECTS' => { activities: Set['users:show', 'projects:index'] },
      'UPDATE_GROUP_USERS' => { activities: Set['groups:update', 'users:index'] },
      'CREATE_AND_VIEW_PROJECT' => { activities: Set['projects:create', 'projects:show', 'companies:index'] },
      'UPDATE_GLOBAL_ROLE_ACTIVITIES' => { activities: Set['global_roles:update', 'activities:index'],
                                           requirements: ['Global System Administrator'] },
      # project related - evaluation is not decisive, just checks if there is any occurance of permission based on current role assignments to projects
      'ADD_SUBPROJECTS' => { activities: Set['projects:create', 'projects:show', 'companies:index'], # CREATE_AND_VIEW_PROJECT
                             project_role_requirements: [':add_subprojects'] },
      'EDIT_PROJECT' => { activities: Set['projects:update'],
                          project_role_requirements: [':edit_project'] },
      'CLOSE_PROJECT' => { activities: Set['projects:close'],
                           project_role_requirements: [':close_project'] },
      'LIST_PROJECT_MEMBERSHIPS' => { activities: Set['projects:show', 'memberships:index'] },
      'EDIT_PROJECT_MEMBERSHIPS' => { activities: Set['projects:show', 'memberships:create', 'memberships:update', 'roles:index', 'users:index'], project_role_requirements: [':manage_members'] },
      'GENERAL FLOW' => { activities: Set['dms-cost_invoices:general_flow'] }
    }.freeze

    def self.names
      ALL.keys.sort
    end
  end
end
