module HolidayRequestForms
  class CategorySelectionValidator < ActiveModel::EachValidator
    def validate_each(record, attribute, value)
      call(record, attribute, value)
    end

    private

    def call(record, attribute, value)
      return unless record.category_changed?

      policy = HolidayRequestPolicy.new(record.actor, HolidayRequest)
      return if policy.categories.include?(value)

      record.errors.add(attribute, :category_not_permittable)
    end
  end
end
