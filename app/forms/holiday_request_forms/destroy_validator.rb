module HolidayRequestForms
  class DestroyValidator < ActiveModel::Validator
    def validate(record)
      call(record)
    end

    private

    def call(record)
      bulk_permission_check = record.policy.public_send(:bulk_permission_check, %w(global_admin hr_user chiefy))
      can_destroy_accepted = bulk_permission_check[0] ||
                             bulk_permission_check[1] ||
                             bulk_permission_check[2] ||
                             false ||
                             record.actor.board_member?

      if record.accepted_at && record.actor.id == record.applicant_id && !can_destroy_accepted
        record.errors.add(:base, 'Applicant cannot delete an accepted holiday request!')
      end
    end
  end
end
