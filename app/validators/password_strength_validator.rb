class PasswordStrengthValidator < ActiveModel::EachValidator
  def validate_each(record, attribute, value)
    return if value.nil? && options[:allow_nil]
    return if !value.nil? && value.to_s.empty? && options[:allow_blank]
    fallback_to_regexp = false
    if options[:cracklib]
      begin
        # require 'cracklib'
        require 'cracklib_reloaded'
        cracklib_check(record, attribute, value)
      rescue LoadError => e
        fallback_to_regexp = true
        # Centos 5.x is not supported!
        Rails.logger.error e.message
      end
    end
    if options[:regexp] || fallback_to_regexp
      regexp_check(record, attribute, value)
    end
  end

  private

  # def cracklib_check(record, attribute, value)
  #   result = CrackLib::Fascist(value.to_s)
  #   unless result.ok?
  #     message = result.reason.to_s
  #     record.errors.add(attribute, message)
  #   end
  #   result
  # end
  def cracklib_check(record, attribute, value)
    password = CracklibReloaded::Password.new
    result = password.weak?(value.to_s)
    if result
      message = password.errors.to_h[:password].try(:first)
      # => {:password=>["it is based on a dictionary word"]}
      # redundant or not needed info
      unless ['it is too short', 'it is based on your username'].include?(message)
        record.errors.add(attribute, message)
      end
    end
    result
  end

  def regexp_check(record, attribute, value)
    result = value =~ /(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}/
    unless result
      message = "must include at least: \
                8 characters, \
                one latin lowercase letter, \
                one latin uppercase letter \
                and one arabic numeral".squish

      record.errors.add(attribute, message)
    end
    result
  end
end
