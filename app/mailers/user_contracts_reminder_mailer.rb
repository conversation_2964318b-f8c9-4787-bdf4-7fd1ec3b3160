class UserContractsReminderMailer < ApplicationMailer
  def six_weeks_chief_reminder(user_contract)
    @user_contract = user_contract

    I18n.with_locale(:pl) do
      mail to: chief(user_contract)&.email,
           subject: t('mailers.user_contracts_reminder_mailer.subject')
    end
  end

  def six_weeks_hr_reminder(user_contract)
    @user_contract = user_contract

    I18n.with_locale(:pl) do
      mail to: hr_coordinator_emails, subject: t('mailers.user_contracts_reminder_mailer.subject')
    end
  end

  def four_weeks_chief_reminder(user_contract)
    @user_contract = user_contract

    I18n.with_locale(:pl) do
      mail to: chief(user_contract)&.email,
           subject: t('mailers.user_contracts_reminder_mailer.subject')
    end
  end

  def four_weeks_hr_reminder(user_contract)
    @user_contract = user_contract

    I18n.with_locale(:pl) do
      mail to: hr_coordinator_emails,
           subject: t('mailers.user_contracts_reminder_mailer.subject')
    end
  end

  private

  def hr_coordinator_emails
    User.joins(:global_roles).active.where(global_roles: { remote_work_periods_notifications: true }).pluck(:email)
  end

  def chief(user_contract)
    user_contract.user&.department&.chief
  end
end
