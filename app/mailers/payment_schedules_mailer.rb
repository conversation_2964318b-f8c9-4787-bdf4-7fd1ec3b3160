class PaymentSchedulesMailer < ApplicationMailer
  def create_schedule_reminder(project, emails)
    @project = project
    mail(to: emails, subject: 'Przypomnienie o harmonogramie płatności')
  end

  def payment_schedule_edited(payment_schedule, emails)
    @project = payment_schedule.project
    mail(to: emails, subject: 'Nastąpiła zmiana harmonogramu płatności')
  end

  def upcoming_payment(payment, project, emails)
    @payment = payment
    @project = project
    mail(to: emails, subject: 'Zbliża się termin wystawienia faktury')
  end

  def payment_deadline_exceeded(payment, project, emails)
    @payment = payment
    @project = project
    mail(to: emails, subject: 'Termin wystawienia faktury został przekroczony')
  end
end
