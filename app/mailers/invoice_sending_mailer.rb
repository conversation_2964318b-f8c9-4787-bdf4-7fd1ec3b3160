class InvoiceSendingMailer < ApplicationMailer
  FULL_COMPANY_NAMES = {
    Efigence: 'Efigence S.A.',
    Artegence: 'Artegence Sp. z o.o.'
  }.with_indifferent_access.freeze

  default from: -> { Settings.accounting['invoice_sending_email_from'][@company_name] }

  layout 'mailer_with_company'

  def invoice_issued(invoice, user) # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
    @invoice_number = invoice.number
    @company = invoice.project.company
    @company_name = invoice.project.company.name

    I18n.with_locale(:pl) do
      subject = t('mailers.invoice_sending_mailer.invoice_issued.subject',
                  company_name: full_company_name, invoice_number: @invoice_number)

      attachments[invoice.invoice_document.document.original_filename] = invoice.invoice_document.document.read
      invoice.required_attachments.map(&:attachment).each do |attachment|
        attachments[attachment.file.original_filename] = attachment.file.read
      end

      mail(to: email_address(invoice), subject: subject,
           bcc: Settings.accounting['invoice_sending_email_bcc'][@company_name])

      invoice.create_snapshot(user, :email,
                              comment: "Email to report an invoice issue has been sent to: #{email_address(invoice)}")
    end
  end

  private

  def email_address(invoice)
    if invoice.invoice_sending_method_fallback_to_client?
      invoice.client.invoice_sending_email
    else
      invoice.client_address.invoice_sending_email
    end
  end

  def full_company_name
    FULL_COMPANY_NAMES[@company_name]
  end
end
