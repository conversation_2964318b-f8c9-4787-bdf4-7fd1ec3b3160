class ClickUpMailer < ApplicationMailer
  def difference_notification(diff_arr)
    @diff_obj = diff_arr
    @click_up_admins = click_up_admins

    recipients = (@click_up_admins + global_admins).uniq

    mail to: recipients, subject: t('mailers.click_up.difference_notification.subject')
  end

  private

  def global_admins
    User.active.joins(:global_roles).where(global_roles: { global_admin: true }).distinct.pluck(:email)
  end

  def click_up_admins
    User.active.where(admin_click_up: true).distinct.pluck(:email)
  end
end
