module Rodo
  class RodoMailer < ApplicationMailer
    def new_approval(approval)
      mail(to: approval.user.email, subject: subject(approval))
    end

    def rodo_reminder(user)
      subject = '[<PERSON><PERSON><PERSON><PERSON><PERSON>] Wiadomo<PERSON> w systemie Imperator'
      mail(to: user.email, subject: subject)
    end

    private

    def subject(approval)
      approvable = approval.approvable
      approvable_name = approvable.name
      if approval.approvable_type == 'Agreement'
        "[<PERSON>wiadomienie] #{approvable_name}"
      else
        "[Powiadomienie] #{approvable.project.name}: #{approvable_name}"
      end
    end
  end
end
