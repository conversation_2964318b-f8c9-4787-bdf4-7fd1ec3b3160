class ApplicationMailer < ActionMailer::Base
  include ActiveJobDeserializationErrorHandling

  default template_path: proc { "mailers/#{mailer_name}" }
  default 'X-Mailer-Name' => proc { mailer_name }

  layout 'mailer'

  MAILING_IMAGES_LOCATION = 'app/assets/images/mailers'.freeze

  private

  def controlling_emails
    User.active.joins(:global_roles)
        .where(global_roles: { notify_dms_controller_acceptances: true })
        .distinct
        .pluck(:email)
  end
end
