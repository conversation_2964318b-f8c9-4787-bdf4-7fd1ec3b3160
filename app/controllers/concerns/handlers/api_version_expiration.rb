require 'date'

# example: V1_EXPIRATION_DATE=2016-05-12
module Handlers
  module ApiVersionExpiration
    extend ActiveSupport::Concern
    include ApiVersion
    include ::Api::V1::Concerns::Handlers::Errors

    included do
      before_action :check_expiration!
    end

    private

    def expiration_date
      @expiration_date ||= api_version && ENV[api_version + '_EXPIRATION_DATE']
    end

    def check_expiration!
      return render_api_error(message: I18n.t('api.messages.version_expired', default: 'Version expired').to_s,
                              status: :upgrade_required) unless supported_version?
    end

    def supported_version?
      !expiration_date || Time.zone.today < Date.parse(expiration_date)
    end
  end
end
