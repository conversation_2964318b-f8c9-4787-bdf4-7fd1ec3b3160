module Api
  module V1
    class PublicKeysController < ApiController
      include ::Api::V1::Concerns::Documentation::PublicKeysEndpoint
      before_action :set_user

      def index
        authorize PublicKey
        @public_keys = scope
        respond_with(@public_keys)
      end

      def create
        authorize PublicKey
        key = scope.new(public_key_params)
        key.save
        respond_with(key.decorate)
      end

      def destroy
        authorize PublicKey
        key = scope.find(params[:id])
        key.destroy
        respond_with(key.decorate)
      end

      private

      def scope
        skip_policy_scope
        PublicKeyPolicy::Scope.new(current_user, PublicKey, @user).resolve
      end

      def set_user
        @user = User.find_by(id: params[:user_id])
      end

      def public_key_params
        params.require(:public_key)
              .permit(policy(PublicKey).permitted_attributes)
      end
    end
  end
end
