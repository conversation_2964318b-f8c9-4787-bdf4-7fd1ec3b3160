module Api
  module V1
    module Evaluations
      class EvaluationsController < ::Api::V1::Evaluations::UsersController
        before_action :find_user
        before_action :authorize

        def new
          @surveys = Survey.all
        end

        def create
          evaluation = @user.evaluations.new(evaluation_params)
          evaluation.created_by = current_user
          evaluation.save
          respond_with evaluation, location: [:evaluations, @user]
        end

        def update
          evaluation = @user.evaluations.pending.find(params[:id])
          evaluation.update(evaluation_params)
          respond_with evaluation, location: [:evaluations, @user]
        end

        def start
          evaluation = @user.evaluations.pending.find(params[:id])
          evaluation.start!
          head :no_content
        end

        def stop
          evaluation = @user.evaluations.in_progress.find(params[:id])
          evaluation.stop(evaluation_params)
          head :no_content
        end

        def discard
          evaluation = @user.evaluations.active.find(params[:id])
          evaluation.discard!
          head :no_content
        end

        def show
          @evaluation = @user.evaluations.find(params[:id]).decorate
          @additional_survey_answers = @evaluation.survey_answers.additional.includes(:user)
        end

        private

        def find_user
          @user = User.find(params[:user_id])
        end

        def authorize
          super(@user)
        end

        def evaluation_params
          params.require(:evaluation).permit(:starts_on, :ends_on, :name, :comment, :main_survey_id,
                                             :additional_survey_id,
                                             additional_user_ids: [],
                                             main_survey_attributes: [
                                               :name,
                                               { survey_questions_attributes: %i[kind content] }
                                             ],
                                             answer_comments_attributes: %i[id content])
        end
      end
    end
  end
end
