module Api
  module V1
    class BookingResourcesController < ApiController
      before_action :find_booking_resource, except: %i[index create form_data]

      def index
        authorize(BookingResource)
        results = search(scope: booking_resources_scope, filters: params[:f]).results
        @booking_resources = authorize!(results).decorate
        respond_with(@booking_resources)
      end

      def create
        authorize(BookingResource)
        @booking_resource = BookingResource.new(booking_resource_params)
        @booking_resource.save
        respond_with(@booking_resource.decorate)
      end

      def update
        authorize(@booking_resource)
        @booking_resource.update(booking_resource_params)
        respond_with(@booking_resource.decorate)
      end

      def show
        authorize(@booking_resource)
        respond_with(@booking_resource.decorate)
      end

      def destroy
        authorize(@booking_resource)
        @booking_resource.destroy
        respond_with(@booking_resource.decorate)
      end

      def form_data
        authorize(BookingResource)
      end

      private

      def search(options = {})
        @search = ::Searches::BookingResourceSearch.new(search_options(options))
      end

      def booking_resources_scope
        BookingResource.all
      end

      def find_booking_resource
        @booking_resource = booking_resources_scope.find(params[:id])
      end

      def booking_resource_params
        params.require(:booking_resource).permit(:id, :name, :identifier, :email,
                                                 :multiple_bookings, :kind, :created_at,
                                                 :updated_at)
      end
    end
  end
end
