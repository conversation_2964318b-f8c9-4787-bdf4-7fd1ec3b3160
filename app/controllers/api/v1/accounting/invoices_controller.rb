module Api
  module V1
    module Accounting
      class InvoicesController < ApiController
        before_action :find_invoice, only: %i[show invoice_document history back_to_pending]
        before_action :find_pending_invoice, only: %i[accept reject]
        before_action :find_accepted_invoice, only: %i[issue create_invoice_document]
        before_action :find_invoice_for_edition, only: %i[edit update]
        before_action :authorize
        before_action :find_kinds, only: %i[show edit]
        before_action :find_advance_invoices, only: :edit

        def index
          results = search(scope: scope, filters: params[:f]).results
          @invoices = paginate(results, search_options(params))
          @invoices = @invoices.includes(
            :attachments, :user, payment: [:current_invoice, { payment_schedule: :project }]
          )
          @invoices = @invoices.decorate
        end

        def show
          @invoice = @invoice.decorate
        end

        def accept
          @invoice.current_user = current_user
          @invoice.force_no_attachment = true
          @invoice.accept!(current_user)

          respond_with @invoice
        end

        def issue
          @invoice.current_user = current_user
          @invoice.force_no_attachment = true
          @invoice.issue!(current_user)

          respond_with @invoice
        end

        def reject
          @invoice.reject!(current_user)

          head :no_content
        end

        def back_to_pending
          @invoice.action_name = action_name
          @invoice.back_to_pending!(current_user)

          respond_with @invoice
        end

        def edit
          @invoice = @invoice.decorate
          @project = @invoice.project
          @revenue_accounts = RevenueAccount.all
          @payment = @invoice.payment
          @mpk_numbers = MpkNumber.order(:key).active(@invoice.sell_date)
          @tax_rates = InvoicePosition.tax_rates.keys
        end

        def update
          @invoice.allow_update = policy(@invoice).update?
          @invoice.create_snapshot(current_user, :update) if @invoice.update(invoice_params)

          @invoice.valid?(:prevent_indexing_errors) if @invoice.errors.any? # Workaround for https://github.com/rails/rails/issues/24390
          respond_with @invoice
        end

        def invoice_document
          document = @invoice.invoice_document&.document

          return head(:not_found) unless document

          file_path = Rails.root.join(document.storage.directory + document.id)
          send_file file_path, filename: document.original_filename,
                               type: document.mime_type,
                               disposition: 'inline'
        end

        def create_invoice_document # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity, Metrics/MethodLength
          if @invoice.invoice_document
            raise ActionController::BadRequest, @invoice.errors.generate_message(:invoice_document, :already_exists)
          end

          invoice_document_params = params.require(:invoice).permit(invoice_document_attributes: [:document])
          @invoice.create_snapshot(current_user, :add_invoice_document) if @invoice.update(invoice_document_params)

          if @invoice.errors.blank? && @invoice.project.client.paper_invoice_sending_method?
            next_day = Time.zone.now + 2.days
            next_day += (1 + ((7 - next_day.wday) % 7)).days if next_day.saturday? || next_day.sunday?

            @invoice.create_snapshot(current_user, :post,
                                     comment: "The invoice was sent by post on #{next_day.strftime('%d/%m/%Y')}.")
          end

          if @invoice.errors.present?
            render json: { errors: @invoice.errors.full_messages }, status: :unprocessable_entity
          else
            head :created
          end
        end

        def history
          @snapshots = @invoice.snapshots.includes(:snapshot_items, :user)
        end

        def monthly_report
          send_data InvoicesMonthlyReportGenerator.generate(monthly_report_params)
        rescue InvoicesMonthlyReportGenerator::Error
          render_bad_request
        end

        private

        def find_kinds
          @kinds = @invoice.payment.allowed_invoice_kinds
        end

        def find_advance_invoices
          client_id = @invoice.project.client_id
          @advance_invoices = Invoice.advance_issued_for(client_id)
        end

        def invoice_params
          params.require(:invoice).permit(
            :payment_id, :send, :revenue_account_id, :receiver_name, :total_amount,
            :due_date, :sell_date, :invoice_date, :correction_reason, :description, :file_name,
            :no_attachment, :force_no_attachment, :client_address_id, :kind,
            :associated_advance_invoice_id, :total_order_amount, :bank_account_id,
            :attachments_required,
            attachment_ids: [],
            mpk_positions_attributes: %i[amount mpk_number_id id project_id _destroy],
            invoice_positions_attributes: %i[name amount unit_price tax_rate id jpk_gtu jpk_transaction_code _destroy],
            required_attachments_attributes: %i[id attachment_id _destroy],
            invoice_document_attributes: %i[id _destroy]
          )
        end

        def authorize
          super(@invoice || Invoice)
        end

        def find_invoice
          @invoice = scope.find(params[:id])
        end

        def find_pending_invoice
          @invoice = scope.pending.find(params[:id])
        end

        def find_accepted_invoice
          @invoice = scope.accepted.find(params[:id])
        end

        def find_invoice_for_edition
          @invoice = scope.where(state: [1, 2, 4]).find(params[:id])
        end

        def search(options = {})
          ::Searches::InvoiceSearch.new(search_options(options))
        end

        def scope
          policy_scope(Invoice)
        end

        def monthly_report_params
          {
            date_from: params.require(:date_from),
            date_to: params.require(:date_to),
            attribute_name: params.require(:attribute_name),
            accounting_number_id: params[:accounting_number_id],
            project_id: params[:project_id],
            client_id: params[:client_id],
            company_id: params[:company_id]
          }.compact
        end
      end
    end
  end
end
