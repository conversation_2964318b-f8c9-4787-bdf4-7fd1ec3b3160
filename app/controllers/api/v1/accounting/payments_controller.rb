module Api
  module V1
    module Accounting
      class PaymentsController < ApiController
        before_action :authorize
        before_action :find_payment, only: %i[show update]

        def index
          results = search(scope: scope, filters: params[:f]).results
          @payments = paginate(results, search_options(params))
          @payments = @payments.includes(:current_invoice, :pending_invoice, :accepted_invoice,
                                         mpk_positions: :mpk_number, payment_schedule: :project)
        end

        def show; end

        def create
          payment = Payment.new(payment_params)
          payment.save

          if payment.save
            render json: payment, status: :created
          else
            render json: { errors: payment.errors.messages }, status: :unprocessable_entity
          end
        end

        def update
          @payment.update(payment_params)
          respond_with @payment
        end

        private

        def find_payment
          @payment = scope.find(params[:id])
        end

        def authorize
          super(Payment)
        end

        def search(options = {})
          ::Searches::PaymentSearch.new(search_options(options))
        end

        def scope
          policy_scope(Payment).order(issued_on: :desc)
        end

        def payment_params
          params.require(:payment).permit(
            :id, :issued_on, :sell_date, :predicted_amount, :description, :_destroy, :ends_on,
            :cyclic, :cycle_length, :kind, :currency, :payment_schedule_id,
            {
              mpk_positions_attributes: %i[id mpk_number_id amount project_id _destroy]
            }
          )
        end
      end
    end
  end
end
