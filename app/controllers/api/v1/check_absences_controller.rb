module Api
  module V1
    class CheckAbsencesController < ApiController
      skip_before_action :authenticate_user!
      skip_after_action :verify_authorized
      before_action :validate_params

      def check
        if request.env["HTTP_X_REDMINE_ABSENCES_CHECK_API_KEY"] == Settings.redmine_api['redmine_absences_check_api_key']
          dates = params[:dates].split(',')
          absence_dates = Absence.where(user_id: params[:user_id], date: dates).to_a.map(&:date).map(&:to_s)
          checked_dates = dates.reject { |day| absence_dates.include?(day) }
          respond_to do |format|
            format.json { render json: { checked_dates: checked_dates }, status: 200 }
          end
        else
          respond_to do |format|
            format.json { render json: {}, status: 401 }
          end
        end
      end

      private

      def validate_params
        render json: {}, status: :bad_request if params[:dates].blank?
      end
    end
  end
end
