module Api
  module V1
    class CostAccountNumbersController < ApiController
      before_action :skip_policy_scope
      before_action :find_cost_account_number, only: %i[show update]
      before_action :authorize

      def index
        @cost_account_numbers = search(scope: CostAccountNumber.order(number: :asc),
                                       filters: params[:f]).results
      end

      def show; end

      def create
        respond_with CostAccountNumber.create(cost_account_number_params)
      end

      def update
        @cost_account_number.update(cost_account_number_params)

        respond_with @cost_accounting_number
      end

      private

      def find_cost_account_number
        @cost_account_number = CostAccountNumber.find(params[:id])
      end

      def search(options = {})
        @search = ::Searches::CostAccountNumberSearch.new(search_options(options))
      end

      def authorize
        super(@cost_account_number || CostAccountNumber)
      end

      def cost_account_number_params
        params.require(:cost_account_number).permit(:number, :description, :active, :default, :kind)
      end
    end
  end
end
