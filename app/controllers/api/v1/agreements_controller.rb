module Api
  module V1
    class AgreementsController < ApiController
      include ::Api::V1::Concerns::Documentation::AgreementsEndpoint
      respond_to :json, :multipart_form
      before_action :find_agreement, only: %i[show update destroy]

      def index
        authorize(Agreement)
        results = search(scope: agreements_scope, filters: params[:f]).results
        @agreements = paginate(authorize!(results), search_options(params))
        raise ActiveRecord::RecordNotFound, 'Page not found' if empty_page?
        respond_with(@agreements)
      end

      def show
        authorize(@agreement)
        respond_with(@agreement.decorate)
      end

      def create
        authorize(Agreement)
        agreement = Agreement.new(agreement_params)
        if agreement.save
          render json: agreement.decorate, status: :created
        else
          render json: agreement.errors.messages, status: :unprocessable_entity
        end
      end

      def update
        authorize(@agreement)
        if @agreement.update(agreement_params)
          head :no_content
        else
          render json: @agreement.errors.messages, status: :unprocessable_entity
        end
      end

      def destroy
        authorize @agreement
        @agreement.destroy
        respond_with(@agreement.decorate)
      end

      def form_data
        authorize!(Agreement)
      end

      private

      def empty_page?
        params[:page].to_i > 1 && @agreements.empty?
      end

      def search(options = {})
        @search = ::Searches::AgreementSearch.new(search_options(options))
      end

      def agreements_scope
        Agreement.with_approvals_counts.includes(:companies)
      end

      def find_agreement
        @agreement = Agreement.find(params[:id])
      end

      def find_agreements
        agreements_scope.find(params[:ids])
      end

      def agreement_params
        params.require(:agreement).permit(policy(Agreement).permitted_attributes)
      end
    end
  end
end
