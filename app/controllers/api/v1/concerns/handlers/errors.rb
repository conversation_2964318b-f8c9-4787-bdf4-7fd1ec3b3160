# prevent responses other than json which in turn return 403, why?
module Api
  module V1
    module Concerns
      module Handlers
        module Errors
          extend ActiveSupport::Concern

          included do
            protected

            def render_api_error(arg)
              error = ApplicationError.new(arg)
              # NOTE: may not work in Rails 5, see https://github.com/rails/rails/issues/25106
              self.response_body = nil
              render template: '/api/v1/application_errors/show', locals: { error: error }, status: error.status
            end

            def render_aasm_error(error, payload = nil)
              self.response_body = nil
              render template: '/api/v1/aasm_errors/show', locals: { error: error, payload: payload }, status: :bad_request
            end

            def render_bad_request
              skip_authorization
              skip_policy_scope
              head :bad_request
            end

            def render_file_not_found
              skip_authorization
              skip_policy_scope
              head :not_found
            end
          end
        end
      end
    end
  end
end
