module Api
  module V1
    module Concerns
      module Documentation
        module UsersEndpoint
          include Swagger::Blocks
          extend ActiveSupport::Concern

          included do
            swagger_path '/api/users' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              operation :get do
                key :description, 'Provides a list of users based on the search criteria'
                key :operationId, 'getUsers'
                key :tags, [
                  'users'
                ]
                parameter do
                  key :name, 'f[collection_for_select]'
                  key :in, :query
                  key :description, 'return only fields for select, defaults to `false`, specify `page` or `per_page` to get paginated results'
                  key :required, false
                  key :type, :boolean
                end
                parameter do
                  key :name, 'f[not_member_of_project]'
                  key :in, :query
                  key :description, "return only users which are not project members \
                                     (ignores indirect membership through a group) \
                                     or all users if project id was not specified"
                  key :required, false
                  key :type, :string
                end
                parameter do
                  key :name, 'f[term]'
                  key :in, :query
                  key :description, 'filter by term'
                  key :required, false
                  key :type, :string
                end
                parameter do
                  key :name, 'f[state]'
                  key :in, :query
                  key :description, <<-HEREDOC.gsub(/(?:\n\r?|\r\n?)/, '<br>').squish.html_safe
                                    filter by state:
                                    ''  - all (default)
                                    '0' - active
                                    '1' - locked
                                    HEREDOC
                  key :required, false
                  key :type, :string
                  key :enum, [0, 1]
                end
                parameter do
                  key :name, 'f[provenance]'
                  key :in, :query
                  key :description, <<-HEREDOC.gsub(/(?:\n\r?|\r\n?)/, '<br>').squish.html_safe
                                    filter by provenance:
                                    ''  - all (default)
                                    '0' - internal
                                    '1' - external
                                    HEREDOC
                  key :required, false
                  key :type, :string
                  key :enum, [0, 1]
                end
                parameter do
                  key :name, 'f[sort]'
                  key :in, :query
                  key :description, 'sort by field in direction'
                  key :required, false
                  key :type, :string
                  key :enum, ['id asc', 'id desc', 'created_at asc', 'created_at desc', 'updated_at asc', 'updated_at desc']
                end
                parameter do
                  key :name, 'page'
                  key :in, :query
                  key :description, 'page number'
                  key :required, false
                  key :type, :integer
                end
                parameter do
                  key :name, 'per_page'
                  key :in, :query
                  key :description, 'items per page'
                  key :required, false
                  key :type, :integer
                end
                response 200 do
                  key :description, 'user list response'
                  schema do
                    key :type, :array
                    items do
                      key :'$ref', :UserResponses
                    end
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
              operation :post do
                key :description, 'Creates a new user. Duplicates are not allowed'
                key :operationId, 'postUser'
                key :tags, [
                  'users'
                ]
                # wazne przy postach
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, :payload
                  key :in, :body
                  key :description, <<-HEREDOC.gsub(/(?:\n\r?|\r\n?)/, '<br>').squish.html_safe
                                    JSON form data for User to add; pass [""] to clear arrays
                                    HEREDOC
                  key :required, true
                  schema do
                    key :'$ref', :UserRequest
                  end
                end
                response 200 do
                  key :description, 'user response'
                  schema do
                    key :'$ref', :UserResponse
                  end
                end
                response 201 do
                  key :description, 'Response with user'
                  schema do
                    key :"$ref", :UserResponseOnCreate
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response 422 do
                  key :description, 'Unprocessable entity'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
            swagger_path '/api/users/{id}' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              parameter do
                key :name, :id
                key :in, :path
                key :description, 'ID of user'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              operation :get do
                key :description, 'Returns a user based on a single ID, if the user does not have access to the pet'
                key :operationId, 'getUser'
                key :tags, [
                  'users'
                ]
                response 200 do
                  key :description, 'user response'
                  schema do
                    key :'$ref', :UserResponse
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
                # security api_key: []
                # security do
                #   key :petstore_auth, ['write:pets', 'read:pets']
                # end
              end
              operation :patch do
                key :description, 'Update user'
                key :operationId, 'patchUser'
                key :tags, [
                  'users'
                ]
                # wazne przy postach
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, :payload
                  key :in, :body
                  key :description, <<-HEREDOC.gsub(/(?:\n\r?|\r\n?)/, '<br>').squish.html_safe
                                    JSON form data for User to update; pass [""] as ids to delete associations.
                                    owncloud password can't be blank
                                    Legacy:
                                    image, redmine_id
                                    Editable by ALL:
                                    username, first_name,
                                    last_name, email,
                                    company_id, password,
                                    password_confirmation,
                                    owncloud_password
                                    Editable by ADMIN or user with activity 'groups:update':
                                    group_ids
                                    Editable only by ADMIN:
                                    state, global_role_ids

                                    HEREDOC
                  key :required, true
                  schema do
                    key :'$ref', :UserRequest
                  end
                end
                response 200 do
                  key :description, 'Response with user'
                  schema do
                    key :"$ref", :UserResponse
                  end
                end
                response 204 do
                  key :description, 'user updated'
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
              end
              operation :delete do
                key :description, 'deletes a single user based on the ID supplied'
                key :operationId, 'deleteUser'
                key :tags, [
                  'users'
                ]
                response 204 do
                  key :description, 'user deleted'
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
          end
        end
      end
    end
  end
end
