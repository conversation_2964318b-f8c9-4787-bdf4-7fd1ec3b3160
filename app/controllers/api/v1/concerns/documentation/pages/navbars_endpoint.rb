module Api
  module V1
    module Concerns
      module Documentation
        module Pages
          module NavbarsEndpoint
            include Swagger::Blocks
            extend ActiveSupport::Concern

            included do
              swagger_path '/api/pages/navbar/public_show' do
                parameter do
                  key :name, 'Authorization'
                  key :description, 'Your current Authorization Token (e.g. on staging server)'
                  key :in, :header
                  key :type, :string
                end
                parameter do
                  key :name, 'X-Swagger-Sign-In-As'
                  key :description, 'Sign in as user with ID (development env only)'
                  key :in, :header
                  key :required, true
                  key :type, :string
                end
                operation :get do
                  key :description, "Returns resources (available nabar tab symbols), \
                                     activities (permitted actions) for current user, \
                                     info on api revision (to sygnalize backend deployments), \
                                     and asks for immediate action to take by frontend app"
                  key :operationId, 'getPagesNavbar'
                  key :tags, [
                    'pages'
                  ]
                  response 200 do
                    key :description, 'pages_navbar response'
                    schema do
                      key :'$ref', :PagesNavbarResponse
                    end
                  end
                  response :default do
                    key :description, 'unexpected error'
                    schema do
                      key :'$ref', :ApplicationErrorModel
                    end
                  end
                  # security api_key: []
                  # security do
                  #   key :petstore_auth, ['write:pets', 'read:pets']
                  # end
                end
              end
              swagger_path '/api/pages/navbar' do
                parameter do
                  key :name, 'Authorization'
                  key :description, 'Your current Authorization Token (e.g. on staging server)'
                  key :in, :header
                  key :type, :string
                end
                parameter do
                  key :name, 'X-Swagger-Sign-In-As'
                  key :description, 'Sign in as user with ID (development env only)'
                  key :in, :header
                  key :required, true
                  key :type, :string
                end
                operation :get do
                  key :description, "Returns resources (available nabar tab symbols), \
                                     activities (permitted actions) for current user, \
                                     info on api revision (to sygnalize backend deployments), \
                                     and asks for immediate action to take by frontend app"
                  key :operationId, 'getPagesNavbar'
                  key :tags, [
                    'pages'
                  ]
                  response 200 do
                    key :description, 'pages_navbar response'
                    schema do
                      key :'$ref', :PagesNavbarResponse
                    end
                  end
                  response :default do
                    key :description, 'unexpected error'
                    schema do
                      key :'$ref', :ApplicationErrorModel
                    end
                  end
                  # security api_key: []
                  # security do
                  #   key :petstore_auth, ['write:pets', 'read:pets']
                  # end
                end
              end
            end
          end
        end
      end
    end
  end
end
