module Api
  module V1
    module Concerns
      module Documentation
        module ProjectsEndpoint
          include Swagger::Blocks
          extend ActiveSupport::Concern

          included do
            swagger_path '/api/projects' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              operation :get do
                key :description, 'Provides a list of projects based on the search criteria'
                key :operationId, 'getProjects'
                key :tags, [
                  'projects'
                ]
                parameter do
                  key :name, 'f[collection_for_select]'
                  key :in, :query
                  key :description, 'return only fields for select, defaults to `false`, specify `page` or `per_page` to get paginated results (TODO)'
                  key :required, false
                  key :type, :boolean
                end
                parameter do
                  key :name, 'f[memberships_member_id]'
                  key :in, :query
                  key :description, 'filter based on memberships by user or group id'
                  key :required, false
                  key :type, :int32
                end
                parameter do
                  key :name, 'f[memberships_member_type]'
                  key :in, :query
                  key :description, 'filter based on memberships by member_type'
                  key :required, false
                  key :type, :string
                  key :enum, ::Membership::MEMBER_TYPES
                end
                parameter do
                  key :name, 'f[not_self_or_descendants]'
                  key :in, :query
                  key :description, "return projects list excluding specified project (by ID or identifier)\
                                     and it's descendants or return empty array if specified project was not found"
                  key :required, false
                  key :type, :int32
                end
                parameter do
                  key :name, 'f[roots]'
                  key :in, :query
                  key :description, 'return only projects which have no parent if `true`'
                  key :required, false
                  key :type, :boolean
                end
                parameter do
                  key :name, 'f[immediate_children]'
                  key :in, :query
                  key :description, "return projects which are immediate children \
                                     of project with given id (confilicts with `roots` filter)"
                  key :required, false
                  key :type, :int32
                end
                parameter do
                  key :name, 'f[term]'
                  key :in, :query
                  key :description, 'filter by term'
                  key :required, false
                  key :type, :string
                end
                parameter do
                  key :name, 'f[status]'
                  key :in, :query
                  key :description, "filter by status index: 0 - active, 1 - closed, 2 - archived \
                                     (`archived` results returned only to 'Global Admin' user) \
                                     (defaults to `active` if not specified)"
                  key :required, false
                  key :type, :string
                  key :enum, [0, 1, 2]
                end
                parameter do
                  key :name, 'f[sort]'
                  key :in, :query
                  key :description, 'sort by field in direction'
                  key :required, false
                  key :type, :string
                  key :enum, ['id asc', 'id desc', 'name asc', 'name desc', 'created_at asc', 'created_at desc', 'updated_at asc', 'updated_at desc']
                end
                parameter do
                  key :name, 'page'
                  key :in, :query
                  key :description, 'page number'
                  key :required, false
                  key :type, :integer
                end
                parameter do
                  key :name, 'per_page'
                  key :in, :query
                  key :description, 'items per page'
                  key :required, false
                  key :type, :integer
                end
                response 200 do
                  key :description, 'project list response'
                  schema do
                    key :type, :array
                    items do
                      key :'$ref', :ProjectResponses
                    end
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
              operation :post do
                key :description, 'Creates a new project. Duplicates are not allowed'
                key :operationId, 'postProject'
                key :tags, [
                  'projects'
                ]
                # wazne przy postach
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, :payload
                  key :in, :body
                  key :description, <<-HEREDOC.gsub(/(?:\n\r?|\r\n?)/, '<br>').squish.html_safe
                                    JSON form data for Project to add
                                    Editable by ALL:
                                    name, description, identifier, accounting_number_id
                                    Editable only by ADMIN:
                                    parent_id, company_id,
                                    gid_number, homepage,
                                    public, inherit_members, owncloud, status,
                                    permissions_for_current_user

                                    HEREDOC
                  key :required, true
                  schema do
                    key :'$ref', :ProjectRequest
                  end
                end
                response 200 do
                  key :description, 'project response'
                  schema do
                    key :'$ref', :ProjectResponse
                  end
                end
                response 201 do
                  key :description, 'Response with project'
                  schema do
                    key :"$ref", :ProjectResponse
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response 422 do
                  key :description, 'Unprocessable entity'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
            swagger_path '/api/projects/{id}' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              parameter do
                key :name, :id
                key :in, :path
                key :description, 'ID or identifier of project'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              operation :get do
                key :description, 'Returns a project based on a single ID, if the project does not have access to the pet'
                key :operationId, 'getProject'
                key :tags, [
                  'projects'
                ]
                response 200 do
                  key :description, 'project response'
                  schema do
                    key :'$ref', :ProjectResponse
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
                # security api_key: []
                # security do
                #   key :petstore_auth, ['write:pets', 'read:pets']
                # end
              end
              operation :patch do
                key :description, 'Update project'
                key :operationId, 'patchProject'
                key :tags, [
                  'projects'
                ]
                # wazne przy postach
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, :payload
                  key :in, :body
                  key :description, <<-HEREDOC.gsub(/(?:\n\r?|\r\n?)/, '<br>').squish.html_safe
                                    JSON form data for Project to update; pass [""] as ids to delete associations
                                    Editable by ALL:
                                    name, description, identifier, accounting_number_id
                                    Editable only by ADMIN:
                                    parent_id, company_id,
                                    gid_number, homepage,
                                    public, inherit_members, owncloud, status,
                                    permissions_for_current_user

                                    HEREDOC
                  key :required, true
                  schema do
                    key :'$ref', :ProjectRequest
                  end
                end
                response 200 do
                  key :description, 'Response with project'
                  schema do
                    key :"$ref", :ProjectResponse
                  end
                end
                response 204 do
                  key :description, 'project updated'
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
              end
              operation :delete do
                key :description, 'deletes a single project based on the ID supplied'
                key :operationId, 'deleteProject'
                key :tags, [
                  'projects'
                ]
                response 204 do
                  key :description, 'project deleted'
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
            swagger_path '/api/projects/{id}/copy_source' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              parameter do
                key :name, :id
                key :in, :path
                key :description, 'ID of project'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              operation :get do
                key :description, 'copies source project.'
                key :operationId, 'copy_sourceProject'
                key :tags, [
                  'projects'
                ]
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                response 200 do
                  key :description, 'got copy_source'
                  # schema do
                  #   key :'$ref', :ProjectResponse
                  # end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
            swagger_path '/api/projects/{id}/copy' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              parameter do
                key :name, :id
                key :in, :path
                key :description, 'ID of project'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              operation :post do
                key :description, <<-HEREDOC.gsub(/(?:\n\r?|\r\n?)/, '<br>').squish.html_safe
                  Copies a project. Optionally pass as Array in params: <br>
                  only (filters which parts of source_project get copied. If not added - all parts get copied), <br>
                  enabled_modules (filters enabled_modules. If blank/not added - all are enabled. If empty ( [' '] ) - none are enabled. Other example: ['wiki', 'news']), <br>
                  trackers (filters trackers. If blank/not added - all are added. If empty ( [' '] ) - none are added. Other example: ['Bug']), <br>
                  issue_custom_fields (filters issue_custom_fields. If blank/not added - all are added. If empty ( [' '] ) - none are added. Other example: ['my_custom_field']), <br>
                  custom_values (filters custom_values). If blank/not added - all are added. If empty ( [' '] ) - none are added. Other example: ['Stable'])"
                  HEREDOC
                key :operationId, 'copyProject'
                key :tags, [
                  'projects'
                ]
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, :payload
                  key :in, :body
                  key :description, 'JSON form data for Project to be copied. Name, identifier and accounting_number_id must be present.'
                  key :required, true
                  schema do
                    key :'$ref', :ProjectCopy
                  end
                end
                response 201 do
                  key :description, 'copy created'
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
            swagger_path '/api/projects/{id}/archive' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              parameter do
                key :name, :id
                key :in, :path
                key :description, 'ID of project'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              operation :patch do
                key :description, 'archives a project.'
                key :operationId, 'archiveProject'
                key :tags, [
                  'projects'
                ]
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                response 204 do
                  key :description, 'project archived'
                  schema do
                    property :message do
                      key :type, :string
                    end
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
            swagger_path '/api/projects/{id}/unarchive' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              parameter do
                key :name, :id
                key :in, :path
                key :description, 'ID of project'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              operation :patch do
                key :description, 'unarchives a project.'
                key :operationId, 'unarchiveProject'
                key :tags, [
                  'projects'
                ]
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                response 204 do
                  key :description, 'project unarchived'
                  schema do
                    property :message do
                      key :type, :string
                    end
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
            swagger_path '/api/projects/{id}/close' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              parameter do
                key :name, :id
                key :in, :path
                key :description, 'ID of project'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              operation :patch do
                key :description, 'closes a project.'
                key :operationId, 'closesProject'
                key :tags, [
                  'projects'
                ]
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                response 204 do
                  key :description, 'project closed'
                  schema do
                    property :message do
                      key :type, :string
                    end
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
            swagger_path '/api/projects/{id}/reopen' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              parameter do
                key :name, :id
                key :in, :path
                key :description, 'ID of project'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              operation :patch do
                key :description, 'reopen a project.'
                key :operationId, 'reopenProject'
                key :tags, [
                  'projects'
                ]
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                response 204 do
                  key :description, 'project reopened'
                  schema do
                    property :message do
                      key :type, :string
                    end
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response :default do
                  key :description, 'unexpected error'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
          end
        end
      end
    end
  end
end
