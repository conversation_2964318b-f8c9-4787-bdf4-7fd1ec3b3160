module Api
  module V1
    module Concerns
      module Documentation
        module RandomEndpoint
          include Swagger::Blocks
          extend ActiveSupport::Concern

          included do
            swagger_path '/api/random/{length}' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, :length
                key :in, :path
                key :description, 'length (8-20), default is 8'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              operation :get do
                key :description, 'Returns a random friendly token e.g. password'
                key :operationId, 'getRandom'
                key :tags, [
                  'random'
                ]
                response 200 do
                  key :description, 'response'
                  schema do
                    property :random do
                      key :type, :string
                    end
                  end
                end
              end
            end
          end
        end
      end
    end
  end
end
