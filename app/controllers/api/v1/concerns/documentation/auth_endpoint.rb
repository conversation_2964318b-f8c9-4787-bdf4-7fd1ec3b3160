module Api
  module V1
    module Concerns
      module Documentation
        module AuthEndpoint
          include Swagger::Blocks
          extend ActiveSupport::Concern

          included do
            swagger_path '/api/auth/confirmation' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              operation :post do
                key :description, 'Send email confirmation instructions at email'
                key :operationId, 'postEmailConfirmationInstructions'
                key :tags, [
                  'auth'
                ]
                # wazne przy postach
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, 'user[email]'
                  key :in, :query
                  key :description, 'unconfirmed user email'
                  key :required, true
                  key :type, :string
                end
                response 200 do
                  key :description, 'response'
                  schema do
                    property :message do
                      key :type, :string
                    end
                  end
                end
                response 422 do
                  key :description, 'Unprocessable entity'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
              end
            end
            swagger_path '/api/auth/password_expired' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              operation :patch do
                key :description, 'Set new user password'
                key :operationId, 'patchUserPassword'
                key :tags, [
                  'auth'
                ]
                # wazne przy postach
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, :payload
                  key :in, :body
                  key :description, 'JSON form data for UserPassword to update'
                  key :required, true
                  schema do
                    property :user do
                      property :current_password do
                        key :name, :current_password
                        key :type, :string
                        key :format, :password
                        key :in, :formData
                      end
                      property :password do
                        key :name, :password
                        key :type, :string
                        key :format, :password
                        key :in, :formData
                      end
                      property :password_confirmation do
                        key :name, :password_confirmation
                        key :type, :string
                        key :format, :password
                        key :in, :formData
                      end
                    end
                  end
                end
                response 204 do
                  key :description, 'user password updated'
                  schema do
                    property :message do
                      key :type, :string
                    end
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
              end
            end
            swagger_path '/api/auth/password/edit' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              operation :get do
                key :description, 'First use POST "/api/auth/password" to generate reset_password_token and redirect_url. This route is the destination URL for password reset confirmation. This route must contain reset_password_token and redirect_url params.'
                key :operationId, 'getPasswordReset'
                key :tags, [
                  'auth'
                ]
                # wazne przy postach
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, 'reset_password_token'
                  key :in, :query
                  key :description, 'Use reset_password_token generated by previously used POST method.'
                  key :required, true
                  key :type, :string
                end
                parameter do
                  key :name, 'redirect_url'
                  key :in, :query
                  key :description, 'Use redirect_url generated by previously used POST method.'
                  key :required, true
                  key :type, :string
                end
                response 0 do
                  key :description, 'Redirect in the edit method was properly executed.'
                  schema do
                    property :message do
                      key :type, :string
                    end
                  end
                end
                response 200 do
                  key :description, 'response'
                  schema do
                    property :message do
                      key :type, :string
                    end
                  end
                end
                response 422 do
                  key :description, 'Unprocessable entity'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
              end
            end
            swagger_path '/api/auth/password' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              operation :post do
                key :description, 'Use this route to send a password reset confirmation email to users that registered by email. Accepts email and redirect_url as params.'
                key :operationId, 'postPasswordResetConfirmation'
                key :tags, [
                  'auth'
                ]
                # wazne przy postach
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, 'email'
                  key :in, :query
                  key :description, 'email of the user to whom the reset instructions will be sent'
                  key :required, true
                  key :type, :string
                end
                response 200 do
                  key :description, 'response'
                  schema do
                    property :message do
                      key :type, :string
                    end
                  end
                end
                response 422 do
                  key :description, 'Unprocessable entity'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
              end
              operation :patch do
                key :description, 'Use this route to change user passwords. Requires password and password_confirmation as params. This route is only valid for users that registered by email.'
                key :operationId, 'putPasswordChangeAfterReset'
                key :tags, [
                  'auth'
                ]
                # wazne przy postach
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, 'X-Swagger-Sign-In-As'
                  key :description, 'Sign in as user with ID (development env only)'
                  key :in, :header
                  key :required, true
                  key :type, :string
                end
                parameter do
                  key :name, 'password'
                  key :in, :formData
                  key :description, 'Password'
                  key :required, true
                  key :type, :string
                end
                parameter do
                  key :name, 'password_confirmation'
                  key :in, :formData
                  key :description, 'Password confirmation'
                  key :required, true
                  key :type, :string
                end
                response 200 do
                  key :description, 'response'
                  schema do
                    property :message do
                      key :type, :string
                    end
                  end
                end
                response 401 do
                  key :description, 'Unauthorized'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response 403 do
                  key :description, 'Not allowed redirect_url'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
                response 422 do
                  key :description, 'Update error'
                  schema do
                    key :"$ref", :ApplicationErrorModel
                  end
                end
              end
            end
          end
        end
      end
    end
  end
end
