module Api
  module V1
    module Concerns
      module WorkersReportable
        extend ActiveSupport::Concern

        included do
          before_action :authorize
        end

        def index
          @workers_reports = paginate(scope.includes(:user, :company), search_options(params))
        end

        def create
          Company.native.each do |company|
            workers_report = model_of_concern.new(
              workers_report_params.merge(user: current_user, company: company)
            )

            return respond_with(workers_report) unless workers_report.save
          end

          head :created
        end

        def file
          workers_report = scope.where.not(file_data: nil).find(params[:id])
          file = workers_report.file

          send_file(Rails.root.join(file.storage.directory + file.id),
                    filename: workers_report.file_name,
                    type: 'application/xls',
                    disposition: 'inline')
        end

        private

        def workers_report_params
          params.require(model_of_concern.name.underscore.to_sym).permit(:month, :year)
        end

        def authorize
          super(WorkersReport)
        end

        def render_error
          render status: :bad_request, json: { error: 'invalid params' }
        end

        def scope
          policy_scope(model_of_concern, policy_scope_class: WorkersReportPolicy::<PERSON><PERSON>)
        end
      end
    end
  end
end
