require 'date_utils'

module Api
  module V1
    class DaysOffController < ApiController
      skip_before_action :authenticate_user!
      before_action :parse_dates
      before_action :skip_policy_scope
      before_action :skip_authorization

      def index
        @days_off = (@date_from..@date_to).select do |date|
          DateUtils.holiday?(date)
        end
      end

      private

      def parse_dates
        return head :bad_request unless params[:date_from] && params[:date_to]

        @date_from = Date.parse(params[:date_from])
        @date_to = Date.parse(params[:date_to])
      rescue ArgumentError
        head :bad_request
      end
    end
  end
end
