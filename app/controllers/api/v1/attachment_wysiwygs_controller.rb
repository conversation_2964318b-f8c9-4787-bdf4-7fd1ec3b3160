module Api
  module V1
    class AttachmentWysiwygsController < ApiController
      respond_to :json, :multipart_form

      def create
        authorize!(AttachmentWysiwyg)
        attachment = AttachmentWysiwyg.new(attachment_params)
        if attachment.save
          render json: {
            id: attachment.id,
            location: attachment.file_url
          }, status: :created
        else
          render json: { errors: attachment.errors.messages }, status: :unprocessable_entity
        end
      end

      private

      def attachment_params
        params.permit(:file)
      end
    end
  end
end
