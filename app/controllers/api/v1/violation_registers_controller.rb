module Api
  module V1
    class ViolationRegistersController < ApiController
      before_action :set_violation_register, except: %i[index create]
      before_action :authorize_violation_register
      before_action :skip_policy_scope

      def index
        results = search(scope: ViolationRegister.includes(:company).order(state: :asc), filters: params[:f]).results
        @violation_registers = paginate(authorize(results))
      end

      def show; end

      def create
        @violation_register = ViolationRegister.new(violation_register_params)
        @violation_register.created_by = current_user
        if @violation_register.save
          respond_with @violation_register
        else
          render json: { errors: @violation_register.errors.messages }, status: :unprocessable_entity
        end
      end

      def update
        if @violation_register.update(violation_register_params)
          respond_with @violation_register
        else
          render json: { errors: @violation_register.errors.messages }, status: :unprocessable_entity
        end
      end

      def destroy
        @violation_register.destroy
        head :no_content
      end

      def processing
        @violation_register.processing!
        head :no_content
      rescue AASM::InvalidTransition => e
        render json: { errors: ["State transition error: #{e.message}"] }, status: :unprocessable_entity
      end

      def reported
        @violation_register.reported!
        head :no_content
      rescue AASM::InvalidTransition => e
        render json: { errors: ["State transition error: #{e.message}"] }, status: :unprocessable_entity
      end

      def complete
        @violation_register.completed!
        head :no_content
      rescue AASM::InvalidTransition => e
        render json: { errors: ["State transition error: #{e.message}"] }, status: :unprocessable_entity
      end

      def attachment
        attachment = @violation_register.attachment

        return head(:not_found) unless attachment

        file_path = Rails.root.join(attachment.storage.directory + attachment.id)
        send_file file_path, filename: attachment.original_filename,
                             type: attachment.mime_type,
                             disposition: 'inline'
      end

      private

      def set_violation_register
        @violation_register = ViolationRegister.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: { errors: ['Violation register not found'] }, status: :not_found
      end

      def violation_register_params
        params.require(:violation_register).permit(:description, :violation_type, :violation_area, :taken_actions,
                                                   :violation_level, :attachment, :signature, :company_id,
                                                   :violation_occurrence_date, :violation_entry_date, :mandatory_report,
                                                   :report_description, :state)
      end

      def authorize_violation_register
        authorize @violation_register || ViolationRegister
      end

      def search(options = {})
        ::Searches::ViolationRegisterSearch.new(search_options(options))
      end
    end
  end
end
