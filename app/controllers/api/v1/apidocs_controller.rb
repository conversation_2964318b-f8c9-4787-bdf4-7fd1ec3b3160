module Api
  module V1
    class ApidocsController < ActionController::Base
      include Swagger::Blocks

      # https://github.com/fotinakis/swagger-blocks/blob/master/spec/lib/swagger_v2_blocks_spec.rb
      swagger_root do
        key :swagger, '2.0'
        info do
          key :version, '1'
          key :title, 'API V1'
          key :description, 'A sample API that exposes data to public '
          key :termsOfService, 'http://xyz.com/terms/'
        end
        tag do
          key :name, 'Users'
          key :description, 'List users'
          externalDocs do
            key :description, 'Find more info here'
            key :url, 'https://swagger.io'
          end
        end
        key :host, Settings.swagger_host
        key :basePath, '/'
        key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
        key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
        # security_definition :Authorization, type: :api<PERSON>ey do
        #   key :name, :Authorization
        #   key :in, :header
        # end
        security_definition :api_key, type: :api<PERSON>ey do
          key :name, :api_key
          key :in, :header
        end
        security_definition :auth do
          key :type, :oauth2
          key :authorizationUrl, 'http://swagger.io/api/oauth/dialog'
          key :flow, :implicit
          scopes 'write:pets' => 'modify pets in your account' do
            key 'read:pets', 'read your pets'
          end
        end
        tag name: 'pet' do
          key :description, 'Pets operations'
          externalDocs description: 'Find more info here' do
            key :url, 'https://swagger.io'
          end
        end
      end

      # A list of all classes that have swagger_* declarations.
      SWAGGERED_CLASSES = [
        ActivitiesController,
        Activity,
        ::Api::V1::ApiController,
        # AbsencesController, # @deprecated access denied
        Absence,
        ApplicationError,
        ::Devise::PasswordExpiredController,
        BookingResourcesController,
        BookingResource,
        CompaniesController,
        Company,
        ::Overrides::ConfirmationsController,
        DepartmentsController,
        Department,
        GlobalRolesController,
        GlobalRole,
        GroupsController,
        Group,
        HolidayRequestsController,
        HolidayRequest,
        InvoicesController,
        Invoice,
        MpkNumber,
        RevenueAccount,
        MembershipsController,
        Membership,
        NotificationsController,
        Notification,
        ::Api::V1::Pages::NavbarsController,
        ::Pages::Navbar,
        PaymentSchedulesController,
        PaymentSchedule,
        ::Api::V1::Pages::ProfilesController,
        ::Pages::Profile,
        PositionsController,
        Position,
        ProjectsController,
        Project,
        PublicKeysController,
        PublicKey,
        WifiTokensController,
        WifiToken,
        RolesController,
        Role,
        UsersController,
        User,
        self
      ].freeze

      def index
        render json: Swagger::Blocks.build_root_json(SWAGGERED_CLASSES)
      end
    end
  end
end
