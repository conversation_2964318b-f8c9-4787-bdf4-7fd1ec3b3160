module Api
  module V1
    class ApprovalsController < ApiController
      respond_to :json

      def index
        authorize(Approval)
        approvals = fetch_all_approvals
        filtered_approvals = if params[:f][:query].present?
                               ::Searches::ApprovalSearch.filter_approvals(approvals, params)
                             else
                               approvals
                             end
        filtered_approvals = filtered_approvals.decorate
        @approvals = paginate(filtered_approvals)
      end

      def accept
        authorize(Approval)
        approval = Approval.find(params[:id]).decorate
        approval.accept
        head :no_content
      end

      def not_accepted
        authorize(Approval)
        @approvals = Approval.where(user_id: current_user, accepted: false)
                             .includes(approvable: :attachments, project_agreement: :project)
        current_user.update_approvals_cache if @approvals.empty?
      end

      private

      def fetch_all_approvals
        project = Project.find(params[:project_id]) if params[:project_id]
        agreement = Agreement.find(params[:agreement_id]) if params[:agreement_id]
        return fetch_approvals(project) if project
        fetch_approvals(agreement) if agreement
      end

      def fetch_approvals(model)
        policy_scope(model.approvals.includes(:user).preload(:approvable, :project_agreement).where(users: { state: 'active' },
                                                                                                    accepted:
                                                                                                      params[:accepted] == '1'))
      end
    end
  end
end
