module Api
  module V1
    class RegistryCategoriesController < ApiController
      before_action :set_registry_category, except: %i[index create]
      before_action :authorize_registry_category
      before_action :skip_policy_scope

      def index
        results = search(scope: RegistryCategory.includes(project: :company), filters: params[:f]).results
        @registry_categories = paginate(authorize(results))
      end

      def show; end

      def create
        @registry_category = RegistryCategory.new(registry_category_params)
        @registry_category.created_by = current_user
        if @registry_category.save
          respond_with @registry_category
        else
          render json: { errors: @registry_category.errors.messages }, status: :unprocessable_entity
        end
      end

      def update
        if @registry_category.update(registry_category_params)
          respond_with @registry_category
        else
          render json: { errors: @registry_category.errors.messages }, status: :unprocessable_entity
        end
      end

      def destroy
        @registry_category.destroy
        head :no_content
      end

      def activate
        @registry_category.activate!
        head :no_content
      rescue AASM::InvalidTransition => e
        render json: { errors: ["State transition error: #{e.message}"] }, status: :unprocessable_entity
      end

      def close
        @registry_category.close!
        head :no_content
      rescue AASM::InvalidTransition => e
        render json: { errors: ["State transition error: #{e.message}"] }, status: :unprocessable_entity
      end

      private

      def set_registry_category
        @registry_category = RegistryCategory.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: { errors: ['Registry category not found'] }, status: :not_found
      end

      def registry_category_params
        params.require(:registry_category).permit(
          :project_id, :entrustment_agreement, :processing_categories, :security_measures_description, :admin_name,
          :admin_contact_details, :co_admin_details, :representative_admin_details, :admin_data_protection_officer,
          :processing_time, :third_country_or_intl_org_recipients, :security_documentation, :subcontractor_details,
          :sub_processing_categories, :creation_date, :expiration_date
        )
      end

      def authorize_registry_category
        authorize @registry_category || RegistryCategory
      end

      def search(options = {})
        ::Searches::RegistryCategorySearch.new(search_options(options))
      end
    end
  end
end
