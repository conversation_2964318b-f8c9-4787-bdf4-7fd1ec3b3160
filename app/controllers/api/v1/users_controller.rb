module Api
  module V1
    class UsersController < ApiController
      include ::Api::V1::Concerns::Documentation::UsersEndpoint
      before_action :auth_with_http_basic, only: [:impersonate]

      def index # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
        authorize(User, :index?)
        request.variant = :collection_for_select if params[:f].try(:[], :collection_for_select).to_s == 'true'
        respond_to do |format|
          format.json do |variant|
            variant.collection_for_select do
              results = search(scope: users_scope, filters: params[:f]).results
              if params[:page].present? || params[:per_page].present?
                paginate_opts = params.dup
                paginate_opts[:filters] ||= {}
                paginate_opts[:filters][:paginated_collection_for_select] = true
                @users = paginate(authorize!(results), search_options(paginate_opts))
              else
                @users = authorize!(results)
              end
              render template: '/api/v1/users/index', collection: @users
            end
            variant.none do
              results = search(scope: users_scope.includes(:global_roles, :groups), filters: params[:f]).results
              @users = paginate(authorize!(results), search_options(params))
              raise ActiveRecord::RecordNotFound, 'Page not found' if params[:page].to_i > 1 && @users.empty?
            end
          end
          format.xlsx do
            results = search(scope: users_scope, filters: params[:f]).results
            send_data ::UsersXlsxGenerator.new(authorize!(results)).generate.read
          end
        end
      end

      def history
        user = authorize!(find_user)
        @versions = user.versions
        @authors = User.where(id: @versions.map(&:whodunnit))
        render template: 'api/v1/shared/history'
      end

      def show
        @user = authorize!(find_user)
        @projects = projects.includes(:accounting_number)
        @approvals = @user.approvals.includes(:approvable).decorate
        @assets = Asset.where(user_id: find_user.id)
        respond_with(@user.decorate)
      end

      # NOTE: to co zakomentowane, spowodowaloby sytuacje, ze zawsze wybieraja `:active`
      # i nikt nie potwierdza email, a to dopuszczalne tylko przy imporcie z Redmine
      def create
        authorize!(User)
        respond_with(CreateUser.new.call(permitted_attributes(User)).decorate)
      end

      def update
        authorize!(find_user)
        # email_chanded? shows false (devise token auth hardcodes it to false!)
        # @user.postpone_email_change_until_confirmation_and_regenerate_confirmation_token if @user.email != @user.email_was
        respond_with(UpdateUser.new.call(params[:id], permitted_attributes(User)).decorate)
      end

      def destroy
        @user = authorize!(find_user)
        @user.destroy!
        respond_with(@user.decorate)
      end

      def impersonate
        @user = authorize!(find_user)

        return render_api_error(message: 'Impersonation disabled!', status: 422) if Settings.impersonation_enabled.to_s != 'true'

        client_id = SecureRandom.urlsafe_base64(nil, false)
        auth_headers = @user.create_new_auth_token(client_id, current_user)

        sign_in(:user, @user, store: false, bypass: false)

        yield if block_given?

        flash.now[:notice] = "Ctrl (&#x2318;) + r, you are now impersonating #{@user.username}!".html_safe

        response.headers.merge!(auth_headers)

        render json: {
          data: auth_headers
        }
      end

      def resend_confirmation_instructions
        user = users_scope.where(confirmed_at: nil).find(params[:id])
        authorize(user)
        user.send_confirmation_instructions
        head :no_content
      end

      private

      # A workaround, if appropriate, is to tell your web server to return something other than 401 on an authentication failure,
      # and go from there. In AngularJS, a 500 (for example) will cause the $http promise to be rejected and you can handle it,
      # however you'd like. This is not recommended if you actually ever need the login prompt to occur!
      def auth_with_http_basic
        authenticate_or_request_with_http_basic('Impersonation login') do |username, password|
          ActiveSupport::SecurityUtils.secure_compare(username, Settings.impersonation_login) &&
            ActiveSupport::SecurityUtils.secure_compare(password, Settings.impersonation_password)
        end
      end

      def projects
        opts = { scope: policy_scope(Project).of_user(@user),
                 filters: params[:f] }
        Searches::ProjectSearch.new(search_options(opts), current_user).results
      end

      def search(options = {})
        @search = ::Searches::UserSearch.new(search_options(options))
      end

      def users_scope
        User.all
      end

      def find_user
        users_scope.includes(:global_roles, :company).find(params[:id])
      end

      def find_users
        users_scope.find(params[:ids])
      end
    end
  end
end
