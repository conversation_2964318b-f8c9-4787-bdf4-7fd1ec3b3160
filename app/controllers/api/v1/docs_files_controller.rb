class Api::V1::DocsFilesController < Api::V1::ApiController
  before_action :find_project, except: %i[index new]
  before_action :find_optional_project, only: %i[index new]
  before_action :authorize

  def new
    @categories = DocsFile.categories.keys
  end

  def index
    results = search(scope: scope, filters: params[:f]).results
    @docs_files = paginate(results, search_options(params))
    @docs_files = @docs_files.includes(:project, :created_by)
  end

  def create
    docs_file = @project.docs_files.create(docs_file_params.merge(created_by: current_user))

    respond_with docs_file, location: [@project, :docs_files]
  end

  private

  def docs_file_params
    params.require(:docs_file).permit(:file, :category)
  end

  def authorize
    if @project
      super(@project, :docs_files_access?)
    else
      super(DocsFile)
    end
  end

  def find_project
    @project = policy_scope(Project).find(params[:project_id])
  end

  def find_optional_project
    find_project if params[:project_id].present?
  end

  def search(options = {})
    ::Searches::DocsFileSearch.new(search_options(options))
  end

  def scope
    policy_scope(@project&.docs_files || DocsFile)
  end
end
