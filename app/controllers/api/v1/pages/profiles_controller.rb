module Api
  module V1
    module Pages
      class ProfilesController < ApiController
        include ::Api::V1::Concerns::Documentation::Pages::ProfilesEndpoint

        def show
          authorize!(find_profile)
          @approvals = current_user.approvals.includes(:approvable).decorate
          @assets = Asset.where(user_id: current_user.id).includes(:project, :user, :activated_by)
          respond_with(@profile.decorate) # if Rails.env.production? && stale?(@profile)
        end

        def update
          authorize!(find_profile)
          current_password = params[:profile][:current_password]
          if @profile.user.valid_password?(current_password)
            UpdateUser.new.call(@profile.user, profile_params)
          else
            @profile.user.assign_attributes(profile_params)
            @profile.user.valid?
            @profile.user.errors.add(:current_password, current_password.blank? ? :blank : :invalid)
          end
          respond_with(@profile.user.decorate)
        end

        def dismiss_onboarding
          authorize!(find_profile)
          user = @profile.user
          user.update(dismiss_onboarding: true)
          respond_with(user.decorate)
        end

        def save_preferences
          authorize!(find_profile)
          unless params[:preferences].is_a?(ActionController::Parameters)
            render json: { error: 'You should provide preferences hash' },
                   status: 422
            return
          end
          @profile.user.preferences.merge!(params[:preferences].permit!)
          @profile.user.save(validate: false)
          respond_with(@profile.user.preferences.as_json)
        end

        private

        def profile_params
          # params.require(:profile).permit(policy(::Pages::Profile).permitted_attributes)
          if @profile
            if params[:profile] && params[:profile][:company_id]
              company = Company.find(params[:profile][:company_id])
            else
              company = @profile.user && @profile.user.company
            end
            if params[:profile][:company_id].to_i != company.try(:id).to_i
              params.require(:profile).permit(policy(::Pages::Profile).permitted_attributes(company))
            else
              params.require(:profile).permit(policy(::Pages::Profile).permitted_attributes)
            end
          else
            if params[:profile] && params[:profile][:company_id]
              company = Company.find(params[:profile][:company_id])
              params.require(:profile).permit(policy(::Pages::Profile).permitted_attributes(company))
            else
              params.require(:profile).permit(policy(::Pages::Profile).permitted_attributes)
            end
          end
        end

        def find_profile
          @profile ||= ::Pages::Profile.new(current_user)
        end
      end
    end
  end
end
