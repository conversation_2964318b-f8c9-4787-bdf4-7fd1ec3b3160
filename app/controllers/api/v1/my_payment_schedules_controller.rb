module Api
  module V1
    class MyPaymentSchedulesController < ApiController
      def index
        authorize(PaymentSchedule, :index?, policy_class: MyPaymentSchedulePolicy)
        results = search(scope: scoped_results, filters: params[:f]).results
        @payment_schedules = paginate(results)
      end

      private

      def search(options = {})
        @search = ::Searches::PaymentScheduleSearch.new(search_options(options))
      end

      def scoped_results
        policy_scope(PaymentSchedule, policy_scope_class: MyPaymentSchedulePolicy::Scope)
          .includes(project: :company, payments: :invoices)
          .where(projects: { status: :active })
          .where.not(payments: { id: nil })
          .where(invoices: { id: nil })
          .where('payments.issued_on <= ?', Time.current + 1.month)
          .order('payments.issued_on ASC')
      end
    end
  end
end
