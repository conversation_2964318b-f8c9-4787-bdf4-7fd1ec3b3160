class Api::V1::TrainingBudgetsController < Api::V1::ApiController
  before_action :authorize
  before_action :skip_policy_scope
  before_action :find_training_budget, only: %i[show update destroy]

  def index
    @training_budgets = TrainingBudget.includes(:department)
  end

  def show; end

  def create
    respond_with TrainingBudget.create(training_budget_params)
  end

  def update
    @training_budget.update(training_budget_params)

    respond_with @training_budget
  end

  def destroy
    @training_budget.destroy

    head :no_content
  end

  private

  def training_budget_params
    params.require(:training_budget).permit(:year, :amount, :department_id)
  end

  def find_training_budget
    @training_budget = TrainingBudget.find(params[:id])
  end

  def authorize
    super(TrainingBudget)
  end
end
