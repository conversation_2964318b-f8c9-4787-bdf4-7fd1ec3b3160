module Api
  module V1
    class AccountingNumbersController < ApiController
      before_action :skip_policy_scope
      before_action :authorize
      before_action :find_accounting_number, only: %i[show update destroy]

      def index
        @accounting_numbers = search(scope: AccountingNumber.with_active_projects_count, filters: params[:f]).results
        @accounting_numbers = @accounting_numbers.includes(:user, :company).order(created_at: :desc)
      end

      def show; end

      def create
        respond_with AccountingNumber.create(accounting_number_params)
      end

      def update
        @accounting_number.update(accounting_number_params)

        respond_with @accounting_number
      end

      def destroy
        @accounting_number.destroy

        respond_with @accounting_number
      end

      private

      def find_accounting_number
        @accounting_number = AccountingNumber.find(params[:id])
      end

      def accounting_number_params
        params.require(:accounting_number).permit(:description, :company_id, :user_id, :overhead, :locked, :bu)
      end

      def authorize
        super(AccountingNumber)
      end

      def search(options = {})
        ::Searches::AccountingNumberSearch.new(search_options(options))
      end
    end
  end
end
