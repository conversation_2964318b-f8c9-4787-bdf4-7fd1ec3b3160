module Api
  module V1
    class MpkNumbersController < ApiController
      before_action :skip_policy_scope

      def index
        authorize(MpkNumber, :index?)
        respond_to do |format|
          format.json do
            @mpk_numbers = MpkNumber.active.order(:key).decorate

            render template: '/api/v1/mpk_numbers/index', collection: @mpk_numbers
          end
        end
      end
    end
  end
end
