module Api
  module V1
    class ClientsController < ApiController
      before_action :authorize_manage, except: %i[create index]
      before_action :find_client, only: %i[destroy show update activate]

      def create
        client = Client.new(client_params)
        authorize client
        client.activate if policy(client).activate?
        client.save!
        respond_with(client)
      end

      def update
        @client.update(client_params)
        respond_with(@client)
      end

      def show
        respond_with(@client)
      end

      def destroy
        @client.destroy
        respond_with(@client)
      end

      def index
        @clients = search(
          scope: policy_scope(Client).order(name: :asc), filters: params[:f]
        ).results
        authorize @clients
      end

      def activate
        @client.activate!

        respond_with @client
      end

      private

      def search(options = {})
        ::Searches::ClientSearch.new(search_options(options))
      end

      def authorize_manage
        authorize Client, :manage?
      end

      def find_client
        @client = Client.find(params[:id])
      end

      def client_params
        params.require(:client)
              .permit(:name, :street, :street_number, :additional_address, :apartment, :city, :download_from_gus,
                      :postcode, :post, :voivodeship, :district, :community, :country, :vat_number,
                      :invoice_sending_method, :invoice_sending_email, :invoice_sending_email_receiver)
      end
    end
  end
end
