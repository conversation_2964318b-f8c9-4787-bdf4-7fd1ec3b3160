module Api
  module V1
    class HolidayRequestsController < ApiController
      include ::Api::V1::Concerns::Documentation::HolidayRequestsEndpoint

      def users
        authorize(HolidayRequest)
        context = ::UsersHolidayRequestsOrganizer.call(
          current_user: current_user,
          params: params,
          policy: HolidayRequestPolicy.new(current_user, HolidayRequest)
        )
        raise ActionController::BadRequest.new, context.message unless context.success?

        @holiday_requests = context.grouped_holiday_requests
        @users = paginate_list(context.users, params.dup)
        meta_headers(context.response_headers_hash)
      end

      # https://mkdev.me/en/posts/a-couple-of-words-about-interactors-in-rails
      def index
        authorize(HolidayRequest)
        context = ::IndexHolidayRequestsOrganizer.call(
          current_user: current_user,
          params: params,
          policy: HolidayRequestPolicy.new(current_user, HolidayRequest)
        )
        raise ActionController::BadRequest.new, context.message unless context.success?

        results = policy_scope(context.applicant_holiday_requests)
        request.variant = context.response_variant
        meta = context.response_headers_hash
        params = context.params

        fetch_ids_of_users_who_overuse_holidays

        respond_to do |format|
          format.json do |variant|
            variant.collection_for_select do
              meta_headers(meta)
              serialize_index(results, params, meta, :collection_for_select)
            end
            variant.none do
              results = paginate_list(results, params)
              meta_headers(meta)
              serialize_index(results, params, meta, :none)
            end
          end
        end
      end

      def show
        holiday_request = find_holiday_request
        # po authorize, tu, w jbuilderze (czy dekoratorze), uzywac dalej tylko zmemoizowanej polityki: policy(@holiday_request)
        @holiday_request = authorize(holiday_request) && holiday_request
        respond_with(@holiday_request.decorate)
      end

      # /uploads/cache/holiday_request/254/file/bd88392e87723e85a9e9decdc5c72637.png
      # /uploads/store/holiday_request/254/file/bd88392e87723e85a9e9decdc5c72637-original.png
      def file # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
        matches = params[:filename].to_s.match(/-(?<style>\w+)/)
        style = matches ? matches[:style].to_sym : nil
        begin
          holiday_request = find_holiday_request
        rescue ActiveRecord::RecordNotFound
          logger.error "Couldn't find record"
          return render_file_not_found
        end
        holiday_request = authorize(holiday_request) && holiday_request
        file = holiday_request.file
        if file && file.exists?
          path_to_file = Rails.root.join(file.storage.directory + file.id).to_s.gsub(%r{/releases/\d+/}, '/shared/')
          metadata = file.metadata
          logger.debug "Sending file #{path_to_file}"
          send_file(path_to_file,
                    filename: holiday_request.file_name,
                    type: metadata['mime_type'],
                    disposition: 'attachment')
        else
          logger.error "Couldn't find file"
          render_file_not_found
        end
      end

      def create
        holiday_request_mock = HolidayRequest.new(params[:holiday_request].reject { |key, _val| key == 'file' }.permit!).freeze
        user = find_holiday_request_applicant(holiday_request_mock)

        adjusted_holiday_request_params = ::HolidayRequests::AdjustHolidayRequestParamsInteractor.call(
          params: holiday_request_params(holiday_request_mock),
          applicant: user,
          actor: current_user,
          action: __method__
        ).params

        holiday_request = authorize!(HolidayRequest.new(adjusted_holiday_request_params))

        persist_holiday_request_opts = {
          actor: current_user,
          object: holiday_request,
          params: adjusted_holiday_request_params,
          action: __method__,
          policy: policy(holiday_request)
        }
        form = PersistHolidayRequest.call(**persist_holiday_request_opts)
        respond_with(form.object.decorate)
      end

      def update
        holiday_request = authorize!(find_holiday_request)
        user = find_holiday_request_applicant(holiday_request)

        interaction = ::HolidayRequests::AdjustHolidayRequestParamsInteractor.call(
          params: holiday_request_params(holiday_request),
          applicant: user,
          actor: current_user,
          action: __method__,
          holiday_request: holiday_request
        )
        adjusted_holiday_request_params = interaction.params
        new_file_params = interaction.new_file_params

        update_holiday_request(holiday_request, adjusted_holiday_request_params, new_file_params)

        respond_with(holiday_request.decorate)
      end

      def history
        holiday_request = authorize!(find_holiday_request)
        @versions = holiday_request.versions
        @authors = User.where(id: @versions.map(&:whodunnit))
        render template: 'api/v1/shared/history'
      end

      def destroy
        holiday_request = authorize!(find_holiday_request)
        destroy_holiday_request_opts = {
          actor: current_user,
          object: holiday_request,
          action: __method__,
          policy: policy(holiday_request)
        }
        form = DestroyHolidayRequest.call(**destroy_holiday_request_opts)
        respond_with(form.object.decorate)
      end

      def category_options
        authorize!(HolidayRequest)
        holiday_request_policy = HolidayRequestPolicy.new(current_user, HolidayRequest)
        respond_with(holiday_request_policy.categories)
      end

      def convert_to_options
        holiday_request = authorize!(find_holiday_request)
        bulk_permission_check = ::HolidayRequestPolicy.new(current_user, holiday_request).public_send(:bulk_permission_check,
                                                                                                      %w[convert_to])
        holiday_request_can_be_converted = bulk_permission_check[0] || false
        categories = if holiday_request_can_be_converted
                       # current category must be selectable
                       (HolidayRequest::CONFIRMABLE_CATEGORIES + [holiday_request.category]).uniq
                     else
                       [holiday_request.category]
                     end
        respond_with(categories)
      end

      def category_options_for_search
        authorize!(HolidayRequest)
        categories = HolidayRequest::CATEGORIES
        respond_with(categories)
      end

      def department_options
        authorize!(HolidayRequest)
        scoped = Department.not_locked.includes(:company)
        request.variant = :collection_for_select
        respond_to do |format|
          format.json do |variant|
            variant.collection_for_select do
              @departments = HolidayRequestPolicy::Scope.new(current_user, scoped).department_options_resolve
              render template: '/api/v1/departments/index', collection: @departments
            end
          end
        end
      end

      def project_options
        authorize!(HolidayRequest)
        scoped = Project.where(status: Project.statuses[:active])
        request.variant = :collection_for_select
        respond_to do |format|
          format.json do |variant|
            variant.collection_for_select do
              @projects = HolidayRequestPolicy::Scope.new(current_user, scoped).project_options_resolve
              render template: '/api/v1/projects/index', collection: @projects
            end
          end
        end
      end

      def user_options
        user_options_commons(__method__)
      end

      private

      # bierze pod uwage wiele dni wew. jednego urlopu NZ;
      # nie musi obliczac biznes days, bo rekordy absences wypadajace w dni wolne nie sa tworzone;
      # absences tworzone sa asynchronicznie, wiec moze byc konieczne poczekanie chwile az sie pojawia,
      # ze wzgledow wydajnosciowych jest to chyba lepsze rozwiazanie niz jeszcze jeden join (eager) lub N+1,
      # rowniez latwiejsze, czas pokaze czy sie sprawdzi
      # rubocop:disable Layout/LineLength, Metrics/AbcSize
      def fetch_ids_of_users_who_overuse_holidays
        ids_of_users_who_overuse_holidays_nz = User.unscoped.select('absences.date, users.id, COUNT(absences.id) AS category_absences_count').joins('RIGHT JOIN absences AS absences ON absences.user_id = users.id').where(absences: { category: HolidayRequest.categories['Niedostępność/Ż'] }).where('YEAR( absences.date ) = :year', year: Time.current.year).where(absences: { visible: true }).having('category_absences_count > 4').group('users.id, absences.date').to_a.map(&:id)
        ids_of_users_with_negative_absence_balance = User.unscoped.where('users.absence_balance < 0').pluck(:id)
        @ids_of_users_who_overuse_holidays = (ids_of_users_with_negative_absence_balance + ids_of_users_who_overuse_holidays_nz).uniq
      end
      # rubocop:enable Layout/LineLength, Metrics/AbcSize

      def user_options_commons(source_action) # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
        authorize!(HolidayRequest)
        users_scope = User.native.where.not(company_id: nil)
        users_scope = users_scope.where(state: 'active') if source_action == :user_options
        users_scope = users_scope.includes(:department).references(:department)
        context = ParseHolidayRequestsParamsDepartmentIdsMappedInteractor.new(current_user: current_user, params: params).call
        request.variant = :collection_for_select
        respond_to do |format|
          format.json do |variant|
            variant.collection_for_select do
              results = ::Searches::HolidayRequestUserOptionsSearch.new(scope: users_scope, filters: context.params[:f]).results
              paginate_opts = params.dup
              paginate_opts[:filters] ||= {}
              paginate_opts[:filters][:paginated_collection_for_select] = true
              @users = paginate(HolidayRequestPolicy::Scope.new(current_user, results).public_send("#{source_action}_resolve"),
                                adjust_pagination(paginate_opts))
              render template: '/api/v1/holiday_requests/user_options', collection: @users
            end
          end
        end
      end

      def update_holiday_request(holiday_request, adjusted_holiday_request_params, new_file_params)
        persist_holiday_request_opts = {
          actor: current_user,
          object: holiday_request,
          params: adjusted_holiday_request_params,
          action: :update,
          policy: policy(holiday_request)
        }
        form = PersistHolidayRequest.call(**persist_holiday_request_opts)
        holiday_request = form.object
        @_prev_changes = form.notable_changes
        if form.form_success?
          @_phase_two_success = holiday_request.update(new_file_params)
          if @_phase_two_success
            holiday_request.instance_variable_set(:@previously_changed, @_prev_changes.merge(holiday_request.previous_changes))
          end
        end
        form.form_success? && @_phase_two_success
      end

      def meta_headers(hash)
        hash.each { |k, v| headers[k] = v.to_json }
      end

      def serialize_index(results, context_params, _meta, variant)
        @holiday_requests = results
        @context_params = context_params
        case variant
        when :collection_for_select
          render template: '/api/v1/holiday_requests/index', collection: @holiday_requests
        when :none
          # raise ActiveRecord::RecordNotFound, 'Page not found' if params[:page].to_i > 1 && @holiday_requests.empty?
        end
      end

      def find_holiday_request
        interaction = ::GetHolidayRequestsInteractor.call(current_user: current_user, params: params)
        interaction.applicant_holiday_requests.find(params[:id])
      end

      def holiday_request_params(holiday_request = nil)
        params.require(:holiday_request).permit(policy(holiday_request || HolidayRequest).permitted_attributes)
      end

      def find_holiday_request_applicant(holiday_request)
        id = holiday_request_params[:applicant_id] || (holiday_request.new_record? ? current_user.try(:id) : holiday_request.applicant_id)
        User.find_by(id: id)
      end
    end
  end
end
