module Api
  module V1
    class PositionsController < ApiController
      before_action :find_position, except: %i[index create]

      def index
        authorize(Position, :index?)
        results = search(scope: positions_scope, filters: params[:f]).results
        @positions = authorize!(results).decorate
        respond_with(@positions)
      end

      def create
        authorize(Position, :create?)
        position = Position.new(position_params)
        position.save
        respond_with(position.decorate)
      end

      def update
        authorize(Position, :update?)
        @position = authorize!(find_position)
        @position.update(position_params)
        respond_with(@position.decorate)
      end

      def show
        authorize(Position, :show?)
        @position = authorize!(find_position)
        respond_with(@position.decorate)
      end

      def destroy
        @position = authorize!(find_position)
        @position.destroy
        respond_with(@position.decorate)
      end

      private

      def search(options = {})
        @search = ::Searches::PositionSearch.new(search_options(options))
      end

      def positions_scope
        Position.all
      end

      def find_position
        positions_scope.find(params[:id])
      end

      def position_params
        params.require(:position).permit(:id, :name)
      end
    end
  end
end
