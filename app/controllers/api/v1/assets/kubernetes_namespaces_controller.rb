class Api::V1::Assets::KubernetesNamespacesController < Api::V1::ApiController
  include ::Api::V1::Concerns::Assets::AllowedParams

  before_action :authorize

  def create
    kubernetes_namespace = KubernetesNamespace.create(
      kubernetes_namespace_params.merge(requester: current_user, requested_date: Date.current)
    )
    respond_with :assets, kubernetes_namespace
  end

  def show
    @kubernetes_namespace = policy_scope(KubernetesNamespace).find(params[:id]).decorate
    return unless current_user.is_a?(ApiKey)

    @users = User.joins(memberships: :roles).where(
      roles: { cluster_keys_visible: true }, memberships: { project: @kubernetes_namespace.project }
    ).distinct.pluck(:username)
  end

  def index
    @kubernetes_namespaces = search(
      scope: policy_scope(namespaces_scope), filters: params
    ).results.includes(:requester, :passed_to_decommission_by, :kubernetes_cluster)
  end

  private

  def namespaces_scope
    KubernetesNamespace.where(state: %i[active closed])
  end

  def search(options = {})
    ::Searches::KubernetesNamespaceSearch.new(search_options(options))
  end

  def authorize
    super(KubernetesNamespace)
  end

  def kubernetes_namespace_params
    params.require(:kubernetes_namespace).permit(allowed_params_for(:kubernetes_namespace))
  end
end
