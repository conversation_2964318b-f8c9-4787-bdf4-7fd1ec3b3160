module Api
  module V1
    module Assets
      class SslCertificatesController < ApiController
        include ::Api::V1::Concerns::Assets::AllowedParams
        respond_to :json, :multipart_form

        def create
          authorize(Asset, :create?)
          ssl_certificate = SslCertificate.new(ssl_certificate_params.merge(requester: current_user,
                                                                            requested_date: Date.current))
          if ssl_certificate.save
            render json: ssl_certificate, status: :created
          else
            render json: { errors: ssl_certificate.errors.messages }, status: :unprocessable_entity
          end
        end

        def show
          authorize(Asset, :show?)
          @ssl_certificate = SslCertificate.find(params[:id])
        end

        private

        def ssl_certificate_params
          params.require(:ssl_certificate).permit(allowed_params_for(:ssl_certificate))
        end
      end
    end
  end
end
