module Api
  module V1
    class DepartmentMembersController < ApiController
      respond_to :json

      before_action :find_agreement

      def index
        authorize(Department)
        members = @department.users.order(:state)
        filtered_members = params[:f].try(:[], :query).present? ? filter_members(members) : members
        @members = paginate(filtered_members)
      end

      private

      def filter_members(scope)
        scope.where('users.first_name LIKE ? OR users.last_name LIKE ?',
                    "%#{params[:f][:query]}%", "%#{params[:f][:query]}%")
      end

      def find_agreement
        @department = policy_scope(Department).find(params[:department_id])
      end
    end
  end
end
