module GusDataHelper
  private

  def download_gus_data_and_validate
    GUS_BIR_CLIENT.with do |gus_bir1_client|
      gus_record = gus_bir1_client.find_by(nip: vat_number).first

      unless gus_record&.regon
        errors.add(:download_from_gus, :cannot_download_data)
        throw(:abort)
      end

      self.country = 'Polska'
      copy_attributes(gus_record)

      check_vat_payer
    end
  end

  def copy_attributes(gus_record)
    assign_attributes(
      name: gus_record.name,
      street: gus_record.street,
      street_number: gus_record.street_number,
      apartment: gus_record.house_number,
      city: gus_record.city,
      postcode: gus_record.zip_code,
      post: gus_record.post_city,
      voivodeship: gus_record.province,
      district: gus_record.district,
      community: gus_record.community
    )
  end

  def check_vat_payer
    self.vat_payer = VATRegister.vat_subject?(vat_number)
  end

  def adjust_vat_number
    return if vat_number.blank?

    self.vat_number = foreign? ? vat_number.gsub(/\W/, '') : vat_number.gsub(/\D/, '')
  end

  def foreign?
    return false unless country

    %w[polska poland].exclude?(country.strip.downcase)
  end
end
