module AccountingHelper
  def payments_summary(records_by_currency)
    records_by_currency.map do |currency_info|
      {
        currency: currency_info.currency,
        paid: currency_info.paid_sum,
        scheduled_payments: currency_info.scheduled_sum,
        balance: currency_info.balance
      }
    end
  end

  def companies_payments_summary(companies)
    current_company = nil
    companies.map do |company|
      hash = {
        currency: currency_string(company.currency),
        paid_sum: company.paid_sum,
        scheduled_sum: company.scheduled_sum,
        balance: company.paid_sum - company.scheduled_sum,
        length: companies.select { |selected_company| selected_company.id == company.id }.length
      }
      hash[:company] = { id: company.id, name: company.name } if current_company != company.id
      current_company = company.id
      hash
    end
  end

  def clients_payments_summary(clients)
    current_client = nil
    (clients + [nil]).each_cons(2).map do |client, next_client|
      hash = {
        currency: currency_string(client.currency || 0),
        paid_sum: client.paid_sum,
        scheduled_sum: client.scheduled_sum,
        pending_sum: client.pending_sum,
        balance: client.paid_sum - client.scheduled_sum,
        length: clients.select { |selected_client| selected_client.id == client.id }.length,
        client_id: client.id
      }
      hash[:client] = { id: client.id, name: client.name } if current_client != client.id
      hash[:is_last] = true if !next_client || client.id != next_client.id
      current_client = client.id
      hash
    end
  end

  def projects_summary(projects)
    current_project = nil
    projects.includes(:accounting_number).map do |project|
      hash = project_currency_data(project, projects)
      if current_project != project.id
        hash[:project] = {
          id: project.id, name: project.name, responsible: responsible_for(project)
        }
      end
      current_project = project.id
      hash
    end
  end

  private

  def project_currency_data(project, projects)
    {
      currency: project.currency,
      paid_sum: project.paid_sum,
      pending_sum: project.pending_sum,
      scheduled_sum: project.scheduled_sum,
      balance: project.paid_sum - project.scheduled_sum,
      length: projects.select { |selected_project| selected_project.id == project.id }.length,
      project_id: project.id,
      accounting_number: project.accounting_number&.number
    }
  end

  def responsible_for(project)
    User.active
        .joins(memberships: :roles)
        .where(memberships: { project_id: project.id }, roles: { responsible: true })
        .distinct
        .map(&:full_name)
        .join(', ')
  end

  def currency_string(currency)
    Project.currencies.detect { |_, v| v == currency }&.first
  end
end
