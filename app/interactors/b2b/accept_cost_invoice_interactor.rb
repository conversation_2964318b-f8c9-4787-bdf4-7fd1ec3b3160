module B2B
  class AcceptCostInvoiceInteractor
    include Interactor

    after :notify

    def call
      context.cost_invoice.enable_force_accept if context.force_accept
      context.cost_invoice.accept!
      context.cost_invoice.create_snapshot(context.user, :accept)
    rescue AASM::InvalidTransition => e
      save_hours_reported
      raise e
    end

    private

    def save_hours_reported
      CostInvoice.where(id: context.cost_invoice.id).update_all( # rubocop:disable Rails/SkipsModelValidations
        hours_reported: context.cost_invoice.hours_reported
      )
    end

    def notify
      CostInvoiceNotificationMailer.with(
        controller_id: context.user.id, cost_invoice_id: context.cost_invoice.id
      ).notify_accepted.deliver_later
    end
  end
end
