module B2B
  class RejectCostInvoiceInteractor
    include Interactor

    after :notify

    def call
      context.cost_invoice.reject!
      context.cost_invoice.create_snapshot(context.user, :reject)
    end

    private

    def notify
      CostInvoiceNotificationMailer.with(
        controller_id: context.user.id, cost_invoice_id: context.cost_invoice.id
      ).notify_rejected.deliver_later
    end
  end
end
