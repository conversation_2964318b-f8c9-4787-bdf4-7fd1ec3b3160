class RemoteWorkPeriods::CreateInteractor
  include Interactor

  delegate :remote_work_period, to: :context

  def call
    context.remote_work_period = RemoteWorkPeriod.new(context.remote_work_period_params)

    set_user
    return unless context.remote_work_period.save

    send_email
  end

  private

  def set_user
    remote_work_period.user = context.user unless context.global_create && remote_work_period.user.present?
  end

  def send_email
    RemoteWorkPeriodMailer.remote_work_period_created(remote_work_period.id, context.user.id).deliver_later
  end
end
