class UnarchiveProjectInteractor
  include Interactor
  include ProjectStatusInteractor

  before :check_for_active_ancestors

  def call
    Project.transaction do
      project.update status: :active
      project.create_activity key: 'project.unarchive'
    end

    RedmineProjectsWorker.perform_in(5.seconds, 'unarchive', project.id)
    share_directories([project])
  end

  private

  def check_for_active_ancestors
    return if project.ancestors.where.not(status: :active).empty?

    context.fail!(
      message: { errors: { parent: [t('interactors.unarchive_project.ancestors_active')] } }
    )
  end
end
