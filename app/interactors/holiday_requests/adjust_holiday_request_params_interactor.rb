module HolidayRequests
  class AdjustHolidayRequestParamsInteractor < BaseInteractor
    delegate :actor, to: :context
    delegate :applicant, to: :context
    delegate :holiday_request, to: :context
    delegate :action, to: :context

    before do
      check_prerequisites
    end

    def call
      context.params = act_on_context
      context.new_file_params = @new_file_params if action == :update
      context
    end

    private

    def adjust_holiday_request_params(holiday_request_params)
      holiday_request_params.merge!(
        HolidayRequests::AdjustHolidayRequestParams.call(
          actor: actor,
          object: holiday_request,
          action: action,
          params: holiday_request_params
        )
      )
    end

    def act_on_context
      send(:"act_on_#{action}", context.params)
    end

    def current_applicant_contracts_type(date_from, date_to)
      applicant.user_contracts.overlapping(date_from, date_to).distinct.pluck(:agreement_type)
    end

    def act_on_create(holiday_request_params) # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      adjusted_holiday_request_params = adjust_holiday_request_params(holiday_request_params)
      adjusted_holiday_request_params.delete(:file)
      adjusted_holiday_request_params[:category] = nil if adjusted_holiday_request_params[:category].blank?

      if applicant && applicant.board_member? && holiday_request_params[:reject].to_s != 'true'
        default_examiner_comment = 'Holiday request for board member\'s are `accepted` automatically.'
        adjusted_holiday_request_params.merge!(HolidayRequest.accept_params).merge(examiner_id: actor.id)
      elsif create_valid_o_ch_category(adjusted_holiday_request_params)
        default_examiner_comment = "`#{adjusted_holiday_request_params[:category]}` leaves are `accepted` automatically."
        adjusted_holiday_request_params.merge!(HolidayRequest.accept_params).merge(examiner_id: actor.id)
      end
      additional_attributes = additional_param_options(adjusted_holiday_request_params, default_examiner_comment)
      adjusted_holiday_request_params.merge!(additional_attributes) unless additional_attributes.empty?
      adjusted_holiday_request_params
    end

    def create_valid_o_ch_category(adjusted_holiday_request_params)
      applicant_contracts_types = current_applicant_contracts_type(adjusted_holiday_request_params['starts_on'],
                                                                   adjusted_holiday_request_params['ends_on'])

      ['Niedostępność/Ch', 'Niedostępność/O'].include?(adjusted_holiday_request_params[:category]) &&
        applicant_contracts_types.include?('employment')
    end

    def act_on_update(holiday_request_params) # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity, Metrics/MethodLength
      if holiday_request.category == holiday_request_params['convert_to']
        holiday_request_params = holiday_request_params.reject { |k, v| k.to_s == 'convert_to' }
      end

      adjusted_holiday_request_params = adjust_holiday_request_params(holiday_request_params)

      if holiday_request.category.blank? && adjusted_holiday_request_params[:category].blank?
        adjusted_holiday_request_params[:category] = nil
      end
      # NOTE: juz odrzuconych wnioskow, nawet board member, nie powinien miec akceptowanych 'automatycznie', bo moze dojsc do akcepatacji przez przypadek
      unless holiday_request.rejected_at
        if applicant&.board_member? && holiday_request_params[:reject].to_s != 'true'
          default_examiner_comment = 'Holiday request for board member\'s are `accepted` automatically.'
          adjusted_holiday_request_params.merge!(HolidayRequest.accept_params(accepted_at: adjusted_holiday_request_params[:accepted_at] || holiday_request.accepted_at)).merge(examiner_id: actor.id)
        elsif update_valid_o_ch_category(adjusted_holiday_request_params[:category], holiday_request_params)
          default_examiner_comment = "#{adjusted_holiday_request_params[:category]} leaves are `accepted` automatically."
          adjusted_holiday_request_params.merge!(HolidayRequest.accept_params(accepted_at: adjusted_holiday_request_params[:accepted_at] || holiday_request.accepted_at)).merge(examiner_id: actor.id)
        elsif update_valid_o_ch_category(adjusted_holiday_request_params[:convert_to], holiday_request_params)
          default_examiner_comment = "Request category converted to `#{adjusted_holiday_request_params[:category]}`. `#{adjusted_holiday_request_params[:category]}` leaves are `accepted` automatically."
        end
        additional_attributes = if %w[Niedostępność/Ch Niedostępność/O].include?(adjusted_holiday_request_params[:convert_to]) &&
                                   holiday_request.category == adjusted_holiday_request_params[:convert_to]
                                  {}
                                else
                                  additional_param_options(adjusted_holiday_request_params, default_examiner_comment)
                                end
        adjusted_holiday_request_params.merge!(additional_attributes) unless additional_attributes.empty?
      end

      file_params = adjusted_holiday_request_params.delete(:file)
      if file_params.present?
        require 'base64'
        @new_file_params = {
          original_filename: file_params[:filename],
          file_data_uri: "data:#{file_params[:filetype]}\;base64,#{file_params[:base64]}"
        }
        model_instance = HolidayRequest.new(@new_file_params)
        unless !model_instance.valid? && model_instance.errors.include?(:file)
          adjusted_holiday_request_params[:remove_file] = true
        end
      else
        @new_file_params = {}
        adjusted_holiday_request_params[:remove_file] = true if adjusted_holiday_request_params[:remove_file].to_s == 'true'
      end

      adjusted_holiday_request_params
    end

    def update_valid_o_ch_category(param, holiday_request_params)
      applicant_contracts_types = current_applicant_contracts_type(holiday_request_params['starts_on'],
                                                                   holiday_request_params['ends_on'])

      ['Niedostępność/Ch', 'Niedostępność/O'].include?(param) && applicant_contracts_types.include?('employment')
    end

    def additional_param_options(holiday_request_params, default_examiner_comment)
      opts = {}
      if holiday_request_params[:examiner_comment].present?
        opts[:examiner_comment_author] = actor.username
        opts[:examiner_comment] = holiday_request_params[:examiner_comment].to_s
      else
        if default_examiner_comment.present?
          opts[:examiner_comment_author] = '[DEFAULT COMMENT]'
          opts[:examiner_comment] = default_examiner_comment
        end
      end
      opts
    end

    def check_prerequisites
      args = context.to_h
      args.fetch(:params)
      args.fetch(:actor)
      args.fetch(:applicant)
      args.fetch(:action)
      args.fetch(:holiday_request) if args[:action] == :update
    end
  end
end
