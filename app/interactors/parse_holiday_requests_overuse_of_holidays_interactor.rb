class ParseHolidayRequestsOveruseOfHolidaysInteractor < BaseInteractor
  def call
    parse_overuse_of_holidays(context.params)
    context
  end

  private

  def parse_overuse_of_holidays(params)
    if params[:f] && params[:f][:overuse_of_holidays].to_s == 'true'
      unless policy.overuse_of_holidays?
        raise Pundit::NotAuthorizedError, 'not allowed to use filter: overuse_of_holidays'
      end
    end
  end
end
