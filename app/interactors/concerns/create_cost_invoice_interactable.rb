module CreateCostInvoiceInteractable
  extend ActiveSupport::Concern

  included do
    delegate :cost_invoice, to: :context
  end

  def call
    context.cost_invoice = cost_invoice_class.new(
      context.cost_invoice_params.merge(user: context.current_user)
    )
    post_init_treatment
    create_snapshot
  end

  private

  def create_snapshot
    return unless cost_invoice.save

    cost_invoice.create_snapshot(context.current_user, :create, initial_state: 'new')
  end

  def cost_invoice_class
    self.class.module_parent::CostInvoice
  end
end
