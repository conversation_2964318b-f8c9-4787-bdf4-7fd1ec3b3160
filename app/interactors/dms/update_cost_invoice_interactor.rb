module Dms
  class UpdateCostInvoiceInteractor
    include Interactor

    after :auto_accept
    after :notify

    delegate :cost_invoice, to: :context

    def call
      cost_invoice.assign_attributes(context.cost_invoice_params)
      cost_invoice.send_to_controller if context.send_to_controller
      create_snapshot
    end

    private

    def auto_accept
      cost_invoice.snapshots.reload
      return unless perform_auto_accept?

      AcceptCostInvoiceInteractor.call(user: context.current_user, cost_invoice: cost_invoice,
                                       action: :auto_accept, do_not_notify: true)
    end

    def perform_auto_accept?
      cost_invoice.valid? && context.send_to_controller &&
        context.current_user.id == cost_invoice.user_id &&
        !cost_invoice.pending_controller?
    end

    def create_snapshot
      cost_invoice.create_snapshot(context.current_user, :update, comment: context.comment) if cost_invoice.save
    end

    def notify
      return unless cost_invoice.saved_change_to_state?

      Dms::NewNotifierWorker.perform_async(cost_invoice.id)
    end
  end
end
