module Dms
  class CreateCostInvoiceInteractor
    include Interactor
    include CreateCostInvoiceInteractable

    after :auto_accept

    private

    def post_init_treatment
      cost_invoice.send_to_controller if context.send_to_controller
    end

    def auto_accept
      return if auto_accept?

      cost_invoice.reload

      AcceptCostInvoiceInteractor.call(user: context.current_user, cost_invoice: cost_invoice,
                                       action: :auto_accept, do_not_notify: true)
      Dms::NewNotifierWorker.perform_async(cost_invoice.id)
    end

    def auto_accept?
      cost_invoice.new_record? || !context.send_to_controller || cost_invoice.pending_controller?
    end
  end
end
