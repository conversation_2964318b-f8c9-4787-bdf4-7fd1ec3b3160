class TrainingRequests::CreateInteractor
  include Interactor

  def call
    context.training_request = TrainingRequest.new(context.training_request_params)

    set_user
    return unless context.training_request.save

    send_email
  end

  private

  def set_user
    context.training_request.user = context.user unless context.global_create && context.training_request.user.present?
  end

  def send_email
    TrainingRequestMailer.training_request_created(context.training_request.id, context.user.id).deliver_later
  end
end
