class GetTeamCalendarUsersInteractor < BaseInteractor
  def call
    search_params = { scope: users_scope, filters: split_params }
    context.users = Searches::UserSearch.new(search_params)
                                        .results
                                        .order(Arel.sql('(users.department_id IS NULL)'),
                                               :department_id)
  end

  private

  def split_params
    return if params[:f].blank?

    user_params = params[:f].select do |k, _v|
      k.in?(%w[departments_ids project_id term])
    end
    params[:f].delete_if { |k, _v| k.in?(user_params.keys) }
    user_params
  end

  def users_scope
    User.internal.native.active.where(system: false)
  end
end
