class OutlookCalendarEventsWorker
  include Sidekiq::Worker

  def perform(action, user_id, holiday_request_id, outlook_calendar_event_id)
    @user = User.find(user_id)
    @holiday_request = HolidayRequest.find_by(id: holiday_request_id)
    @outlook_calendar_event_id = outlook_calendar_event_id

    send("perform_#{action}")
  end

  private

  attr_reader :user, :holiday_request, :outlook_calendar_event_id

  def perform_create
    delete_event(holiday_request.outlook_calendar_event_id)
    event = create_event
    holiday_request.update(outlook_calendar_event_id: event.id)
  end

  def perform_update
    delete_event
    event = create_event
    holiday_request.update(outlook_calendar_event_id: event.id)
  end

  def perform_destroy
    delete_event
    holiday_request&.update(outlook_calendar_event_id: nil)
  end

  def create_event
    event_body = MicrosoftGraphApi::EventBuilder.new(holiday_request).call
    MicrosoftGraphApi::Events.create_user_event(user, event_body)
  end

  def delete_event(event_id = outlook_calendar_event_id)
    return if event_id.blank?

    MicrosoftGraphApi::Events.delete_user_event(user, event_id)
  rescue MicrosoftGraph::Models::ODataErrorsODataError => e
    logger.info e.error.inspect
    raise if e.error.code != 'ErrorItemNotFound'
  end
end
