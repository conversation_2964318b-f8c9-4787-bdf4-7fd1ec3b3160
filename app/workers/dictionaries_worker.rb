require 'sidekiq-scheduler'

class DictionariesWorker
  include Sidekiq::Worker

  def perform
    path = Settings.invoices_integration.files_path
    Company.native.find_each do |company|
      generate_regular_dictionary(company, path)
      generate_clients_projects_dictionary(company, path)
      generate_accounting_numbers_dictionary(company, path)
    end
  end

  private

  def generate_regular_dictionary(company, path)
    xml = DictionariesGenerator.generate(company)
    file_name = regular_file_name(company)
    File.write(File.join(path, file_name), xml)

    SMBWorker.perform_async(file_name, File.join(company.name, 'KSIĘGOWOŚĆ', 'Słowniki'),
                            'estelligence_share')
    SMBWorker.perform_async(file_name, File.join(company.name, 'KSIĘGOWOŚĆ', 'Słowniki'),
                            'bi_share')
  end

  def generate_clients_projects_dictionary(company, path)
    xml = AccountingNumbersClientsDictionaryGenerator.generate(company)
    file_name = "#{company.name.parameterize}_clients_projects_dictionary.xml"
    File.write(File.join(path, file_name), xml)

    SMBWorker.perform_async(file_name, File.join(company.name, 'KSIĘGOWOŚĆ', 'Słowniki'),
                            'bi_share')
  end

  def generate_accounting_numbers_dictionary(company, path)
    xml = AccountingNumbersDictionaryGenerator.generate(company)
    file_name = "#{company.name.parameterize}_projects_dictionary.xml"
    File.write(File.join(path, file_name), xml)

    SMBWorker.perform_async(file_name, File.join(company.name, 'KSIĘGOWOŚĆ', 'Słowniki'),
                            'bi_share')
  end

  def regular_file_name(company)
    "#{Time.zone.now.utc.to_formatted_s(:number)}#{company.name.parameterize}_dictionary.xml"
  end
end
