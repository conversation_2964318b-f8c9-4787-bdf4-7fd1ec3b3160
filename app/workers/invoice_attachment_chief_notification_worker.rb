require 'sidekiq-scheduler'

class InvoiceAttachmentChiefNotificationWorker
  include Sidekiq::Worker

  def perform
    chief_invoices = Hash.new { |hash, key| hash[key] = [] }
    invoices.each do |invoice|
      chief = invoice.user.department.chief
      chief_invoices[chief] << invoice
    end
    chief_invoices.each do |chief, grouped_invoices|
      InvoiceAttachmentNotificationMailer.notify_chief(chief, grouped_invoices).deliver_now
    end
  end

  private

  def invoices
    Invoice.includes(:attachments)
           .where(state: [1, 2, 4], attachments: { id: nil },
                  no_attachment: false)
           .includes(user: { department: :chief },
                     payment: { payment_schedule: :project })
           .where(created_at: 1.month.ago..Time.zone.now)
  end
end
