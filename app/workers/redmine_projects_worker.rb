class RedmineProjectsWorker
  include RedmineModelWorker

  private

  def resource_name
    'project'
  end

  def body_for(project) # rubocop:disable Metrics/MethodLength
    { project: {
      imperator_id: project.id, name: project.name, identifier: project.identifier,
      description: project.description, homepage: project.homepage,
      is_public: false, parent_id: project.parent.try(:redmine_id),
      owner_id: project.author.try(:redmine_id),
      inherit_members: project.inherit_members?,
      owncloud: project.owncloud,
      sla: project.sla,
      custom_field_values: {
        '5' => project.account_number, '14' => project.company.try(:name),
        '7' => project.code_name
      }
    } }
  end

  %w(archive unarchive close reopen).each do |action|
    define_method("perform_#{action}") do |id|
      project = Project.find_by(id: id)
      return unless project
      # TODO: rework api so that this endpoint return 200 instead of 3xx on success
      RedmineApi.post("#{base_url}/#{project.redmine_id}/#{action}.json",
                      follow_redirects: false)
    end
  end

  def find_project(id)
    Project.find_by(id: id)
  end
end
