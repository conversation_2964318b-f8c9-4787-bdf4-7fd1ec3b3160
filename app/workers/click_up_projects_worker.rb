class ClickUpProjectsWorker
  include ClickUpModelWorker

  private

  def resource_name
    'project'
  end

  def body_for(project)
    {
      name: project.name
    }
  end

  def perform_destroy(parent_id, click_up_id)
    ClickUpApi.delete("#{delete_base_url(parent_id)}/#{click_up_id}")
  end

  def find_project(id)
    Project.find_by(id: id)
  end

  def base_url(id)
    project = find_project(id)
    space_project?(project) && !cooperative_project?(project) ? '/space' : '/folder'
  end

  def delete_base_url(parent_id)
    parent_id.blank? ? '/space' : '/folder'
  end

  def create_base_url(id)
    project = find_project(id)
    cooperative_project = cooperative_project?(project)

    if space_project?(project) && !cooperative_project
      team_id = project&.company&.click_up_workspace_id
      "/team/#{team_id}/space"
    else
      parent_id = cooperative_project ? Settings.space_cooperative_project_id.to_i : project&.parent&.click_up_id
      "/space/#{parent_id}/folder"
    end
  end

  def space_project?(project)
    project&.accounting_number&.number&.in?(Settings.arte_space_accounting_number) && project.parent.blank?
  end

  def cooperative_project?(project)
    project.cooperative_project? && Settings.space_cooperative_project_id.present? && !project.company.click_up_synchronization?
  end
end
