require 'sidekiq-scheduler'

# Reminds about user contracts expiration

class UserContractsReminderWorker
  include Sidekiq::Worker

  sidekiq_options retry: false

  def perform
    four_weeks_scope.each do |contract|
      UserContractsReminderMailer.four_weeks_hr_reminder(contract).deliver_later
      UserContractsReminderMailer.four_weeks_chief_reminder(contract).deliver_later
    end

    six_weeks_scope.each do |contract|
      UserContractsReminderMailer.six_weeks_hr_reminder(contract).deliver_later
      UserContractsReminderMailer.six_weeks_chief_reminder(contract).deliver_later
    end
  end

  private

  def mailer
    @mailer ||= UserContractsReminderMailer
  end

  def base_scope
    UserContract.includes(user: { department: :chief })
  end

  def four_weeks_scope
    base_scope.where(ends_on: 4.weeks.from_now.to_date)
  end

  def six_weeks_scope
    base_scope.where(ends_on: 6.weeks.from_now.to_date)
  end
end
