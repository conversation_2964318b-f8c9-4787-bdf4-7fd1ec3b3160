class RedmineResourceBookingsWorker
  include RedmineModelWorker

  DEFAULT_HOURS_TO_BOOK = 8

  def perform(action, user_id, custom_hours, *args)
    @user = User.find(user_id)
    @hours = custom_hours || DEFAULT_HOURS_TO_BOOK
    return unless redmine_user?

    range_args = args.each_slice(2).map { |first, last| Date.parse(first)..Date.parse(last) }
    send("perform_#{action}", *range_args)
  end

  private

  attr_reader :user

  def perform_create(range)
    booking_ids = scheduled_in(range)
    delete_bookings(booking_ids)
    create_bookings(range)
  end

  def perform_update(old_range, new_range)
    old_booking_ids = scheduled_in(old_range)
    delete_bookings(old_booking_ids)
    create_bookings(new_range)
  end

  def perform_destroy(range)
    booking_ids = scheduled_in(range)
    delete_bookings(booking_ids)
  end

  def create_bookings(range)
    start_date = range.begin
    end_date = range.end
    RedmineApi.post('/resource_bookings.json',
                    body: { resource_booking: {
                      assigned_to_id: user.redmine_id,
                      project_id: user.company.holiday_project_id,
                      issue_id: user.company.holiday_issue_id,
                      start_date: start_date, end_date: end_date, notes: '.',
                      hours_per_day: @hours
                    } })
  end

  def delete_bookings(ids)
    return if ids.empty?

    r = RedmineApi.delete('/resource_bookings/destroy.json', body: { ids: ids })
    logger.debug 'destroy bookings response:'
    logger.debug r.inspect
    r
  end

  def scheduled_in(range)
    from = range.begin
    to = range.end
    response = get_resource_bookings(from, to)
    handle_resource_bookings_response(response, from, to)
  end

  def handle_resource_bookings_response(response, from, to)
    resource_bookings = response['resources']
    ids = resource_bookings.map { |resource_booking| resource_booking['id'] }
    total_count = response['total_count']
    if resource_bookings.count < total_count
      (2..((total_count / resource_bookings.count) + 1)).each do |page|
        ids += get_resource_bookings(from, to, page)['resources'].map do |resource_booking|
          resource_booking['id']
        end
      end
    end
    ids
  end

  def get_resource_bookings(from, to, page = 1)
    RedmineApi.get('/resource_bookings.json',
                   query: { f: %w[assigned_to_id project_id],
                            op: { assigned_to_id: '=', project_id: '=' },
                            v: { assigned_to_id: [user.redmine_id.to_s],
                                 project_id: holiday_project_ids },
                            date_from: from, date_to: to,
                            limit: 100, page: page })
  end

  def holiday_project_ids
    @holiday_project_ids ||= Company.native.order(id: :asc).pluck(:holiday_project_id)
  end

  def redmine_user?
    user.redmine? && user.redmine_id
  end
end
