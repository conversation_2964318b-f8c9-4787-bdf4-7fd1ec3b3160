class RedmineUsersWorker
  include RedmineModelWorker

  private

  def create(resource)
    return unless resource.redmine?
    super
    # we need to sync user's groups and memberships in case it's an old user but
    # is being synchronized with Redmine just now.
    update(resource) # hack: redmine don't accept group_ids on create
    resource.memberships.each(&:touch)
    resource.public_keys.each(&:save)
  end

  def perform_lock(id)
    RedmineApi.put("/users/#{id}.json", body: { user: { status: 3 } })
  end

  def resource_name
    'user'
  end

  def body_for(user)
    {
      user: {
        login: user.username,
        mail: user.email,
        firstname: user.first_name,
        lastname: user.last_name,
        status: user.locked? ? 3 : 1,
        auth_source_id: Settings.redmine_auth_source_id,
        group_ids: user.groups.pluck(:redmine_id).compact,
        activation_date: user.activates_on,
        mpk_number_id: user.mpk_number.try(:key),
        company: user.company.try(:name),
        position: user.position.try(:name),
        has_approvals: user.has_approvals,
        time_reports_not_required: user.time_reports_not_required
      }
    }
  end
end
