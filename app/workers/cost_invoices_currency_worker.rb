require 'sidekiq-scheduler'

class CostInvoicesCurrencyWorker
  include Sidekiq::Worker

  def perform
    Dms::CostInvoice.where.not(currency: 'PLN')
                    .where.not(invoice_date: nil)
                    .where(before_invoice_date_currency_rate: nil)
                    .where(sell_date: 1.year.ago..)
                    .find_each(&:update_before_invoice_date_currency_rate)
  end
end
