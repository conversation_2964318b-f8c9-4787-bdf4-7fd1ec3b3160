require 'sidekiq-scheduler'

class InvoiceAttachmentNotificationWorker
  include Sidekiq::Worker

  def perform
    Invoice.includes(:attachments, payment: { payment_schedule: :project })
           .where(state: [1, 2, 4], attachments: { id: nil }, no_attachment: false)
           .each do |invoice|
      users = account_users(invoice)
      next if users.empty?

      InvoiceAttachmentNotificationMailer.notify_attachment(invoice, users).deliver
    end
  end

  private

  def account_users(invoice)
    User.joins(memberships: :roles).active
        .where(memberships: { project_id: invoice.project.id }, roles: { responsible: true })
        .distinct
  end
end
