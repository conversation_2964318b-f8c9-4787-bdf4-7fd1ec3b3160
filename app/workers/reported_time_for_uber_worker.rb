require 'sidekiq-scheduler'

class ReportedTimeForUberWorker
  include Sidekiq::Worker

  def perform
    time_reports = fetch_time_reports
    time_range = time_boundaries.first..time_boundaries.last
    notify_departments_with_time_data(time_range, time_reports)
  end

  private

  def notify_departments_with_time_data(time_range, time_reports)
    User.active.includes(departments_as_uber_chief: :active_users)
        .where(departments: { notify_time_collectively: true }).each do |uber_chief|
      departments_time_data = {}
      uber_chief.departments_as_uber_chief.each do |department|
        fill_data_for_department(departments_time_data, department, time_range, time_reports)
      end

      next if departments_time_data.empty?

      ReportedTimeMailer.report_for_uber_chief_collectively(uber_chief, departments_time_data)
                        .deliver_now
    end
  end

  def fill_data_for_department(departments_time_data, department, time_range, time_reports)
    reported_time_data = {}
    department.active_users.each do |user|
      fill_data_for_user(user, time_range, time_reports, reported_time_data)
    end
    departments_time_data[department] = reported_time_data if reported_time_data.any?
  end

  def fill_data_for_user(user, time_range, time_reports, reported_time_data)
    return if user.time_reports_not_required? || !user.redmine_id

    user_dates = {}
    time_range.each do |date|
      fill_data_for_date(user, date, user_dates, time_reports)
    end
    reported_time_data[user] = user_dates if user_dates.any?
  end

  def fill_data_for_date(user, date, user_dates, time_reports)
    return if skip_date?(date, user)

    reports = time_reports.select do |report|
      Date.parse(report['spent_on']) == date && report['user']['id'] == user.redmine_id
    end
    sum = reports.sum { |report| report['hours'] }.round(2)
    user_dates[date] = sum if sum < user.working_hours(date)
  end

  def fetch_time_reports
    RedmineWorkingTimeApi::Importer.new.between(*time_boundaries).import
  end

  def skip_date?(date, user)
    !user.working_day?(date) || (user.activates_on || user.created_at.to_date) > date
  end

  def time_boundaries
    [3.weeks.ago.to_date, 1.week.ago.to_date]
  end
end
