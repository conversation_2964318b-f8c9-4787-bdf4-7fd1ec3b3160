module RedmineModelWorker
  extend ActiveSupport::Concern

  included do
    include Sidekiq::Worker
    sidekiq_options queue: :redmine
  end

  def perform(action, *args)
    send("perform_#{action}", *args)
  end

  private

  def perform_update(id)
    resource = resource_name.camelize.constantize.find(id)
    resource.redmine_id ? update(resource) : create(resource)
  end

  def perform_destroy(redmine_id)
    RedmineApi.delete("#{base_url}/#{redmine_id}.json")
  end

  def create(resource)
    response = RedmineApi.post("#{base_url}.json",
                                body: body_for(resource))
    resource.redmine_id = response[resource_name]['id']
    resource.save!
    response
  end

  def update(resource)
    RedmineApi.put("#{base_url}/#{resource.redmine_id}.json",
                    body: body_for(resource))
  end

  def base_url
    "/#{resource_name.tableize}"
  end

  def resource_name
    raise NotImplementedError
  end

  def body_for(*_)
    raise NotImplementedError
  end
end
