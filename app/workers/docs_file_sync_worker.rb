require 'owncloud_provider'

class DocsFileSyncWorker
  include Sidekiq::Worker

  def perform
    docs_file_ids_to_destroy = DocsFile.ids
    Project.active.where(docs_cloud: true).find_each do |project|
      sync_project(project, docs_file_ids_to_destroy)
    end

    DocsFile.destroy(docs_file_ids_to_destroy)
  end

  private

  def sync_project(project, docs_file_ids_to_destroy)
    DocsFile.categories.keys.each do |category|
      files = provider.get_project_files(project.identifier, category)

      files.each do |file|
        sync_file_data(project, category, file, docs_file_ids_to_destroy)
      end
    end
  end

  def sync_file_data(project, category, file, docs_file_ids_to_destroy)
    docs_file = DocsFile.find_or_initialize_by(docs_file_id: file.id)
    docs_file.project = project
    docs_file.category = category
    docs_file.file_name = file.file_name
    docs_file.exists_in_docs = true
    docs_file.save
    docs_file_ids_to_destroy.delete(docs_file.id)
  end

  def provider
    @provider ||= OwncloudProvider.new('docscloud')
  end
end
