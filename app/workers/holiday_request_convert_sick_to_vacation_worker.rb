require 'sidekiq-scheduler'
require 'date_utils'

class HolidayRequestConvertSickToVacationWorker
  include Sidekiq::Worker
  include HolidayRequests::NotifierCommons

  def perform # rubocop:disable Metrics/AbcSize
    if Settings.disable_hr_workers.to_s == 'true' || (Settings.disable_hr_workers.respond_to?(:[]) &&
        Settings.disable_hr_workers.disable_holiday_request_convert_sick_to_vacation_worker.to_s == 'true')
      return
    end

    deadline_calculator = ->(record) { (record.created_at + 14.days).to_date }
    perform_task(holiday_requests_scope_of_category('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/Ch'), deadline_calculator)
    deadline_calculator = ->(record) { (record.ends_on + 7.days).to_date }
    perform_task(holiday_requests_scope_of_category('Niedostępność/O'), deadline_calculator)
  end

  private

  def add_previous_examiner_to_receipients(receipients, holiday_request, _opts = {})
    examiner_id_was = holiday_request.examiner_id_was
    examiner = examiner_id_was ? User.active.where(id: examiner_id_was).first : holiday_request.try(:examiner)
    receipients << [examiner, true] if examiner && examiner.email
    receipients
  end

  # @overload
  def gather_receipients(holiday_request, opts = {})
    receipients = []
    receipients = add_applicant_to_receipients(receipients, holiday_request, opts)
    # worker zmienil wlasnie kategorie i dodal komentarz, wiec jest ostatnim examinerem (nil), trzeba wziac wczesniejszego
    receipients = add_previous_examiner_to_receipients(receipients, holiday_request, opts)
    receipients = add_hr_manager_to_receipients(receipients)
    receipients.uniq
  end

  # rubocop:disable Metrics/AbcSize, Layout/LineLength
  def perform_task(scope, deadline_calculator)
    scope.find_in_batches(batch_size: 100).with_index do |batch, _index|
      sleep(0.1) # Make sure it doesn't get too crowded in there!
      batch.each do |record|
        deadline = deadline_calculator.call(record)
        # skip if yesterday was exact deadline and also a holiday
        next if day_after_deadline_is_today?(deadline) && day_is_holiday?(record, (Time.zone.now - 1.day).to_date)

        next unless over_deadline?(deadline)

        examiner_comment = "Request category converted from `#{record.category}` to `Niedostępność`, because leave was not justified or documented within 7 days."
        record.update(HolidayRequest.convert_to_vacation_params(examiner_comment: examiner_comment))
        schedule_dispatch(record, origin: 'holiday_request_convert_sick_to_vacation') if record.save
      end
    end
  end
  # rubocop:enable Metrics/AbcSize, Layout/LineLength

  def day_is_holiday?(holiday_request, date)
    !holiday_request.applicant.working_day?(date)
  end

  def day_after_deadline_is_today?(deadline)
    Time.zone.now.to_date == deadline + 1.day
  end
end
