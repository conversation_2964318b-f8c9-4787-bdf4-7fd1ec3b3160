module Dms
  class NewNotifierWorker
    include Dms::Notifiable

    def perform(cost_invoice_id)
      cost_invoice = CostInvoice.find(cost_invoice_id)
      supervisors = fetch_supervisors(cost_invoice)
      return if supervisors.empty?

      supervisors.each do |supervisor|
        Dms::NotificationMailer.notify_supervisor(supervisor, cost_invoice).deliver_now
      end

      cost_invoice.update(notified_at: Time.zone.now)
    end
  end
end
