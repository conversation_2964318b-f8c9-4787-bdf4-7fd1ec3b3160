require 'sidekiq-scheduler'

class RemoteWorkPeriodNotifierWorker
  include Sidekiq::Worker

  def perform
    scope = RemoteWorkPeriod.pending
    scope = scope.where(notified_at: ..2.days.ago).or(scope.where(created_at: ..5.days.ago, notified_at: ..1.day.ago))

    scope.find_each do |remote_work_period|
      notify(remote_work_period)
    end
  end

  private

  def notify(remote_work_period)
    RemoteWorkPeriod.transaction do
      remote_work_period.update!(notified_at: Time.current)
      RemoteWorkPeriodMailer.remote_work_period_reminder(remote_work_period).deliver_now
    end
  end
end
