require 'owncloud_provider'

class DocscloudWorker
  include Sidekiq::Worker
  sidekiq_options queue: :docscloud

  def perform(action, *args)
    if action.to_s == 'create_directory'
      create_directory(*args)
    else
      owncloud_provider.public_send(action, *args)
    end
  end

  private

  def create_directory(directory_name, permissions)
    owncloud_provider.create_directory(
      directory_name, permissions, subdirectories: DocsFile.categories.transform_values { nil }
    )
  end

  def owncloud_provider
    @owncloud_provider ||= OwncloudProvider.new('docscloud')
  end
end
