require 'sidekiq-scheduler'

class TrainingRequestNotifierWorker
  include Sidekiq::Worker

  def perform
    scope = TrainingRequest.pending.includes(user: { department: %i[chief uber_chief] })
    scope = scope.where(notified_at: ..2.days.ago).or(scope.where(created_at: ..5.days.ago, notified_at: ..1.day.ago))

    scope.find_each do |training_request|
      notify(training_request)
    end
  end

  private

  def notify(training_request)
    TrainingRequest.transaction do
      training_request.update!(notified_at: Time.current)
      TrainingRequestMailer.training_request_reminder(training_request).deliver_now
    end
  end
end
