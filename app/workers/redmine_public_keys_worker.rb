class RedminePublicKeysWorker
  include RedmineModelWorker

  private

  def perform_update(id)
    public_key = PublicKey.find(id)
    return unless public_key.user.redmine?
    response = RedmineApi.post("#{base_url}.json",
                               body: body_for(public_key))
    public_key.redmine_id = response['id']
    public_key.save!
    response
  end

  def body_for(public_key)
    {
      user_id: public_key.user.redmine_id,
      gitolite_public_key: {
        title: public_key.identifier,
        key: public_key.key
      }
    }
  end

  def base_url
    '/gitolite_public_keys'
  end
end
