module B2B
  class CostInvoiceDecorator < ApplicationDecorator
    delegate_all

    def actions_for_current_user(user)
      policy = CostInvoicePolicy.new(user, object)
      policy.bulk_permissions
    end

    def acceptors # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity, Metrics/MethodLength
      user = contractor&.user
      department = user&.department
      uber_chief = department&.uber_chief
      substitute_chief = department&.substitute_chief

      if pending_department? && department.present?
        acceptors = if uber_chief == user
                      uber_chief_acceptor(department).compact
                    else
                      chief_acceptor(department, user_as_substitute_chief: substitute_chief == user).compact
                    end
        acceptors = controllers if user == acceptors.first

        acceptors.map(&:full_name).join(', ')
      elsif pending?
        controllers.map(&:full_name).join(', ')
      end
    end

    private

    def controllers
      User.active.joins(:global_roles)
          .where(global_roles: { notify_dms_controller_acceptances: true })
          .distinct
    end
  end
end
