module Pages
  class DashboardDecorator < ApplicationDecorator
    RECORDS_LIMIT = 3

    delegate_all

    %i[my_dms_cost_invoices teams_assets_requests employees_dms_cost_invoices employees_hr_cost_invoices
       employees_next_weeks_holidays employees_holiday_requests employees_assets_requests my_dms_cost_invoices_for_correction
       my_holiday_requests_pending].each do |attribute_name|
      define_method("#{attribute_name}_count") do
        object.public_send(attribute_name).count
      end
    end

    def my_holiday_requests
      # first pending, then accepted, then rejected
      super.reorder('ISNULL(rejected_at) DESC, ISNULL(accepted_at) DESC, starts_on ASC')
           .limit(RECORDS_LIMIT)
    end

    def my_holiday_requests_pending
      super.reorder('ISNULL(rejected_at) DESC, ISNULL(accepted_at) DESC, starts_on ASC')
           .limit(RECORDS_LIMIT)
    end

    def my_dms_cost_invoices
      super.reorder(invoice_date: :desc)
           .limit(RECORDS_LIMIT)
    end

    def my_dms_cost_invoices_for_correction
      super.reorder(invoice_date: :desc)
           .limit(RECORDS_LIMIT)
    end

    def teams_assets_requests
      super.reorder(requested_date: :asc)
           .limit(RECORDS_LIMIT)
    end

    def employees_dms_cost_invoices
      super.reorder(invoice_date: :asc)
           .limit(RECORDS_LIMIT)
    end

    def employees_hr_cost_invoices
      super.reorder(invoice_date: :asc)
           .limit(RECORDS_LIMIT)
    end

    def employees_next_weeks_holidays
      super.reorder(starts_on: :asc)
           .limit(RECORDS_LIMIT)
    end

    def employees_holiday_requests
      super.reorder(starts_on: :asc)
           .limit(RECORDS_LIMIT)
    end

    def employees_assets_requests
      super.reorder(requested_date: :asc)
           .limit(RECORDS_LIMIT)
    end
  end
end
