payment_schedule = payment_schedule.decorate unless payment_schedule.decorated?
payments ||= payments_scope(payment_schedule.payments)
json.extract! payment_schedule, :id, :target_current_amount,
              :target_total_amount, :current_amount
json.default_currency payment_schedule.project.currency
json.lock_day_reached payment_schedule.lock_day_reached?
json.payments_attributes do
  json.array! payments do |payment|
    json.extract! payment, :id, :issued_on, :sell_date, :description, :predicted_amount,
                  :cyclic, :cycle_length, :originator_id, :ends_on, :kind, :currency
    json.project_identifier payment.payment_schedule.project.identifier
    json.shared payment.payment_schedule_id != payment_schedule.id
    json.invoice_no payment.current_invoice.try(:id)
    json.pending_invoice_no payment.pending_invoice.try(:id)
    json.accepted_invoice_no payment.accepted_invoice.try(:id)
    json.should_lock_edit payment.should_lock_edit?(current_user)
    json.mpk_positions_attributes do
      json.array!(payment.mpk_positions) do |mpk_position|
        json.extract! mpk_position, :id, :amount, :mpk_number_id, :project_id
        json.mpk_number_key mpk_position.mpk_number.key
        json.mpk_number_full_name mpk_position.mpk_number.decorate.full_name
      end
    end
  end
end
json.url project_payment_schedule_url(payment_schedule.project, format: :json)
json.cache_ts((Time.current.to_f * 1000).ceil)
