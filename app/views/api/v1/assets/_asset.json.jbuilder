asset = asset.decorate unless asset.decorated?
json.partial! '/api/v1/escape_json', locals: {
  model_attributes: AssetDecorator.attribute_names_visible_to_all,
  resource: asset
}
json.project_name h(asset.project_name)
json.username asset.user.username if asset.user
json.cache_ts((Time.current.to_f * 1000).ceil)
json.activated_by asset.activated_by.username if asset.active?
json.activated_date asset.activated_date if asset.active?
