json.tax_rates do
  json.array! @tax_rates do |tax_rate|
    json.key tax_rate
    json.name InvoicePosition.human_attribute_name("tax_rate.#{tax_rate}")
  end
end

json.flows do
  json.array! @flows do |flow|
    json.key flow
    json.name Dms::CostInvoice.human_attribute_name("flow.#{flow}")
  end
end

json.payment_methods do
  json.array! @payment_methods do |payment_method|
    json.key payment_method
    json.name Dms::CostInvoice.human_attribute_name("payment_method.#{payment_method}")
  end
end

json.kinds do
  json.array! @kinds do |kind|
    json.key kind
    json.name Dms::CostInvoice.human_attribute_name("kind.#{kind}")
  end
end
