user = user.decorate unless user.decorated?
json.cache! user do
  json.partial! '/api/v1/escape_json', locals: {
    model_attributes: UserDecorator.attribute_names_for_collection_for_select_visible_to_all,
    resource: user
  }
  json.department_names user.departments.to_a.map { |i| controller.view_context.angular_escape(h(i.name))}.sort if user.association_instance_get(:departments).present? && user.departments.any?
  json.url user_url(user, format: :json)
  json.full_name controller.view_context.angular_escape(h(user.full_name))
  json.cache_ts((Time.current.to_f * 1000).ceil)
end
