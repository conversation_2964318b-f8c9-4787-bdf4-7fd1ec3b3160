json.project do
  json.extract! @project, :id, :name, :account_number, :days_to_payment, :bank_account_id
  if @project.client
    json.main_address do
      json.extract!(@project.client, :name, :street, :street_number, :apartment, :city,
                    :additional_address, :postcode, :post, :country, :voivodeship, :district,
                    :community, :id, :vat_number)
      json.client_name @project.client.name
      json.client_id @project.client.id
    end
  end
end

json.company do
  json.extract! @project.company, :id, :name, :address1, :address2, :city, :zipcode
end

json.payment do
  json.extract! @payment, :predicted_amount, :issued_on, :sell_date
end
