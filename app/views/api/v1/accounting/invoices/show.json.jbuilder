json.invoice do
  json.extract! @invoice, :id, :total_amount, :due_date, :sell_date, :invoice_date,
                :receiver_name, :description, :correction_reason, :created_by, :state, :title,
                :kind, :associated_advance_invoice_id, :client_address_id, :number,
                :total_order_amount, :issued_at, :attachments_required

  json.amends_id @invoice.amends.id if @invoice.amendment?
  json.associated_advance_invoice_number @invoice.associated_advance_invoice&.number
  json.currency @invoice.payment.currency

  json.attachments do
    json.array! @invoice.attachments do |attachment|
      json.extract! attachment, :id
      json.file JSON.parse(attachment.file_data)
      json.file_url file_attachments_url(attachment)
      json.required @invoice.attachment_required?(attachment)
    end
  end

  json.required_attachments do
    json.array! @invoice&.required_attachments || [] do |attachment|
      json.extract! attachment, :id, :attachment_id
    end
  end

  json.invoice_document do
    invoice_document = @invoice.invoice_document
    if invoice_document
      json.extract! invoice_document, :id
      json.url invoice_document_accounting_invoice_url(invoice_document)
      json.document JSON.parse(invoice_document.document_data)
    else
      json.nil!
    end
  end

  json.send_type @invoice.electronic_invoice_sending_method? ? 'electronic' : 'paper'

  json.invoice_positions_attributes do
    json.array!(@invoice.invoice_positions) do |invoice_position|
      json.extract! invoice_position, :name, :amount, :tax_rate, :unit_price, :net_value, :jpk_gtu, :jpk_transaction_code
    end
  end
  if @invoice.amendment?
    json.amended_invoice_positions_attributes do
      json.array!(@invoice.amends.invoice_positions) do |invoice_position|
        json.extract! invoice_position, :name, :amount, :tax_rate, :unit_price, :net_value, :jpk_gtu, :jpk_transaction_code
      end
    end
    json.any_invoice_positions_changes(
      json.attributes!['invoice_positions_attributes'] != json.attributes!['amended_invoice_positions_attributes']
    )
  end

  json.mpk_positions_attributes do
    json.array!(@invoice.mpk_positions.includes(:mpk_number)) do |mpk_position|
      json.extract! mpk_position, :amount
      json.mpk_number_full_name mpk_position.mpk_number.decorate.full_name
    end
  end

  json.revenue_account do
    json.extract! @invoice.revenue_account, :name
  end
end

json.update policy(@invoice).update?

json.company do
  json.extract! @invoice.project.company, :name
end

json.project do
  json.extract! @invoice.project, :id, :identifier, :name, :currency, :account_number
  client_address = @invoice.client_address || @invoice.project.client
  json.main_address do
    json.extract!(client_address, :street, :street_number, :city, :postcode, :post,
                  :apartment, :additional_address, :country, :voivodeship, :district, :community,
                  :id, :vat_number)
    json.client_name client_address.name
    json.client_id @invoice.project.client.id
  end
  json.accounting_numbers @invoice.mpk_positions.includes(project: :accounting_number).pluck(:number)
end

json.kinds @kinds.map { |kind| [kind, Invoice.human_attribute_name("kind.#{kind}")] }.to_h
