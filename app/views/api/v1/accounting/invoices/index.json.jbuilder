json.array! @invoices do |invoice|
  json.extract! invoice, :id, :total_amount, :created_by, :description, :invoice_date,
                :total_amount, :correction_reason, :number, :no_attachment, :sell_date,
                :kind
  json.any_attachments invoice.attachments.any?
  json.project_name invoice.project.name
  json.corrects invoice.correction_reason.present?
  json.currency invoice.payment.currency
end
