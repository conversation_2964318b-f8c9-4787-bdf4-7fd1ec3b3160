user = user.decorate unless user.decorated?
json.partial! '/api/v1/escape_json', locals: {
  model_attributes: UserDecorator.attribute_names_visible_to_all,
  resource: user
}
if user.persisted?
  json.groups do
    json.array! user.groups do |group|
      json.partial! '/api/v1/groups/group_for_select', locals: { group: group }
    end
  end
  json.global_roles do
    json.array! user.global_roles do |global_role|
      json.partial! '/api/v1/global_roles/global_role_for_select', locals: { global_role: global_role }
    end
  end
  json.departments do
    json.array! user.departments do |department|
      json.partial! '/api/v1/departments/department_for_select', locals: { department: department }
    end
  end
end

json.bio { json.partial! '/api/v1/bios/bio_brief', locals: { bio: user.bio } } if user.bio.present?
json.full_name controller.view_context.angular_escape(h(user.full_name))
json.first_password user.first_password if action_name.to_s == 'create'
json.has_owncloud_password user.hashed_passwords['owncloud'].present?
json.has_dev_password user.hashed_passwords['dev'].present?
if action_name == 'show'
  json.names_of_departments_as_uber_chief user.names_of_departments_as_uber_chief
  json.names_of_departments_as_supervisor user.names_of_departments_as_supervisor
  json.names_of_departments_as_chief user.names_of_departments_as_chief
  json.names_of_departments_as_substitute_chief user.names_of_departments_as_substitute_chief
end
json.url user_url(user, format: :json)
json.cache_ts((Time.current.to_f * 1000).ceil)
json.has_business_cards user.cards.present?
json.cards_attributes do
  json.array!(user.cards) do |card|
    json.extract! card, :id, :company_id, :code, :active, :expires_on
    json.company_name card.company.name
  end
end
