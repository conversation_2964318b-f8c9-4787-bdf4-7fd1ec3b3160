group = group.decorate unless group.decorated?
json.partial! '/api/v1/escape_json', locals: {
  model_attributes: GroupDecorator.attribute_names_visible_to_all,
  resource: group
}
if group.persisted?
  json.users do
    json.array!(group.active_users) do |user|
      json.partial! '/api/v1/groups/user_for_select', locals: { user: user }
    end
  end
end
json.url group_url(group, format: :json)
json.cache_ts((Time.current.to_f * 1000).ceil)
