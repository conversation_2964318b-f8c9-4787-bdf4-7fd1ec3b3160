json.partial! 'form'
json.cost_invoice do
  json.extract! @cost_invoice, :id, :contractor_id, :company_id, :sell_date, :due_date,
                :invoice_date, :auto_cost_projects, :total_amount, :state, :number,
                :hours_worked, :hours_reported
  json.accounting_number_id @cost_invoice.cost_projects.first&.accounting_number_id
  json.cost_invoice_positions_attributes do
    json.array!(@cost_invoice.cost_invoice_positions) do |cost_invoice_position|
      json.extract! cost_invoice_position, :id, :name, :amount, :tax_rate, :unit_price,
                    :net_value
    end
  end

  json.actions_for_current_user @cost_invoice.actions_for_current_user(current_user)

  if @cost_invoice.document
    json.document_url document_b2b_cost_invoice_url(@cost_invoice)
    json.document JSON.parse(@cost_invoice.document_data)
  end

  json.company_name @cost_invoice.company&.name

  json.cost_projects_attributes do
    json.array!(@cost_projects) do |cost_project|
      json.accounting_number_description cost_project.accounting_number.description
      json.amount cost_project.amount
      json.accounting_number do
        if cost_project.accounting_number
          json.partial!('/api/v1/accounting_numbers/accounting_number', accounting_number: cost_project.accounting_number)
        else
          json.nil!
        end
      end
    end
  end

  if @cost_invoice.contractor
    json.contractor do
      json.extract! @cost_invoice.contractor, :name, :street, :street_number, :apartment,
                    :additional_address, :city, :postcode, :country, :vat_number, :post,
                    :voivodeship, :district, :community
    end
  end

  json.acceptor @cost_invoice.acceptors
end
