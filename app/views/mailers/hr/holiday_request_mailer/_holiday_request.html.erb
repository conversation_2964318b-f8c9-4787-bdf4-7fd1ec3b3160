<tr>
  <td align="center" valign="top">
    <table border="0" cellpadding="10" cellspacing="0" width="100%" id="emailBody">
      <tr>
        <td align="left" valign="top">
          <%= holiday_request.applicant.decorate.full_name %>
          <% if @receipient_email == holiday_request.applicant.email %>
            <b>(Ja)</b>
          <% end %>
        </td>
        <td align="right" valign="top">
          <% if holiday_request.starts_on == holiday_request.ends_on %>
            <%= holiday_request.starts_on.try(:strftime, "%Y-%m-%d") %>
          <% else %>
            <%= holiday_request.starts_on.try(:strftime, "%Y-%m-%d") %>
            -
            <%= holiday_request.ends_on.try(:strftime, "%Y-%m-%d") %>
          <% end %>
          <!-- <%= root_url + "#/holidays/my_holidays/#{holiday_request.id}" %> -->
        </td>
      </tr>
    </table>
  </td>
</tr>
