<%= content_for :title do %>
Email confirmed!
<% end %>

<h1>Thank you. Your email address and username are now confirmed!</h1>
<p>
  <%= link_to "You\'ll be redirected", root_url, { id: 'a-redirect' } %>
</p>
<script>
  function delayedRedirect() {
    window.location = '/' + window.location.search;
  }
  // doesn't work in older IEs
  document.addEventListener('DOMContentLoaded', function () {
    setTimeout('delayedRedirect()', 3000);
  }, false);
  // Web API (IE9+)
  document.getElementById('a-redirect').addEventListener('click', function(e) {
    e.preventDefault();
    delayedRedirect();
  });
</script>
