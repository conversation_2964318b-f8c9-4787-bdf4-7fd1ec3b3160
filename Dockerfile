FROM ruby:3.1.3 AS build-step
WORKDIR /app
ENV HTTP_PROXY 'http://repo.non.3dart.com:3128'
ENV RAILS_ENV 'production'
RUN echo "Acquire::http::Proxy \"${HTTP_PROXY}\";" > /etc/apt/apt.conf
RUN apt-get update -qq && apt-get install -y -qq default-libmysqlclient-dev libcrack2 cmake libssl-dev
RUN gem install bundler --no-document
COPY . .
RUN bundle install --jobs $(nproc) --without development test --deployment --quiet --path=cache/bundler

COPY tools/sidekiq/entryponint.sh .
RUN chmod 755 /app/entryponint.sh
ENTRYPOINT ["/app/entryponint.sh"]
