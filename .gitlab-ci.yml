image: "ruby:3.1.3"

services:
  - name: mariadb:10.2.27
    alias: mariadb
  - redis

variables:
  RAILS_ENV: test
  MYSQL_ROOT_PASSWORD: root
  REDIS_URL: redis://redis:6379
  APP_NAME: imperator-api
  DOCKER_REGISTRY: harbor.efigence.com/imperator
  DOCKER_HOST: tcp://dind-service:2375
  DOCKER_TLS_CERTDIR: ""
  http_proxy: http://repo.non.3dart.com:3128
  https_proxy: http://repo.non.3dart.com:3128
  NO_PROXY: localhost,127.0.0.1,.non.3dart.com,docker,.artegence.com,dind-service
  DISABLE_SPRING: 1

cache:
  untracked: true
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - cache/bundler/

before_script:
  - apt-get update -qq && apt-get install -y -qq default-libmysqlclient-dev libcrack2 cmake libssl-dev
  - ruby -v
  - which ruby
  - gem install bundler --no-document
  - cp config/database.yml.gitlab config/database.yml
  - bundle install --jobs $(nproc) "${FLAGS[@]}" --path=cache/bundler

stages:
  - test
  - stable_image

test:
  stage: test
  artifacts:
    when: always
    expire_in: 1 day
    paths:
      - coverage/
  script:
    - cat config/settings/test.gitlab.yml >> config/settings/test.yml
    - ./wait-for-it.sh mariadb:3306 --timeout=120
    - bundle exec rails db:create
    - bundle exec rails db:schema:load
    - bundle exec rails test

audit:
  stage: test
  script:
    - bundle exec bundle-audit check --update
  allow_failure: true

pronto:
  stage: test
  except:
    - master@efigence-projekt-imperator/imperator
  script:
    - bundle exec pronto run -c=origin/master --exit-code

stable_image:
  image: docker:git
  services:
    - name: docker:25
      alias: dind-service
  stage: stable_image
  before_script: []
  cache: {}
  script:
    - echo $HARBOR_ROBOT_PASSWORD | docker login harbor.efigence.com -u $HARBOR_ROBOT_USERNAME --password-stdin
    - release_version=${CI_COMMIT_TAG:-${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}}
    - docker pull ${DOCKER_REGISTRY}/${APP_NAME}-sidekiq:$release_version
    - docker tag ${DOCKER_REGISTRY}/${APP_NAME}-sidekiq:$release_version ${DOCKER_REGISTRY}/${APP_NAME}-sidekiq:stable
    - docker push ${DOCKER_REGISTRY}/${APP_NAME}-sidekiq:stable
    - docker pull ${DOCKER_REGISTRY}/${APP_NAME}:$release_version
    - docker tag ${DOCKER_REGISTRY}/${APP_NAME}:$release_version ${DOCKER_REGISTRY}/${APP_NAME}:stable
    - docker push ${DOCKER_REGISTRY}/${APP_NAME}:stable
  only:
    - master

development_image:
  image: docker:git
  services:
    - name: docker:25
      alias: dind-service
  stage: stable_image
  before_script: []
  cache: {}
  script:
    - echo $HARBOR_ROBOT_PASSWORD | docker login harbor.efigence.com -u $HARBOR_ROBOT_USERNAME --password-stdin
    - release_version=${CI_COMMIT_TAG:-${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}}
    - docker pull ${DOCKER_REGISTRY}/${APP_NAME}-sidekiq:$release_version
    - docker tag ${DOCKER_REGISTRY}/${APP_NAME}-sidekiq:$release_version ${DOCKER_REGISTRY}/${APP_NAME}-sidekiq:development
    - docker push ${DOCKER_REGISTRY}/${APP_NAME}-sidekiq:development
    - docker pull ${DOCKER_REGISTRY}/${APP_NAME}:$release_version
    - docker tag ${DOCKER_REGISTRY}/${APP_NAME}:$release_version ${DOCKER_REGISTRY}/${APP_NAME}:development
    - docker push ${DOCKER_REGISTRY}/${APP_NAME}:development
  only:
    - dev
