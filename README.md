[![pipeline status](https://git.efigence.com/efigence-projekt-imperator/imperator/badges/master/pipeline.svg)](https://git.efigence.com/efigence-projekt-imperator/imperator/commits/master)
[![coverage report](https://git.efigence.com/efigence-projekt-imperator/imperator/badges/master/coverage.svg)](https://git.efigence.com/efigence-projekt-imperator/imperator/commits/master)


## Instalacja

``` bash
bundle install
bundle exec rake db:create # moze wywalic sie na ost. srodowisku, ale zadziala
bin/rake db:migrate db:seed
bin/rake db:test:prepare # wazne zwlaszcza podczas przepinania miedzy branchami
bin/rake test
# gdy failuja sprobuj:
# bin/spring stop
# bundle exec rake
firefox http://localhost:3000/swagger/

# WhoIsWho
# Scaffolding examples
# && bin/rails $SCAFFOLD_COMMAND pundit:policy $MODEL_NAME \
DISABLE_SPRING='1' \
SCAFFOLD_COMMAND='g' \
&& MODEL_NAME='Todo' \
&& CONTROLLER_UNDERSCORE=`echo "${MODEL_NAME}" | sed -r 's/([a-z0-9])([A-Z])/\1_\L\2/g'` \
&& CONTROLLER_NAME="${CONTROLLER_UNDERSCORE,,}s" \
&& SCAFFOLD_COLUMNS='user_id:integer department_id:integer phone:string mobile:string file_data:text' \
&& bin/rails $SCAFFOLD_COMMAND model $MODEL_NAME $SCAFFOLD_COLUMNS \
&& bin/rails $SCAFFOLD_COMMAND swagger_block $MODEL_NAME $SCAFFOLD_COLUMNS \
&& bin/rails $SCAFFOLD_COMMAND decorator $MODEL_NAME \
&& bin/rails $SCAFFOLD_COMMAND custom_responders_controller "api/v1/${CONTROLLER_NAME}" $SCAFFOLD_COLUMNS --model-name=$MODEL_NAME --skip-decorator --skip-policy
```

## Uruchamianie testów


``` bash
bin/rake db:test:prepare # wazne zwlaszcza podczas przepinania miedzy branchami
bin/rake test

# Automated, continuous testing with Guard
bundle exec guard

# Alternatywne sposoby na odpalenie testów

# stop na pierwszym błędzie
TESTOPTS="--fail-fast" bundle exec rake

# uruchomienie jednego testu
be rake test TEST=path/to/test_file.rb TESTOPTS="--name test_method_name"

# uruchomienie jednego testu, rspec way
bundle exec m path/to/test_file.rb:LN
```

## Przyklad operacji z uzyciem curla

``` bash
# pobierasz token
curl -si 'http://localhost:3000/api/auth/sign_in' -H 'Pragma: no-cache' -H 'Accept-Encoding: gzip, deflate' -H 'Content-Type: application/json;charset=UTF-8' -H 'Accept: application/json, text/plain, */*' -H 'Connection: keep-alive' --data-binary '{"email":"<EMAIL>","password":"1234Aaaa"}' --compressed > out.txt
ACCESS_TOKEN=`cat out.txt | tr -d '\r' | sed -En 's/^Access-Token: (.*)/\1/p'`
if [ -z "${ACCESS_TOKEN}" ]; then ACCESS_TOKEN=`cat out.txt | tr -d '\r' | sed -En 's/^access-token: (.*)/\1/p'`; fi
EXPIRY=`cat out.txt | tr -d '\r' | sed -En 's/^Expiry: (.*)/\1/p'`
if [ -z "${EXPIRY}" ]; then EXPIRY=`cat out.txt | tr -d '\r' | sed -En 's/^expiry: (.*)/\1/p'`; fi
CLIENT=`cat out.txt | tr -d '\r' | sed -En 's/^Client: (.*)/\1/p'`
if [ -z "${CLIENT}" ]; then CLIENT=`cat out.txt | tr -d '\r' | sed -En 's/^client: (.*)/\1/p'`; fi
# szybko uzyj go, bo wygasa
curl -si '%{http_code}\n' 'http://localhost:3000/api/projects.json' -H "access-token:$ACCESS_TOKEN" -H "client:$CLIENT" -H 'token-type:Bearer' -H "expiry:$EXPIRY" -H "uid:<EMAIL>" -H 'Pragma: no-cache' -H 'Accept-Encoding: gzip, deflate' -H 'Content-Type: application/json;charset=UTF-8' -H 'Accept: application/json, text/plain, */*' -H 'Connection: keep-alive' --compressed
```

## Obsługa etag'ów (na razie tylko pages/navbars i projects jako proof of concept)

``` bash
RAILS_ENV=production be rails s

curl -I -X GET \
-H "If-Modified-Since: Tue, 21 Jun 2016 12:18:05 GMT" \
-H "If-None-Match: \"2473e6f8d1ebe44f64c595cbcb39a27a\"" \
-H 'Accept: application/vnd.api+json' \
-H 'X-Swagger-Sign-In-As: 1' \
'http://localhost:3000/api/projects?f%5Bcollection_for_select%5D=true'

curl -I -X GET \
-H "If-Modified-Since: Tue, 21 Jun 2016 12:18:05 GMT" \
-H "If-None-Match: \"64a456110381c03c6620f1d884742e39\"" \
-H 'Accept: application/vnd.api+json' \
-H 'X-Swagger-Sign-In-As: 1' \
'http://localhost:3000/api/projects'

curl -I -X GET \
-H "If-Modified-Since: Tue, 21 Jun 2016 12:18:05 GMT" \
-H "If-None-Match: \"64a456110381c03c6620f1d884742e39\"" \
-H 'Accept: application/vnd.api+json' \
-H 'X-Swagger-Sign-In-As: 1' \
'http://localhost:3000/api/projects/1'
```

## Moze pozniej sie przydac

Modify Headers for Google Chrome plugin

## Best Practices

Model:
```
 #-Constants
 #-Attributes
 #-Extensions
 #-Relationships
 # accepts_nested_attributes_for
 #-Validations
 #-Scopes
 #-Callbacks
 #-Class Methods
 #-Instance Methods
```

## Konwencja redirectow z Redmine (draft)

- akcja index

```
po stronie Redmine:
/projects/agora/memberships

po stronie angulara:
#/projects/agora?tab=memberships

po stronie api:
/api/memberships.json?project_id=agora
```

- akcja show

```
po stronie Redmine:
/projects/agora/some_plugin_controller/some_tab_as_path_parameter

po stronie angulara:
#/some_plugin_controller?project_id=agora&tab=some_tab_as_path_parameter

po stronie api:
/api/some_plugin_controller.json?project_id=agora&tab=some_tab_as_path_parameter
```

- inne akcje

tak samo jak akcja index

## Odpalenie lokalnie środ. produkcyjnego

```
RAILS_ENV=production rake db:migrate db:seed
RAILS_LOG_LEVEL_DEBUG=true RAILS_CONSIDER_ALL_REQUESTS_LOCAL=true RAILS_ENV=production bundle exec passenger start
RAILS_ENV=production bundle exec sidekiq -C config/sidekiq.yml
```

## Odpalenie lokalnie środ. development z dumpem bazy z produkcji

```
USE_PRODUCTION_PM_ROLE_IDS=true bundle exec passenger start
USE_PRODUCTION_PM_ROLE_IDS=true RAILS_ENV=development bundle exec sidekiq -C config/sidekiq.yml
```

## Badania okresowe CVE

```
bundle exec bundle-audit check
```

## Testing tips

Trace queries:
```
tail -n 2000 -f log/test.log | grep -A 50 test_create_multiple_for_user_ids_and_group_ids_with_rollback
```

## Acceptance tests setup ("the easy way", WARNING: must update to get `headless` working)

## Headless acceptance tests installation on Ubuntu

### Include google-chrome in your apt repo

```
wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
sudo sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list'
sudo apt-get update
sudo apt-get install google-chrome-stable
```

### Download chromedriver

```
sudo su
# platform options: linux32, linux64, mac64, win32
PLATFORM=linux64 && \
cd /usr/local/bin/ && \
VERSION=$(curl http://chromedriver.storage.googleapis.com/LATEST_RELEASE) && \
wget -O chromedriver.zip http://chromedriver.storage.googleapis.com/$VERSION/chromedriver_$PLATFORM.zip && \
unzip chromedriver.zip && \
rm chromedriver.zip
```

### Set your frontend location in ENV['FRONTEND_URL']. Default: 'http://localhost:3001'

```
cd imperator-front
BROWSERSYNC_PROXY_PORT=3001 gulp serve

cd imperator
bundle exec rake test CAPYBARA_DEFAULT_DRIVER=headless_chrome NO_COVERAGE=true TEST='test/features/*'
```

### Known problems

Incidental failures occur often ana unpredictably. Ignore this error:

```
Selenium::WebDriver::Error::WebDriverError: unexpected response, code=400, content-type="text/plain"
unhandled request

# Rendered page:

<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body><pre style="word-wrap: break-word; white-space: pre-wrap;"></pre></body></html>

```

## Swagger

```
sudo -i -u mongrel
mkdir -p /home/<USER>/html/imperator/current/public/download
ln -s /home/<USER>/html/imperator/shared/bundle/ruby/2.1.0/gems/efigence-swagger_ui_wrapper-0.0.8/vendor/assets/swagger
http://imperator.dev.non.3dart.com/download/swagger/v1.html
```

## Cracklib

```
# There is no cracklib-devel package available for CentOS 5 or Ubuntu (walidacja nie dziala na teście)
# CENTOS: sudo yum install cracklib cracklib-devel
# UBUNTU sudo apt-get install libcrack2 cracklib-runtime
# MAC sudo port install cracklib cracklib-words
sudo su
UBUNTU: ln -s /usr/lib/x86_64-linux-gnu/libcrack.so.2 /usr/lib/x86_64-linux-gnu/libcrack.so
exit
export LC_ALL=en_US.UTF-8; export LANG=en_US.UTF-8; export LANGUAGE=en_US.UTF-8; echo 'bla' | cracklib-check
bla: it is WAY too short
```

## Sidekiq

```
ps awux | grep sidekiq
SENV=production script/sidekiq start production
REDIS_URL='redis://localhost:6379/3/' bundle exec rackup -E production -D -p 3000 sidekiq-web.ru
ssh -L 9000:localhost:3000 dev55
```

## Monit

Na teście:

```
# /etc/monit.d/sidekiq_imperator.conf
check process sidekiq_imperator with pidfile /home/<USER>/html/imperator/shared/pids/sidekiq.pid
  start program = "/bin/bash -c 'cd /home/<USER>/html/imperator/current && script/sidekiq restart'" as uid 1052 # mongrel
  stop program = "/bin/bash -c 'cd /home/<USER>/html/imperator/current && script/sidekiq stop'" as uid 1052 with timeout 60 seconds
  if totalmem is greater than 500 MB for 2 cycles then restart # eating up memory?
  group imperator-sidekiq

```

## LDAP:

### Lokalna konfiguracja LDAP

- Zainstaluj ApacheDS (http://directory.apache.org/apacheds/downloads.html)
- Zainstaluj Apache Directory Studio (http://directory.apache.org/studio/)
- Włącz Apache Directory Studio, wejdź w "LDAP Servers"
- Dodaj serwer, kliknij w jego konfigurację
- Advanced Partitions Configuration
- Dodaj partycję "dc=efigence,dc=com"
- Uruchom serwer
- Konfiguracja znajduje się w pliku config/app.yml (domyślny użytkownik to uid=admin,ou=system, hasło: secret)
- Więcej informacji: https://secure.artegence.com/redmine/projects/ror/wiki/Ldap

## Import

zrob deploy
chwilowo na serwerze wywal raise if production w taskach
- import_department_users
- import_absence_balances

wykonaj:
PRO_DATABASE=true bundle exec rake imperator:initial_import_of_hr_data

## Export userów

```ruby
CSV.open(Rails.root.join('report.csv'), 'w') do |csv|
  csv << ['Użytkownik', 'email', 'login', 'Data dodania', 'Zdalny', 'Członek zarządu', 'Stanowisko', 'Redmine', 'Monitoring', 'Stan niedostępności', 'UoP', 'Firma', 'Departament', 'MPK']

  User.native.active.where(system: false).includes(:company, :position, department: :mpk_number).each do |user|
    csv << [user.full_name, user.email, user.username,
            (user.activates_on || user.created_at.to_date).to_fs(:db),
            user.remote? ? 'Tak' : 'Nie',
            user.board_member? ? 'Tak' : 'Nie',
            user.position&.name, user.redmine? ? 'Tak' : 'Nie', user.monitoring? ? 'Tak': 'Nie',
            user.absence_balance, user.user_contracts.employment.overlapping(Date.current, Date.current) ? 'Tak' : 'Nie',
            user.company.name, user.department&.name, user.mpk_number&.name]
  end
end

```

## Dodawanie userów do projektów

```ruby
Project.active.each do |project|
  m = project.memberships.find_or_initialize_by(member: u)
  m.roles << r
  m.save!
  project.touch
end
```

```ruby
date = Date.parse('2020-10-01')
date_to = date.end_of_month
CSV.open(Rails.root.join('report.csv'), 'w') do |csv|
  csv << ['Spółka', 'Numer pozycji', 'Kontrahent', 'Data sprzedaży', 'Data wystawienia', 'Kwota netto', 'Waluta']

  Invoice.includes(payment: { payment_schedule: { project: [:client, :company] }}).where(invoice_date: date..date_to).accepted_or_issued.each do |invoice|
    csv << [invoice.project.company.name, invoice.number, invoice.project.client.name, invoice.sell_date, invoice.invoice_date, invoice.total_amount / 100.0, invoice.payment.currency]
  end
end
```
## Zmiana roli usera w wielu projektach

```ruby

u.memberships.each do |membership|
  membership_role = membership.membership_roles.detect { |mr| mr.role_id == 35 && !mr.inherited_from_id }
  if membership_role
    membership.membership_roles.find_or_initialize_by(role_id: 61, inherited_from_id: nil)
    membership.save
    membership_role.destroy
  end
end
```
