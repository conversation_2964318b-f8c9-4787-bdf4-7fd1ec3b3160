class CreateDocsFiles < ActiveRecord::Migration[6.0]
  def change
    create_table :docs_files do |t|
      t.integer :project_id, null: false
      t.string :file_name
      t.integer :category
      t.integer :created_by_id
      t.integer :docs_file_id

      t.timestamps
    end
    add_index :docs_files, :project_id
    add_index :docs_files, :created_by_id
    add_index :docs_files, :docs_file_id, unique: true
    add_index :docs_files, %w[project_id file_name category], unique: true
    add_foreign_key :docs_files, :projects
  end
end
