class PopulateStartsOnInEvaluations < ActiveRecord::Migration[4.2]
  def change
    # https://apidock.com/rails/ActiveRecord/Migration/reversible
    reversible do |direction|
      direction.up do
        if 'evaluations'.classify.safe_constantize
          Evaluation.reset_column_information
          say_with_time 'Populating starts_on.' do
            Evaluation.update_all(starts_on: Time.zone.parse('2017-05-29'))
          end
        end
      end
    end
  end
end
