class CreateCalendarMonths < ActiveRecord::Migration[6.1]
  def change
    create_table :calendar_months do |t|
      t.belongs_to :user, null: false, foreign_key: true, type: :integer
      t.integer :year, null: false
      t.integer :month, null: false
      t.text :days
      t.integer :total_hours, null: false

      t.timestamps

      t.index %i[user_id year month], unique: true
    end
  end
end
