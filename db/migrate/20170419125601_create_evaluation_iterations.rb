class CreateEvaluationIterations < ActiveRecord::Migration[4.2]
  def change
    create_table :evaluation_iterations do |t|
      t.integer :evaluation_id, null: false
      t.date :deadline_on, null: false
      t.integer :number, null: false
      t.boolean :completed, null: false, default: false
      t.text :summary
      # NOTE: should be t.decimal, fixed later in a migration
      t.integer :communicativeness_avg, precision: 15, scale: 14
      t.integer :diligence_avg, precision: 15, scale: 14
      t.integer :promptness_avg, precision: 15, scale: 14
      t.integer :commitment_avg, precision: 15, scale: 14
      t.integer :independence_avg, precision: 15, scale: 14

      t.timestamps null: false
    end
  end
end
