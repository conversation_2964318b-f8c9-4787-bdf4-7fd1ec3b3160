class RemoveDefaultValueInAssets < ActiveRecord::Migration[4.2]
  def change
    change_column_default :assets, :g_account, nil
    change_column_default :assets, :backup, nil
    change_column_null :assets, :backup, true
    change_column_null :assets, :g_account, true
    add_column :assets, :internally_managed, :boolean, default: false, null: false
    add_column :assets, :internally_purchased, :boolean, default: false, null: false
    add_column :assets, :elastic_search, :boolean, default: false, null: false
    remove_column :assets, :designation
    remove_column :assets, :hq_services
    remove_column :assets, :manage_by
    remove_column :assets, :bought_by
  end
end
