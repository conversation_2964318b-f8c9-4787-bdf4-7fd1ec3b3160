class AddIssuedAtToInvoice < ActiveRecord::Migration[4.2]
  def up
    add_column :invoices, :issued_at, :datetime

    execute <<-SQL
      DROP PROCEDURE IF EXISTS migrateIssuedAt;
    SQL

    execute <<-SQL
      CREATE PROCEDURE migrateIssuedAt()
        BEGIN
          DECLARE finished INT DEFAULT 0;
          DECLARE invoice_id INT DEFAULT 0;
          DECLARE invoice_updated_at DATETIME DEFAULT NULL;
          DEClARE invoice_cur CURSOR FOR SELECT id, updated_at FROM invoices;
          DECLARE CONTINUE HANDLER FOR NOT FOUND SET finished = 1;

          OPEN invoice_cur;

          get_invoice: LOOP
            FETCH invoice_cur INTO invoice_id, invoice_updated_at;

            IF finished = 1 THEN
              LEAVE get_invoice;
            END IF;

            UPDATE invoices SET issued_at = invoice_updated_at WHERE state = 1 AND id = invoice_id;
          END LOOP;

          CLOSE invoice_cur;
        END;
    SQL

    execute <<-SQL
      CALL migrateIssuedAt();
    SQL
  end

  def down
    remove_column :invoices, :issued_at

    execute <<-SQL
      DROP PROCEDURE migrateIssuedAt;
    SQL
  end
end
