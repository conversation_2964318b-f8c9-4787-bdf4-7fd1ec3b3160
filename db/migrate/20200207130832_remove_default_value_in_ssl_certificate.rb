class RemoveDefaultValueInSslCertificate < ActiveRecord::Migration[4.2]
  def up
    change_column_default :assets, :ssl_type, nil
    change_column_default :assets, :receivers_addresses, nil
    change_column_null :assets, :ssl_type, true
    change_column_null :assets, :receivers_addresses, true
  end

  def down
    change_column_default :assets, :ssl_type, 0
    change_column_default :assets, :receivers_addresses, 0
    change_column_null :assets, :ssl_type, true
    change_column_null :assets, :receivers_addresses, true
  end
end
