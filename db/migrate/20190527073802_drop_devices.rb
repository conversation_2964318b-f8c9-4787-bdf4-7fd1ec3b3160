class DropDevices < ActiveRecord::Migration[4.2]
  def up
    drop_table :mac_addresses
    drop_table :device_locations
    drop_table :devices
  end

  def down
    create_table :devices do |t|
      t.string :name
      t.references :user, index: true, foreign_key: true
      t.string :serial
      t.string :inventory_name
      t.references :company, index: true, foreign_key: true
      t.references :mpk_number, index: true, foreign_key: true
      t.date :purchased_on
      t.integer :state
      t.date :deactivated_at
      t.references :operating_system, index: false, foreign_key: false
      t.references :operating_system_version, index: false, foreign_key: false
      t.integer :kind
      t.string :operating_system_version
      t.string :localization

      t.timestamps null: false
    end
    create_table :device_locations do |t|
      t.string :location

      t.timestamps null: false
    end
    create_table :mac_addresses do |t|
      t.references :device, index: true, foreign_key: true
      t.string :address

      t.timestamps null: false
    end
  end
end
