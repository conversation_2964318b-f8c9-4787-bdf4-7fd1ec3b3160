class MigrateHrCostInvoicesToB2B < ActiveRecord::Migration[7.0]
  def up
    execute <<~SQL.squish
      UPDATE cost_invoices set type = 'B2B::CostInvoice' where type = 'Hr::CostInvoice'
    SQL

    execute <<~SQL.squish
      UPDATE snapshot_items set item_type = 'B2B::CostInvoice' where item_type = 'Hr::CostInvoice'
    SQL

    ActiveSnapshot::SnapshotItem.where(item_type: 'B2B::CostInvoice').each do |snapshot_item|
      snapshot_item.object = snapshot_item.object.merge('type' => 'B2B::CostInvoice')
      snapshot_item.save!
    end
  end

  def down
    execute <<~SQL.squish
      UPDATE cost_invoices set type = 'Hr::CostInvoice' where type = 'B2B::CostInvoice';
    SQL

    execute <<~SQL.squish
      UPDATE snapshot_items set item_type = 'Hr::CostInvoice' where item_type = 'B2B::CostInvoice'
    SQL

    ActiveSnapshot::SnapshotItem.where(item_type: 'Hr::CostInvoice').each do |snapshot_item|
      snapshot_item.object = snapshot_item.object.merge('type' => 'Hr::CostInvoice')
      snapshot_item.save!
    end
  end
end
