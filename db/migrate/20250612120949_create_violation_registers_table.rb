class CreateViolationRegistersTable < ActiveRecord::Migration[7.0]
  def change
    create_table :violation_registers do |t|
      t.text :description, null: false
      t.text :violation_type, null: false
      t.text :violation_area
      t.text :taken_actions, null: false
      t.integer :violation_level, null: false
      t.text :attachment_data
      t.text :signature, null: false
      t.integer :created_by_id, null: false
      t.integer :company_id, null: false
      t.date :violation_occurrence_date
      t.date :violation_entry_date
      t.boolean :mandatory_report
      t.text :report_description
      t.integer :state, default: 0, null: false

      t.timestamps
    end

    add_foreign_key :violation_registers, :users, column: :created_by_id
    add_index :violation_registers, :created_by_id
    add_foreign_key :violation_registers, :companies, column: :company_id
    add_index :violation_registers, :company_id
  end
end
