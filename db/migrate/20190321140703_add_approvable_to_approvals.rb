class AddApprovableToApprovals < ActiveRecord::Migration[4.2]
  def up
    rename_column :approvals, :agreement_id, :approvable_id
    add_column :approvals, :approvable_type, :string
    add_index :approvals, :approvable_type
    add_index :approvals, [:user_id, :approvable_id, :approvable_type], unique: true
    add_index :approvals, :user_id
    ActiveRecord::Base.connection.execute('UPDATE approvals SET approvable_type = "Agreement"')
  end

  def down
    remove_index :approvals, :user_id
    remove_index :approvals, [:user_id, :approvable_id, :approvable_type]
    remove_index :approvals, :approvable_type
    remove_column :approvals, :approvable_type
    rename_column :approvals, :approvable_id, :agreement_id
  end
end
