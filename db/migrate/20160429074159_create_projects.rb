class CreateProjects < ActiveRecord::Migration[4.2]
  def change
    create_table :projects do |t|
      t.string :name
      t.text :description
      t.string :identifier
      t.string :account_number
      t.integer :parent_id
      t.boolean :inherit_members, default: true
      t.boolean :public, default: false
      t.references :company, index: true, foreign_key: true
      t.boolean :personal_data
      t.boolean :responsibility_list
      t.integer :status, default: 0

      t.timestamps null: false
    end
  end
end
