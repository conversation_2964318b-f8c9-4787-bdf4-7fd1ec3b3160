class MergeCostInvoiceDepartmentsIntoCostProjects < ActiveRecord::Migration[5.2]
  def change
    add_reference :cost_projects, :department, foreign_key: true, type: :integer

    reversible do |dir|
      dir.up do
        execute (
          <<-SQL
            UPDATE cost_projects
            SET
              department_id = (
                SELECT
                  department_id
                FROM
                  cost_invoice_departments
                  INNER JOIN cost_invoices ON cost_invoices.id = cost_invoice_departments.cost_invoice_id
                WHERE
                  cost_invoices.type = 'Dms::CostInvoice' AND
                  cost_invoice_departments.cost_invoice_id = cost_projects.cost_invoice_id AND
                  cost_invoice_departments.amount = cost_projects.amount
              );
          SQL
        )
      end

      dir.down do
        execute (
          <<-SQL
            INSERT INTO
              cost_invoice_departments (department_id, cost_invoice_id, amount, created_at, updated_at)
            SELECT
              department_id, cost_invoice_id, amount, now() AS created_at, now() AS updated_at
            FROM
              cost_projects
              INNER JOIN cost_invoices ON cost_invoices.id = cost_projects.cost_invoice_id
            WHERE
              cost_invoices.type = 'Dms::CostInvoice';
          SQL
        )
      end
    end

    drop_table :cost_invoice_departments do |t|
      t.references :department, foreign_key: true, type: :integer
      t.references :cost_invoice, foreign_key: true, type: :integer
      t.decimal :amount, precision: 12, scale: 2

      t.timestamps
    end

    add_column :cost_projects, :accounting_number, :string
    remove_column :cost_invoice_positions, :accounting_number, :string
  end
end
