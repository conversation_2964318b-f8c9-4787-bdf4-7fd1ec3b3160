class AddIndexToEvaluationIterationAssignments < ActiveRecord::Migration[4.2]
  def change
    add_index :evaluation_iteration_assignments, [:user_id, :evaluation_iteration_id],
              unique: true,
              name: :index_unique_en_in_as_on_ur_id_and_en_in_id
    add_index :evaluation_iteration_assignments, :evaluation_iteration_id, name: :index_en_in_as_on_en_in_id
    add_foreign_key :evaluation_iteration_assignments, :evaluation_iterations
    add_foreign_key :evaluation_iteration_assignments, :users
  end
end
