class CreateJoinTableEvaluationIterationsUsers < ActiveRecord::Migration[4.2]
  def change
    create_join_table :evaluation_iterations, :users, force: Rails.env.development? do |t|
      t.index :evaluation_iteration_id
      t.index :user_id
    end
    add_foreign_key :evaluation_iterations_users, :evaluation_iterations, on_delete: :cascade
    add_foreign_key :evaluation_iterations_users, :users, on_delete: :cascade
  end
end
