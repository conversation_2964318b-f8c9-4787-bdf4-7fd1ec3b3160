class CreateRegistryCategories < ActiveRecord::Migration[7.0]
  def change
    create_table :registry_categories do |t|
      t.references :project, index: true, foreign_key: true, type: :integer
      t.text :entrustment_agreement, null: false
      t.text :processing_categories, null: false
      t.text :security_measures_description, null: false
      t.text :admin_name, null: false
      t.text :admin_contact_details, null: false
      t.text :co_admin_details
      t.text :representative_admin_details
      t.text :admin_data_protection_officer
      t.text :processing_time, null: false
      t.text :third_country_or_intl_org_recipients, null: false
      t.text :security_documentation
      t.text :subcontractor_details
      t.text :sub_processing_categories
      t.text :creation_date, null: false
      t.references :created_by, foreign_key: { to_table: :users }, type: :integer
      t.integer :state, null: false, default: 0
      t.date :expiration_date

      t.timestamps
    end
  end
end
