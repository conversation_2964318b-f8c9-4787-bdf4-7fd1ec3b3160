class CreateResourceTimeEntries < ActiveRecord::Migration[6.1]
  def change
    create_table :resource_time_entries do |t|
      t.string :type, null: false
      t.belongs_to :project, null: false, foreign_key: true, type: :integer
      t.belongs_to :user, null: false, foreign_key: true, type: :integer
      t.belongs_to :issue, foreign_key: true
      t.date :date_from, null: false
      t.date :date_to
      t.decimal :hours, null: false, precision: 6, scale: 2
      t.string :activity

      t.timestamps
    end
  end
end
