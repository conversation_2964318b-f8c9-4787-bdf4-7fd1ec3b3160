class DropEmailVerses < ActiveRecord::Migration[5.2]
  def up
    drop_table :email_verses
  end

  def down
    create_table :email_verses do |t|
      t.references :email_recipient, polymorphic: true, index: false

      t.string  :email,   default: ''
      t.string  :verp,    default: ''
      t.string  :reason,  default: ''
      t.integer :amount,  default: 0

      t.timestamps
    end
    add_index :email_verses, %i[email_recipient_id email_recipient_type], name: :index_email_recipient
    add_index :email_verses, %i[email verp reason], unique: true
  end
end
