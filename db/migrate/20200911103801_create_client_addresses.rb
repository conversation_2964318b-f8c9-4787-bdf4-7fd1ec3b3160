class CreateClientAddresses < ActiveRecord::Migration[4.2]
  def change
    create_table :client_addresses do |t|
      t.references :client, index: true, foreign_key: true
      t.string :name
      t.string :street
      t.string :additional_address
      t.string :city
      t.string :postcode
      t.string :country
      t.string :vat_number
      t.string :street_number
      t.string :apartment
      t.string :post
      t.string :voivodeship
      t.string :district
      t.string :community
      t.string :identifier

      t.timestamps null: false
    end
  end
end
