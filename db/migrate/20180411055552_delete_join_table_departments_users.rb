require './db/migrate/20160928104040_create_join_table_departments_users.rb'

class DeleteJoinTableDepartmentsUsers < ActiveRecord::Migration[4.2]
  def up
    add_reference :users, :department, index: true

    User.reset_column_information

    User.find_each do |user|
      membership = DepartmentMembership.where(user: user).first

      if membership
        user.update_columns(department_id: membership.department_id) # rubocop:disable Rails/SkipsModelValidations
      end
    end

    ActiveRecord::Migration.run(CreateJoinTableDepartmentsUsers, revert: true)
  end

  def down
    ActiveRecord::Migration.run(CreateJoinTableDepartmentsUsers)

    User.find_each do |user|
      if user.department_id
        created = DepartmentMembership.create(user: user, department_id: user.department_id)
        status = created.persisted? ? 'OK' : 'ERR'
        Rails.logger.info "Reverting reference user: #{user.id} <-> department: #{user.department_id} => #{status}"
      end
    end

    remove_reference :users, :department, index: true
  end

  class DepartmentMembership < ApplicationRecord
    self.table_name = 'departments_users'

    belongs_to :user
    belongs_to :department
  end
end
