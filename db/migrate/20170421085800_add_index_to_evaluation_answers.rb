class AddIndexToEvaluationAnswers < ActiveRecord::Migration[4.2]
  def change
    add_index :evaluation_answers, [:respondent_id, :evaluation_iteration_id],
              unique: true,
              name: :index_unique_en_as_on_rt_id_en_and_in_id
    add_index :evaluation_answers, :evaluation_iteration_id
    add_index :evaluation_answers, :created_at
    add_index :evaluation_answers, :updated_at
    add_foreign_key :evaluation_answers, :evaluation_iterations
    # execute 'SET foreign_key_checks = 0;'
    add_foreign_key :evaluation_answers, :users, column: :respondent_id
    # execute 'SET foreign_key_checks = 1;'
  end
end
