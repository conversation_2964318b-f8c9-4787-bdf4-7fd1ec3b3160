class AddStateDownloadFromGusAndVATPayerToContractors < ActiveRecord::Migration[6.1]
  def change
    add_column :contractors, :download_from_gus, :boolean, default: false
    add_column :contractors, :vat_payer, :boolean, default: false
    add_column :contractors, :state, :integer, index: true

    reversible do |direction|
      direction.up do
        execute <<~SQL.squish
          UPDATE contractors SET state = 1;
        SQL
      end
    end
  end
end
