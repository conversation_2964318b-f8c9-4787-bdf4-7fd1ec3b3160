class RebuildEvaluations < ActiveRecord::Migration[4.2]
  def change
    change_table :evaluations do |t|
      t.date :ends_on
      t.string :name
      t.integer :state
    end

    remove_column :evaluations, :iteration_every, :integer
    remove_column :evaluations, :on_hold, :integer
    remove_column :evaluations, :communicativeness_avg, :decimal
    remove_column :evaluations, :diligence_avg, :decimal
    remove_column :evaluations, :promptness_avg, :decimal
    remove_column :evaluations, :commitment_avg, :decimal
    remove_column :evaluations, :independence_avg, :decimal
  end
end
