class CreateInvoices < ActiveRecord::Migration[4.2]
  def change
    create_table :invoices do |t|
      t.string :title
      t.date :sell_date
      t.date :due_date
      t.date :invoice_date
      t.string :description
      t.references :revenue_account, index: true, foreign_key: true
      t.references :payment, index: true, foreign_key: true
      t.references :mpk_number, index: true, foreign_key: true
      t.string :receiver_name
      t.text :correspondence_address
      t.integer :state

      t.timestamps null: false
    end
  end
end
