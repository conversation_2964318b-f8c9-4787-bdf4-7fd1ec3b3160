class FakeUser
  def first_name
    @first_name ||= FFaker::Name.first_name
  end

  def last_name
    @last_name ||= FFaker::Name.last_name
  end

  def username
    @username ||= set_username
  end

  def email
    @email ||= FFaker::Internet.email(first_name)
  end

  def pwd
    @pwd ||= Devise.friendly_token + 'A1'
  end

  private

  def set_username
    username = first_name.chr.downcase + last_name.gsub(/[^0-9a-z ]/i, '').downcase
    if User.find_by(username: username)
      "#{username}#{Random.rand(0..100)}"
    else
      username
    end
  end
end

# dla projektow
Company.create!(name: 'Artegence', domain: 'artegence.com')
company = Company.create!(name: 'Efigence', domain: 'efigence.com')
Company.create!(name: 'Filmweb', domain: 'filmweb.pl')
Company.create!(name: 'Kontomierz', domain: 'kontomierz.pl')

group = Group.create!(name: 'Developers')

# ------------------------------------------------------------------------------
GlobalRole.create(name: 'Global User')
GlobalRole.create(name: 'Global Admin Programmer')
GlobalRole.create(name: 'Global Uber Project Manager')
GlobalRole.create(name: 'Global Project Manager')
GlobalRole.create(name: 'Global HR Manager')
GlobalRole.create(name: 'Global Accounting')
GlobalRole.create(name: 'Global Analyst')
GlobalRole.create(name: 'Global Client')
GlobalRole.create(name: 'Global System Administrator')
GlobalRole.create(name: 'Global Agreements Admin')
GlobalRole.create(name: 'Global Traffic', holiday_requests_notifications: true)
GlobalRole.create(name: 'Global Asset Manager')
GlobalRole.create(name: 'Global Coordinator')
GlobalRole.create(name: 'Global Controlling Reader')
GlobalRole.create(name: 'Global Project Coordinator')

global_role_global_user = GlobalRole.where(name: 'Global User').first
global_role_global_user.activities = Activity.default
global_role_global_user.save!

global_role_global_admin_and_imperator_programmer = GlobalRole.where(name: 'Global Admin Programmer').first
global_role_global_admin_and_imperator_programmer.activities = Activity.programmer
global_role_global_admin_and_imperator_programmer.global_admin = true
global_role_global_admin_and_imperator_programmer.save!

global_role_global_uber_project_manager = GlobalRole.where(name: 'Global Uber Project Manager').first
global_role_global_uber_project_manager.activities = Activity.uber_project_manager
global_role_global_uber_project_manager.save!

global_role_global_project_manager = GlobalRole.where(name: 'Global Project Manager').first
global_role_global_project_manager.activities = Activity.project_manager
global_role_global_project_manager.save!

global_role_global_hr_manager = GlobalRole.where(name: 'Global HR Manager').first
global_role_global_hr_manager.activities = Activity.hr_manager
global_role_global_hr_manager.save!

global_role_global_accounting = GlobalRole.where(name: 'Global Accounting').first
global_role_global_accounting.activities = Activity.accounting
global_role_global_accounting.save!

global_role_global_client = GlobalRole.where(name: 'Global Client').first
global_role_global_client.activities = Activity.client
global_role_global_client.save!

# helpdesk
global_role_global_admin = GlobalRole.where(name: 'Global System Administrator').first
global_role_global_admin.activities = Activity.admin
global_role_global_admin.global_admin = true
global_role_global_admin.save!
#---

global_role_global_admin = GlobalRole.where(name: 'Global Agreements Admin').first
global_role_global_admin.activities = Activity.agreements_admin
global_role_global_admin.save!

global_role_global_traffic = GlobalRole.where(name: 'Global Traffic').first
global_role_global_traffic.activities = Activity.global_traffic
global_role_global_traffic.save!

global_asset_manager = GlobalRole.where(name: 'Global Asset Manager').first
global_asset_manager.activities = Activity.asset_manager
global_asset_manager.save!

global_coordinator = GlobalRole.find_by(name: 'Global Coordinator')
global_coordinator.activities = Activity.coordinator
global_coordinator.save!

global_controlling_reader = GlobalRole.find_by(name: 'Global Controlling Reader')
global_controlling_reader.activities = Activity.controlling_reader
global_controlling_reader.save!

global_project_coordinator = GlobalRole.find_by(name: 'Global Project Coordinator')
global_project_coordinator.activities = Activity.project_coordinator
global_project_coordinator.save!

# role takie jak w RM; na produkcji tego fragmentu nie bedziemy seedowac, bo role zmigruja sie z RM bezposrednio:
# rubocop:disable Layout/LineLength
Role.create(name: 'Developer', permissions: [])
Role.create(name: 'Frontend Developer', permissions: [])
Role.create(name: 'Application architect', permissions: [])
Role.create(name: 'Project Manager', permissions: %i[add_subprojects edit_project close_project manage_members])
Role.create(name: 'Account Manager', permissions: %i[add_subprojects edit_project close_project manage_members])
Role.create(name: 'Tester', permissions: [])
Role.create(name: 'Client', permissions: [])
Role.create(name: 'Vice-Architect', permissions: [])
Role.create(name: 'Observer', permissions: [])
Role.create(name: 'Flash Developer', permissions: [])
Role.create(name: 'AI', permissions: [])
Role.create(name: 'Creation', permissions: [])
Role.create(name: 'SEM/SEO', permissions: [])
Role.create(name: 'Admin - NOC', permissions: %i[edit_project close_project manage_members]) # :add_project # (nasi admini)
Role.create(name: 'Accounting', permissions: %i[edit_project close_project manage_members]) # :add_project
Role.create(name: 'Analyst', permissions: [:close_project])
Role.create(name: 'BacklogViewer', permissions: [])
Role.create(name: 'Client - Observer', permissions: [])
Role.create(name: 'Uber Project Manager', permissions: %i[add_subprojects edit_project close_project manage_members]) # :add_project
Role.create(name: 'Client - Developer', permissions: [])
Role.create(name: 'Client - Documents', permissions: [])
Role.create(name: 'Client - Alior - Observer', permissions: [])
Role.create(name: 'Legal', permissions: [])
Role.create(name: 'Wiki Editor', permissions: [])
Role.create(name: 'Client - Owncloud', permissions: [])
# rubocop:enable Layout/LineLength
#
# MAPOWANIE W MIGRACJI Z REDMINE
# global_role_name = case name
#   when 'Project Manager' then return 'Global Project Manager'
#   when 'Admin - NOC' then return 'Global System Administrator'
#   when 'Accounting' then return 'Global Accounting'
#   when 'Uber Project Manager' then return 'Global Uber Project Manager'
#   when 'Analyst' then return 'Global Analyst'
#   when 'Client' then return 'Global Client'
#   when 'Client - Observer' then return 'Global Client'
#   when 'Client - Developer' then return 'Global Client'
#   when 'Client - Documents' then return 'Global Client'
#   when 'Client - Alior - Observer' then return 'Global Client'
#   when 'Client - Owncloud' then return 'Global Client'
#   when 'Observer' then return 'Global Client'
#   else
#     'Global User'
#   end

# na razie tyle, memberships i membership_roles wiaza role, grupy i uzytkownikow z projektem
# jesli juz bedziesz mial projekt, chcac dodac grupe do projektu robisz Membership.create(member: group, project: project)
# analogicznie chcac dodac uzytkownika: Membership.create(member: user, project: project)
# chcac dodac role do czlonkostwa w projekcie: membership.roles << role

if User.count.zero? || ENV['FORCE'].to_s == 'true'
  user = User.new do |u|
    u.first_name            = 'Marcin'
    u.last_name             = 'Kalita'
    u.username              = 'mkalita'
    u.email                 = '<EMAIL>'
    u.password              = '1234Aaaa'
    u.password_confirmation = u.password
    u.company_id            = Company.first.id
    u.global_roles          = [global_role_global_admin]
    u.activates_on          = Time.zone.today
    u.save!
    u.confirm
  end
  user.groups << group # dodaje uzytkownika do grupy, tworze GroupMembership

  99.times do
    fake_user = FakeUser.new
    User.new do |u|
      u.first_name            = fake_user.first_name
      u.last_name             = fake_user.last_name
      u.username              = fake_user.username
      u.email                 = fake_user.email
      u.password              = '3Fw' + fake_user.pwd
      u.password_confirmation = u.password
      u.global_roles          = [global_role_global_user]
      u.activates_on          = Time.zone.today
      u.save!
      u.confirm
    end
  end
end

user_mkalita = User.where(email: '<EMAIL>').first!
accounting_number = AccountingNumber.create!(company_id: company.id, user_id: user_mkalita.id, description: 'description')
Project.create!(name: 'Zonk', identifier: 'zonk', account_number: '*********',
                company_id: company.id, accounting_number_id: accounting_number.id)
project_zonk = Project.where(identifier: 'zonk').first!
role = Role.find_by(name: 'Developer')
Membership.create!(project: project_zonk, member: user_mkalita, roles: [role])

department1 = Department.create(
  chief_id: User.first.id,
  company_id: Company.first.id,
  name: 'Demo Department 1'
)
department2 = Department.create(
  chief_id: User.last.id,
  company_id: Company.last.id,
  name: 'Demo Department 2'
)
User.find_in_batches(batch_size: 100).with_index do |batch, _index|
  sleep(0.1) # Make sure it doesn't get too crowded in there!
  batch.each do |usr|
    if usr.id.to_i.even?
      if (usr.id.to_i % 4).zero?
        # jeden
        usr.department = [department1, department2].sample
        usr.save
      end
    else
      # dwa
      usr.department = department1
      usr.save
    end
  end
end
if ENV['SEED_HOLIDAYS'].to_s == 'true'
  Absence.delete_all
  HolidayRequest.delete_all
  User.find_in_batches(batch_size: 100).with_index do |batch, _index|
    sleep(0.1) # Make sure it doesn't get too crowded in there!
    batch.each do |usr|
      # tacy co nie maja
      next if usr.id.to_i.even?

      proc_object = proc do
        # rozne lata
        [2016, 2017].each do |year|
          # poczatek mies./roku
          date = Time.zone.parse("#{year}-02-#{usr.id.to_i.even? ? '02' : '03'}").to_date
          holiday_request = HolidayRequest.create(created_at: Time.zone.now - Random.rand((1..365)).days, applicant_id: usr.id,
                                                  starts_on: date, ends_on: date + Random.rand((7..14)).days)
          raise holiday_request.errors.inspect unless holiday_request.persisted?

          # koniec mies./roku
          date = Time.zone.parse("#{year}-12-24").to_date
          holiday_request = HolidayRequest.create(created_at: Time.zone.now - Random.rand((1..365)).days, applicant_id: usr.id,
                                                  starts_on: date, ends_on: date + Random.rand((7..14)).days)
          raise holiday_request.errors.inspect unless holiday_request.persisted?
        end
        # stykajace sie
        date = Time.zone.parse('2016-10-01').to_date
        holiday_request = HolidayRequest.create(created_at: Time.zone.now - Random.rand((1..365)).days, applicant_id: usr.id,
                                                starts_on: date, ends_on: date + 1.day)
        raise holiday_request.errors.inspect unless holiday_request.persisted?

        date = Time.zone.parse('2016-10-03').to_date
        holiday_request = HolidayRequest.create(created_at: Time.zone.now - Random.rand((1..365)).days, applicant_id: usr.id,
                                                starts_on: date, ends_on: date + Random.rand((7..14)).days)
        raise holiday_request.errors.inspect unless holiday_request.persisted?
      end
      HolidayRequest.with_background_userstamping(usr, proc_object)
    end
  end
  HolidayRequest.where('id < ?',
                       (HolidayRequest.maximum('id') || 0) / 2)
                .find_in_batches(batch_size: 100)
                .with_index do |batch, _index|
    sleep(0.1) # Make sure it doesn't get too crowded in there!
    first_user = User.first
    last_user = User.last
    batch.each do |hr|
      stamper = [first_user, last_user].sample
      examiner = [stamper, nil].sample
      proc_object = proc do
        if hr.id.to_i.even?
          # niektore zaakceptowane
          hr.accept_by!(examiner)
        else
          # niektore odrzucone
          hr.reject_by!(examiner)
        end
      end
      HolidayRequest.with_background_userstamping(stamper, proc_object)
    end
  end
end

# Accounting module seed: Revenue accounts:
params = {
  '702-01' => 'Budowa serwisu',
  '702-02' => 'Kampania kreacja i produkcja',
  '702-03' => 'Hosting i usługi data center',
  '702-04' => 'Aktualizacja, abonament Technologia',
  '702-05' => 'Aktualizacja, abonament Marketing',
  '702-06' => 'Produkcja wideo',
  '702-07' => 'Licencje/prawa autorskie',
  '702-08' => 'Red Alarms',
  '702-09' => 'Other',
  '702-10' => 'Badania',
  '702-14' => 'Outsourcing specjalistów',
  '702-15' => 'Konsulting/doradztwo/szkolenia',
  '702-20' => 'Usługi księgowe, kadrowe, płacowe, biurowo-recepcyjne',
  '702-21' => 'Usługi kolokacja, obsł. sieci, help desk',
  '760-09-PP-1-2' => 'Refaktury',
  '703-02' => 'Pozostałe usługi'
}

params.each do |key, name|
  RevenueAccount.create!(key: key, name: name)
end

# MPK numbers:

params = {
  '119' => 'OTHER',
  '120' => 'Motion Design',
  '121' => 'GUI',
  '122' => 'UI/UX i Badania',
  '123' => 'Data Center',
  '124' => 'Kreacja',
  '125' => 'Technologia',
  '126' => 'Marketing',
  '127' => 'Analitycy',
  '128' => 'PM',
  '129' => 'G&A'
}

params.each do |key, name|
  MpkNumber.create!(key: key, name: name)
end

# accounting-related columns on existing tables:

Company.find_by(name: 'Artegence').update(accounting: true)
Company.find_by(name: 'Efigence').update(accounting: true)
Role.find_by(name: 'Project Manager').update(pm: true)
Role.find_by(name: 'Uber Project Manager').update(pm: true)
GlobalRole.find_by(name: 'Global Project Manager').update(pm: true)
GlobalRole.find_by(name: 'Global Uber Project Manager').update(pm: true)

Agreement.create! do |a|
  a.name = 'Test agreement'
  a.content = 'Some agreement text'
  a.confirmation_button_text = 'Agree'
  a.departments = [department1]
  a.companies = [Company.find_by(name: 'Artegence')]
  a.business_to_business = true
end
