WITH currency_map AS (
  SELECT 0 AS currency_id, 'PLN' AS currency
  UNION ALL
  SELECT 1 AS currency_id, 'EUR' AS currency
  UNION ALL
  SELECT 2 AS currency_id, 'USD' AS currency
  UNION ALL
  SELECT 3 AS currency_id, 'GBP' AS currency
  UNION ALL
  SELECT 4 AS currency_id, 'CHF' AS currency
)

SELECT accounting_numbers.number AS project_no, mpk_numbers.key AS mpk_number,
  companies.name AS company, external_costs.cost_date AS date,
  NULL as due_date, NULL AS invoice_number,
  contractors.name AS contractor,
  external_costs.amount / 100.0 AS amount, 'PLN' AS currency,
  'External' AS cost_type
FROM external_costs
INNER JOIN projects ON external_costs.project_id = projects.id
INNER JOIN accounting_numbers ON projects.accounting_number_id = accounting_numbers.id
INNER JOIN mpk_numbers ON external_costs.mpk_number_id = mpk_numbers.id
INNER JOIN companies ON projects.company_id = companies.id
INNER JOIN contractors ON external_costs.contractor_id = contractors.id
