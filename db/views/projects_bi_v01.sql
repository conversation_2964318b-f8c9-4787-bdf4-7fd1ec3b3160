SELECT accounting_numbers.number AS project_no, projects.name AS project, companies.name AS company,
  clients.name AS client,
  case when accounting_numbers.overhead = 1 then 'tak' else 'nie' end as overhead,
  CONCAT(users.first_name, ' ', users.last_name) as business_owner
FROM accounting_numbers
INNER JOIN projects ON accounting_numbers.id = projects.accounting_number_id
INNER JOIN payment_schedules ON projects.id = payment_schedules.project_id
INNER JOIN companies ON projects.company_id = companies.id
INNER JOIN clients ON projects.client_id = clients.id
LEFT JOIN memberships ON projects.id = memberships.project_id
LEFT JOIN membership_roles ON membership_roles.membership_id = memberships.id
LEFT JOIN roles ON membership_roles.role_id = roles.id
LEFT JOIN users ON memberships.member_id = users.id AND memberships.member_type = 'User'
WHERE roles.name = 'Uber Project Manager' AND users.state = 0
GROUP BY accounting_numbers.number
