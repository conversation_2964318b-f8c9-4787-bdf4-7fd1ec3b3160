# Read about fixtures at http://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  date: 2016-07-22
  user: mkalita_user
  category: 3
  holiday_request: one

two:
  date: 2016-07-21
  user: mkalita_user
  category: 3
  holiday_request: one

three:
  date: 2016-07-20
  user: mkalita_user
  category: 3
  holiday_request: one

mkalita_absence:
  date: 2016-01-01
  user: mkalita_user
  category: 3
  holiday_request: one

mkalita_absence_alternative:
  date: 2016-01-02
  user: mkalita_user
  category: 3
  holiday_request: one

many_days2017-01-23:
  date: 2017-01-23
  user: mkalita_user
  category: 0
  holiday_request: many_days

many_days2017-01-24:
  date: 2017-01-24
  user: mkalita_user
  category: 0
  holiday_request: many_days

many_days2017-01-25:
  date: 2017-01-25
  user: mkalita_user
  category: 0
  holiday_request: many_days
