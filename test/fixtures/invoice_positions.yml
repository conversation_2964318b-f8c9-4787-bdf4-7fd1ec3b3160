# Read about fixtures at
# http://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

payment_three:
  invoice: payment_three
  name: Product
  amount: 1
  unit_price: 1
  tax_rate: 3
  jpk_gtu: 'GTU_11'
  jpk_transaction_code: 'SW'

payment_three_amendment_draft_position:
  invoice: payment_three_amendment_draft
  name: Product
  amount: 1
  unit_price: 1
  tax_rate: 3
  jpk_gtu: 'GTU_11'
  jpk_transaction_code: 'SW'

payment_four_position:
  invoice: payment_four
  name: Product
  amount: 1
  unit_price: 0.5
  tax_rate: 3

payment_four_amendment_position:
  invoice: payment_four_amendment
  name: Product
  amount: 1
  unit_price: 1
  tax_rate: 3

payment_four_amendment_position:
  invoice: payment_four_amendment
  name: Product
  amount: 1
  unit_price: 1
  tax_rate: 3

payment_five_draft_position:
  invoice: payment_five_draft
  name: Product
  amount: 1
  unit_price: 1
  tax_rate: 3

payment_eight_position:
  invoice: payment_eight
  name: Product
  amount: 1
  unit_price: 1
  tax_rate: 3

payment_ten_position:
  invoice: payment_ten
  name: Product
  amount: 1
  unit_price: 1
  tax_rate: 3

payment_ten_amendment_position:
  invoice: payment_ten_amendment
  name: Product
  amount: 10
  unit_price: 1
  tax_rate: 3

project_five_payment_invoice_position:
  invoice: project_five_payment_invoice
  name: Product
  amount: 5
  unit_price: 1
  tax_rate: 3

project_five_second_payment_invoice_position:
  invoice: project_five_second_payment_invoice
  name: Product
  amount: 5
  unit_price: 1
  tax_rate: 3

project_five_second_payment_invoice_amendment_position:
  invoice: project_five_second_payment_invoice_amendment
  name: Product
  amount: 5
  unit_price: 1
  tax_rate: 3
