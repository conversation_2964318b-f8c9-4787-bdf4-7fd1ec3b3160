# Read about fixtures at
# http://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

DEFAULTS: &DEFAULTS
  activities: <%= Activity.default %>

one:
  name: 'Global HR'

global_admin_programmer:
  <<: *DEFAULTS
  name: 'Global Admin Programmer'
  global_admin: true
  activities: <%= Activity.programmer %>
  processed_invoices_notification: true

global_admin:
  <<: *DEFAULTS
  name: 'Global System Administrator'
  global_admin: true
  activities: <%= Activity.admin %>
  processed_invoices_notification: true

global_admin_without_activities:
  name: 'Global Admin without activities'
  activities:
  global_admin: true

global_user:
  <<: *DEFAULTS
  name: 'Global User'

global_hr_manager:
  <<: *DEFAULTS
  name: 'Global HR Manager'
  activities: <%= Activity.hr_manager %>

global_hr_coordinator:
  <<: *DEFAULTS
  name: 'Global HR Coordinator'
  activities: <%= Activity.hr_coordinator %>
  remote_work_periods_notifications: true

global_accounting:
  <<: *DEFAULTS
  name: 'Global Accounting'
  activities: <%= Activity.accounting %>
  notify_dms_controller_acceptances: true
  processed_invoices_notification: true

global_client:
  name: 'Global Client'
  activities: <%= Activity.client %>

global_pm:
  name: 'Global Project Manager'
  activities: <%= Activity.project_manager %>
  pm: true

global_user_without_activities:
  name: 'Global User without activities'
  activities:

global_role_without_activities_and_associations:
  name: 'Global role without associations'
  activities:

global_role_agreements_admin:
  name: 'Global Agreements Admin'
  activities: <%= Activity.agreements_admin %>

global_role_global_traffic:
  name: 'Global Traffic'
  activities: <%= Activity.global_traffic %>

global_asset_manager:
  name: Global Asset Manager
  activities: <%= Activity.asset_manager %>

global_coordinator:
  name: Global Coordinator
  activities: <%= Activity.coordinator %>

global_general_flow:
  name: Global User with DMS general flow enabled
  activities: <%= Activity.general_flow %>

global_all_flows:
  name: Global User with DMS all flows enabled
  activities: <%= Activity.all_flows %>
