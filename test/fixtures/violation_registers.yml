# Read about fixtures at http://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  description: "Safety violation in warehouse area"
  violation_type: "Safety protocol breach"
  violation_area: "Warehouse"
  taken_actions: "Employee training scheduled"
  violation_level: administrator
  signature: "<PERSON>"
  created_by: wiktoria
  company: one
  violation_occurrence_date: <%= Date.current - 1.day %>
  violation_entry_date: <%= Date.current %>
  mandatory_report: false
  state: registered

two:
  description: "Data processing violation"
  violation_type: "GDPR compliance issue"
  violation_area: "IT Department"
  taken_actions: "System access revoked, audit initiated"
  violation_level: processor
  signature: "<PERSON>"
  created_by: milosz
  company: two
  violation_occurrence_date: <%= Date.current - 2.days %>
  violation_entry_date: <%= Date.current - 1.day %>
  mandatory_report: true
  report_description: "Detailed report of GDPR violation and corrective measures"
  state: processing

three:
  description: "Security incident"
  violation_type: "Unauthorized access attempt"
  violation_area: "Server room"
  taken_actions: "Access logs reviewed, security enhanced"
  violation_level: administrator
  signature: "Security Officer"
  created_by: wiktoria
  company: one
  violation_occurrence_date: <%= Date.current - 3.days %>
  violation_entry_date: <%= Date.current - 2.days %>
  mandatory_report: true
  report_description: "Security breach report with timeline and response"
  state: reported

four:
  description: "Completed violation case"
  violation_type: "Process violation"
  violation_area: "Production"
  taken_actions: "Process updated, staff retrained"
  violation_level: processor
  signature: "Production Manager"
  created_by: milosz
  company: two
  violation_occurrence_date: <%= Date.current - 10.days %>
  violation_entry_date: <%= Date.current - 9.days %>
  mandatory_report: false
  state: completed
