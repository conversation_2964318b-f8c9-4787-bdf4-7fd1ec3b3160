DEFAULTS: &DEFAULTS
  provider: email
  confirmed_at: <%= Time.now %>
  state: 0
  encrypted_password: <%= BCrypt::Password.create('DefaultPassword*235%#') %>
  password_changed_at: <%= Time.now - 1.minutes %>
  activates_on: <%= Time.zone.today %>

mkalita_global_admin_programmer:
  <<: *DEFAULTS
  username: $LABEL
  first_name: <PERSON><PERSON>
  last_name: <PERSON><PERSON>
  email: $<EMAIL>
  uid: $<EMAIL>
  company: mkalita_company

capybara_user:
  <<: *DEFAULTS
  username: $LABEL
  first_name: Capybara
  last_name: User
  email: $<EMAIL>
  uid: $<EMAIL>
  contract_of_employment: false

capybara_global_admin_user:
  <<: *DEFAULTS
  username: $LABEL
  first_name: Capybara
  last_name: Admin
  email: $<EMAIL>
  uid: $<EMAIL>
  contract_of_employment: false

mkalita_user:
  <<: *DEFAULTS
  username: $LABEL
  first_name: Marcin
  last_name: Kali<PERSON>
  email: $<EMAIL>
  uid: $<EMAIL>
  company: mkalita_company

mkalita_user_alternative:
  <<: *DEFAULTS
  username: $LABEL
  first_name: Mar
  last_name: Kal
  email: $<EMAIL>
  uid: $<EMAIL>
  company: mkalita_company
  absence_balance: 20

unconfirmed_user:
  username: 'unconfirmed'
  first_name: 'Foo'
  last_name: 'Bar'
  email: '<EMAIL>'
  activates_on: <%= Time.zone.today %>

mrlocked:
  username: mrlocked
  first_name: John
  last_name: Locked
  email: <EMAIL>
  uid: <EMAIL>
  provider: email
  confirmed_at: <%= Time.now %>
  state: 1
  activates_on: <%= Time.zone.today %>

internal_user:
  <<: *DEFAULTS
  username: $LABEL
  first_name: Int
  last_name: Usr
  email: $<EMAIL>
  uid: $<EMAIL>
  company: mkalita_company

internal_user_alternative:
  <<: *DEFAULTS
  username: $LABEL
  first_name: Int
  last_name: Usr
  email: $<EMAIL>
  uid: $<EMAIL>
  company: mkalita_company

external_user:
  <<: *DEFAULTS
  username: $LABEL
  first_name: Ext
  last_name: Usr
  email: $<EMAIL>
  uid: $<EMAIL>
  redmine_id: 999

external_user_alternative:
  <<: *DEFAULTS
  username: $LABEL
  first_name: Ext
  last_name: Usr
  email: $<EMAIL>
  uid: $<EMAIL>

milosz:
  username: mgrabowski
  first_name: Miłosz
  last_name: Grabowski
  email: <EMAIL>
  encrypted_password: <%= BCrypt::Password.create('qwer1234') %>
  uid: <EMAIL>
  provider: email
  confirmed_at: <%= Time.now %>
  company: one
  department: two
  absence_quota: 26
  contract_of_employment: true
  password_changed_at: <%= Time.now %>
  activates_on: <%= Time.zone.today %>
  remote_allowed: true
  remote_yearly_limit: 10

mikolaj:
  <<: *DEFAULTS
  username: mromanow
  first_name: Mikołaj
  last_name: Romanow
  email: <EMAIL>
  redmine_id: 1
  uid: <EMAIL>
  provider: email
  confirmed_at: <%= Time.now %>
  redmine: false
  created_at: <%= Time.parse('2016-05-02 10:00') %>
  department: two
  company: one
  activates_on: <%= Date.new(2016, 5, 3) %>
  click_up_id: **********

wiktoria:
  company: one
  username: whanowerska
  first_name: Wiktoria
  last_name: Hanowerska
  email: <EMAIL>
  redmine_id: 1155
  uid: <EMAIL>
  provider: email
  confirmed_at: <%= Time.now %>
  created_at: <%= Time.parse('2016-05-03 10:00') %>
  last_sign_in_at: <%= Time.now %>
  password_changed_at: <%= Time.now + 1.week - Devise.expire_password_after %>
  uid_number: 20161800
  activates_on: <%= Date.new(2016, 5, 4) %>
  card_holder: true
  department: one
  position: head_of_client_service
  click_up_id: **********

hr_user:
  <<: *DEFAULTS
  username: $LABEL
  first_name: Hr
  last_name: Usr
  email: $<EMAIL>
  uid: $<EMAIL>
  company: mkalita_company

board_member_user:
  <<: *DEFAULTS
  username: board_user
  first_name: John
  last_name: Doe
  email: <EMAIL>
  uid: <EMAIL>
  company: mkalita_company
  time_reports_not_required: true
  department: board_member_department

wilhelm:
  <<: *DEFAULTS
  username: whohenzollern
  first_name: Wilhelm
  last_name: Hohenzollern
  email: <EMAIL>
  uid: <EMAIL>
  provider: email
  confirmed_at: <%= Time.now %>
  redmine: false
  created_at: <%= Time.parse('2016-05-02') %>
  department: three

<% 6.times do |i| %>
mkalita_user_<%= i %>:
  <<: *DEFAULTS
  username: $LABEL
  first_name: Marcin
  last_name: Kalita
  email: <%= "mkalita+#{i}@artegence.com" %>
  uid: <%= "mkalita+#{i}@artegence.com" %>
<% end %>
