# Read about fixtures at
# http://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

DEFAULTS: &DEFAULTS
  project: one
  entrustment_agreement: Test entrustment agreement
  processing_categories: Test processing categories
  security_measures_description: Test security measures description
  admin_name: Test admin name
  admin_contact_details: Test admin contact details
  processing_time: Test processing time
  third_country_or_intl_org_recipients: Test third country recipients
  creation_date: <%= Date.current.to_s %>
  state: 0

one:
  <<: *DEFAULTS
  created_by: wiktoria

two:
  <<: *DEFAULTS
  created_by: mikolaj
  co_admin_details: Test co-admin details
  representative_admin_details: Test representative admin details
  admin_data_protection_officer: Test data protection officer
  security_documentation: Test security documentation
  subcontractor_details: Test subcontractor details
  sub_processing_categories: Test sub-processing categories
  expiration_date: <%= (Date.current + 1.year).to_s %>

closed:
  <<: *DEFAULTS
  created_by: wiktoria
  state: 1

project_two_registry:
  <<: *DEFAULTS
  project: two
  created_by: milosz
  admin_name: Project Two Admin
  admin_contact_details: <EMAIL>
  processing_time: 6 months
  creation_date: <%= (Date.current - 3.months).to_s %>

expired:
  <<: *DEFAULTS
  project: three
  created_by: mikolaj
  admin_name: Expired Registry Admin
  expiration_date: <%= (Date.current - 1.month).to_s %>
  processing_time: 12 months
  security_measures_description: Enhanced security measures with encryption

detailed:
  <<: *DEFAULTS
  project: four
  created_by: wiktoria
  entrustment_agreement: |
    This detailed entrustment agreement covers multiple aspects of data processing,
    including but not limited to personal data, financial data, and sensitive information.
    The agreement is governed by GDPR regulations and includes specific clauses for
    data breach notification, data subject rights, and processor obligations.
  processing_categories: |
    - Personal identification data
    - Contact information
    - Financial data
    - Health information
    - Biometric data
    - Location data
    - Online identifiers
  security_measures_description: |
    1. Data encryption at rest and in transit
    2. Multi-factor authentication
    3. Regular security audits
    4. Access control based on least privilege principle
    5. Automated intrusion detection systems
    6. Regular backups with encryption
    7. Physical security measures for data centers
  admin_name: Security Compliance Team
  admin_contact_details: <EMAIL>, +1-************
  processing_time: 24 months with annual review

international:
  <<: *DEFAULTS
  project: capybara_project
  created_by: milosz
  admin_name: Global Data Protection Office
  admin_contact_details: <EMAIL>
  third_country_or_intl_org_recipients: |
    - United States (Privacy Shield certified entities)
    - Canada (adequate protection country)
    - Japan (adequacy decision)
    - Switzerland (adequate protection country)
    - International Organization for Standardization (ISO)
  security_documentation: Standard Contractual Clauses and Binding Corporate Rules
  creation_date: <%= (Date.current - 6.months).to_s %>
  expiration_date: <%= (Date.current + 2.years).to_s %>
