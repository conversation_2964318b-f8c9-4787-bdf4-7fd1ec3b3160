# Read about fixtures at
# http://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

DEFAULTS: &DEFAULTS
  starts_on: <%= Time.parse('2017-05-29').to_date %>

milosz_evaluation:
  <<: *DEFAULTS
  user: milosz
  created_by: wiktoria
  name: milosz onboarding
  starts_on: <%= Date.yesterday %>
  main_survey: onboarding
  state: 2
  additional_survey: onboarding

milosz_pending_evaluation:
  <<: *DEFAULTS
  user: milosz
  created_by: wiktoria
  starts_on: <%= Date.tomorrow %>
  name: milosz evaluation
  main_survey: onboarding
  state: 0

mikolaj_evaluation:
  <<: *DEFAULTS
  user: mikolaj
  created_by: wiktoria
  name: mikolaj evaluation
  starts_on: <%= Date.tomorrow %>
  main_survey: onboarding
  state: 0
