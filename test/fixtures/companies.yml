# Read about fixtures at http://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

DEFAULTS: &DEFAULTS
  name: $LABEL
  domain: domain.com
  nip: MyString
  address1: MyString
  address2: MyString
  city: MyString
  zipcode: MyString
  phone: MyString

one:
  <<: *DEFAULTS
  name: Artegence
  domain: artegence.com
  accounting: true
  holiday_project_id: 796
  redmine_holiday_issues_ids:
    Niedostępność: 169527
  show_onboarding: true
  click_up_synchronization: true
  redmine_synchronization: false
  click_up_workspace_id: ************

two:
  <<: *DEFAULTS
  name: Efigence
  domain: efigence.com
  accounting: true
  show_onboarding: true
  click_up_synchronization: false
  redmine_synchronization: true

three:
  <<: *DEFAULTS
  name: Kontomierz
  domain: kontomierz.com
  accounting: true
  show_onboarding: false
  click_up_synchronization: false
  redmine_synchronization: true

filmweb:
  <<: *DEFAULTS
  name: Filmweb
  domain: filmweb.pl
  accounting: false
  show_onboarding: false
  click_up_synchronization: false
  redmine_synchronization: true

agreement_company:
  <<: *DEFAULTS
  name: Agreement Company
  domain: agreement.pl
  accounting: false
  click_up_synchronization: false
  redmine_synchronization: true

mkalita_company:
  <<: *DEFAULTS
