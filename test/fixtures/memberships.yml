# Read about fixtures at http://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

DEFAULTS: &DEFAULTS
  project: mkalita_project

one:
  member: mi<PERSON><PERSON> (User)
  project: one

two:
  member: mi<PERSON><PERSON><PERSON> (User)
  project: one

three:
  member: one (Group)
  project: one

four:
  member: two (Group)
  project: one

four_inherited_wiktoria:
  member: wik<PERSON> (User)
  project: one

five:
  member: two (Group)
  project: three

six:
  member: two (Group)
  project: two

seven:
  member: three (Group)
  project: three

eight:
  member: miko<PERSON><PERSON> (User)
  project: four

nine:
  member: mrlocked (User)
  project: four

ten:
  member: four (Group)
  project: four

mkalita_membership_group:
  <<: *DEFAULTS
  member: mkalita_group (Group)

mkalita_membership_user:
  <<: *DEFAULTS
  member: mkalita_user (User)

capybara_user_membership_user:
  <<: *DEFAULTS
  member: capybara_user (User)
  project: capybara_project
