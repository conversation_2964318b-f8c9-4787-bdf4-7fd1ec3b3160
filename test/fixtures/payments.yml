# Read about fixtures at
# http://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  payment_schedule: project_one_schedule
  issued_on: <%= Date.today - 2.months %>
  sell_date: <%= Date.today - 2.months %>
  predicted_amount: 500
  description: MyString
  currency: 0

project_one_today:
  payment_schedule: project_one_schedule
  issued_on: <%= Date.today %>
  sell_date: <%= Date.today %>
  predicted_amount: 1000
  description: Project one today payment
  currency: 0

two:
  payment_schedule: project_one_schedule
  issued_on: <%= Date.today + 2.month %>
  sell_date: <%= Date.today + 2.month %>
  predicted_amount: 500
  description: MyString
  currency: 0

three:
  payment_schedule: project_two_schedule
  issued_on: <%= Date.today - 1.month %>
  sell_date: <%= Date.today - 1.month %>
  predicted_amount: 100
  description: Example description
  currency: 0

four:
  payment_schedule: project_two_schedule
  issued_on: <%= Date.today %>
  sell_date: <%= Date.today %>
  predicted_amount: 500
  description: Example description
  currency: 0

five:
  payment_schedule: project_two_schedule
  issued_on: <%= Date.today + 1.month %>
  sell_date: <%= Date.today + 1.month %>
  predicted_amount: 100
  description: Fifth payment
  currency: 0

six:
  payment_schedule: project_two_schedule
  issued_on: <%= Date.today + 2.month %>
  sell_date: <%= Date.today + 2.month %>
  predicted_amount: 500
  description: Payment without invoice on purpose
  currency: 0

seven_cyclic_payment:
  payment_schedule: project_three_schedule
  issued_on: <%= (1.month.ago - 2.weeks).to_date %>
  sell_date: <%= (1.month.ago - 2.weeks).to_date %>
  predicted_amount: 500
  cyclic: true
  cycle_length: 1
  currency: 0

eight_cyclic_payment:
  payment_schedule: project_three_schedule
  issued_on: <%= 2.weeks.ago.to_date %>
  sell_date: <%= 2.weeks.ago.to_date %>
  predicted_amount: 500
  cyclic: true
  originator: seven_cyclic_payment
  currency: 0
  cycle_length: 1

nine_cyclic_payment:
  payment_schedule: project_three_schedule
  issued_on: <%= Time.zone.today %>
  sell_date: <%= Time.zone.today %>
  predicted_amount: 1000
  cyclic: true
  cycle_length: 2
  currency: 0

tenth_cyclic_payment:
  payment_schedule: project_three_schedule
  issued_on: <%= (1.month.ago - 1.week).to_date %>
  sell_date: <%= (1.month.ago - 1.week).to_date %>
  predicted_amount: 1000
  cyclic: true
  cycle_length: 1
  currency: 0

tenth_cyclic_child_payment:
  payment_schedule: project_three_schedule
  issued_on: <%= (1.month.ago - 1.week).to_date %>
  sell_date: <%= (1.month.ago - 1.week).to_date %>
  predicted_amount: 1000
  cyclic: false
  originator: tenth_cyclic_payment
  currency: 0

ten:
  payment_schedule: project_four_schedule
  issued_on: <%= Date.today + 2.month %>
  sell_date: <%= Date.today + 2.month %>
  predicted_amount: 1000
  description: MyString
  currency: 0

project_five_payment:
  payment_schedule: project_five_schedule
  issued_on: <%= Date.today - 1.month %>
  sell_date: <%= Date.today - 1.month %>
  predicted_amount: 500
  description: MyString
  currency: 0

project_five_second_payment:
  payment_schedule: project_five_schedule
  issued_on: <%= Date.today %>
  sell_date: <%= Date.today %>
  predicted_amount: 500
  description: MyString
  currency: 0
