require 'test_helper'

class ArchiveProjectInteractorTest < ActiveSupport::TestCase
  let(:project) { projects(:two) }

  test 'RedmineProjectsWorker is run upon project archivization' do
    assert_difference('RedmineProjectsWorker.jobs.count', 1) do
      result = ArchiveProjectInteractor.call(project: project)
      assert result.success?
    end
    assert_equal ['archive', project.id], RedmineProjectsWorker.jobs.last['args']
  end

  test 'OwncloudWorker is run upon project archivization if project has a owncloud share' do
    project.update(owncloud: true)

    assert_difference -> { OwncloudWorker.jobs.count } do
      result = ArchiveProjectInteractor.call(project: project)
      assert result.success?
    end
    assert_equal ['update_directory_share', project.identifier, 1], OwncloudWorker.jobs.last['args']
  end

  test 'project and descendants get archivized' do
    project1 = projects(:mkalita_project)
    project2 = projects(:mkalita_project_child)

    ArchiveProjectInteractor.call(project: project1)

    assert_equal 'archived', project1.reload.status
    assert_equal 'archived', project2.reload.status
  end
end
