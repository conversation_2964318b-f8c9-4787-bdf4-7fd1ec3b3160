require 'test_helper'

module Doorkeeper
  class TokensControllerTest < ActionController::TestCase
    let(:user) { users(:wik<PERSON>) }
    let(:access_token) do
      Doorkeeper::AccessToken.create!(resource_owner_id: user.id, use_refresh_token: true)
    end

    test 'should create access token' do
      password = 'Default password 1215#'
      user.update!(password:, password_confirmation: password)
      credentials = {
        grant_type: 'password',
        username: user.username,
        password: password
      }

      assert_difference -> { Doorkeeper::AccessToken.count } do
        post :create, params: credentials
      end

      assert_response :success
      assert_equal 'Bearer', json_body['token_type']
    end

    test 'should create access token with refresh token' do
      access_token # create access token first
      assert_difference -> { Doorkeeper::AccessToken.count } do
        post :create, params: { grant_type: 'refresh_token',
                                refresh_token: access_token.refresh_token }
      end
    end

    test 'should revoke access token' do
      post :revoke, params: { token: access_token.token }

      assert access_token.reload.revoked_at.present?
    end
  end
end
