require 'test_helper'

class Api::V1::LogsControllerTest < ActionController::TestCase
  before do
    @user = users(:wik<PERSON>)
    @log_file_name = 'testowy.log'
    @log_content = "log line 1\nlog line 2\nlog line 3\nlog line 4\nlog line 5\nlog line 6\nlog line 7\nlog line 11\n"
    @log_file_path = Rails.root.join('log', @log_file_name)

    @controller.stubs(:current_user).returns(@user)
    Settings.stubs(:log_files_names).returns([@log_file_name])
    File.write(@log_file_path, @log_content)
    LogsPolicy.any_instance.stubs(:index?).returns(true)
    LogsPolicy.any_instance.stubs(:permitted_attributes).returns([:file_name, :page, :per_page, :search])
  end

  test 'returns_logs_when_file_exists' do
    get :index, format: :json, params: { logs: { file_name: @log_file_name } }
    assert_response :success
    assert json_body['logs'].present?
    assert_equal 1, json_body['page']
    assert_equal 8, json_body['total_records']
  end

  test 'returns_empty_array_when_file_does_not_exist' do
    get :index, format: :json, params: { logs: { file_name: 'nonexistent.log' } }
    assert_response :success
    assert_equal [], json_body['logs']
    assert_equal 1, json_body['page']
  end

  test 'returns_unprocessable_entity_when_file_name_is_blank' do
    get :index, format: :json, params: { logs: { file_name: '' } }
    assert_response :unprocessable_entity
  end

  test 'paginates_results_correctly' do
    get :index, format: :json, params: { logs: { file_name: @log_file_name, page: 1, per_page: 2 } }
    assert_equal 2, json_body['logs'].length
    assert_equal 1, json_body['page']
    assert_equal 8, json_body['total_records']
  end

  test 'paginates_results_correctly_when_page_is_too_high' do
    get :index, format: :json, params: { logs: { file_name: @log_file_name, page: 5, per_page: 3 } }
    assert_equal 2, json_body['logs'].length
    assert_equal 3, json_body['page']
    assert_equal 8, json_body['total_records']
  end

  test 'paginates_results_correctly_when_page_and_per_page_is_too_high' do
    get :index, format: :json, params: { logs: { file_name: @log_file_name, page: 4, per_page: 10 } }
    assert_equal 8, json_body['logs'].length
    assert_equal 1, json_body['page']
    assert_equal 8, json_body['total_records']
  end

  test 'filters_logs_by_search_term' do
    get :index, format: :json, params: { logs: { file_name: @log_file_name, search: 'line 1' } }
    assert_equal 2, json_body['logs'].length
    assert_includes json_body['logs'].first, 'line 1'
    assert_equal 2, json_body['total_records']
  end

  test 'test_returns_forbidden_status' do
    LogsPolicy.any_instance.stubs(:index?).returns(false)
    get :index, format: :json, params: { logs: { file_name: @log_file_name } }
    assert_response :forbidden
  end

  test 'returns_list_of_log_files' do
    get :tab_list, format: :json
    assert_response :success
    assert_equal [@log_file_name], json_body.map { |e| e['key'] }
    assert_equal ['Testowy'], json_body.map { |e| e['name'] }
  end

  test 'returns_forbidden_status_tab_list' do
    LogsPolicy.any_instance.stubs(:index?).returns(false)
    get :tab_list, format: :json
    assert_response :forbidden
  end
end
