require 'test_helper'

module Api
  module V1
    class ContractorsControllerTest < ActionController::TestCase
      setup do
        @user = users(:wik<PERSON>)
        authenticate(@user)
      end

      let(:valid_params) do
        {
          user_id: users(:milosz).id,
          name: '<PERSON>z<PERSON>',
          street: 'U<PERSON>',
          street_number: '8a',
          apartment: '25',
          additional_address: '',
          city: 'City',
          postcode: '02-252',
          country: 'Country',
          vat_number: '*********',
          post: 'City',
          voivodeship: 'mazowieckie',
          district: 'Warszawa',
          community: 'Mokotów',
          account_number: '****************************'
        }
      end

      let(:contractor) { contractors(:wiktoria_contractor) }

      test 'contractor is created with valid params' do
        assert_difference('Contractor.count') do
          post :create, params: { contractor: valid_params }, format: :json
        end

        assert_response :created
      end

      test 'validation fails properly on create' do
        post :create, params: { contractor: valid_params.merge(name: '') }, format: :json

        assert_response :unprocessable_entity
        assert_not_empty json_body['errors']
      end

      test 'contractor is updated properly' do
        patch :update, params: { id: contractor.id, contractor: { name: '<PERSON>a nazwa' } },
                       format: :json

        assert_response :no_content
        assert_equal 'Inna nazwa', contractor.reload.name
      end

      test 'validation fails properly on update' do
        patch :update, params: { id: contractor.id, contractor: { name: '' } }, format: :json

        assert_response :unprocessable_entity
      end

      test 'index works properly showing only relevant data' do
        get :index, format: :json
        assert_equal Contractor.count, json_body.count
        returned_record = json_body.first
        assert_equal contractor.name, returned_record['name']
      end

      test 'index filters user-only contractors' do
        get :index, params: { f: { with_user: true } }, format: :json

        assert_response :success
        assert_equal Contractor.joins(:user).count, json_body.count
        ids = JSON.parse(response.body).map { |record| record['id'] }
        assert_not_includes ids, contractors(:non_user_contractor).id
      end

      test 'index filters contractors by state' do
        contractor.update(state: :pending)

        get :index, params: { f: { state: 'pending' } }, format: :json

        assert_response :success
        assert_equal Contractor.pending.count, json_body.count
      end

      test 'PATCH #activate activates contractor' do
        contractor.update(state: :pending)

        patch :activate, params: { id: contractor }, format: :json

        assert_response :no_content
        assert contractor.reload.active?
      end

      test 'GET #show returns proper data' do
        get :show, params: { id: contractor }, format: :json

        assert_response :success
        assert_equal contractor.vat_payer, json_body['vat_payer']
      end

      test 'contractor is created with valid params and current user as created by' do
        user = users(:wiktoria)
        sign_in user

        post :create, params: { contractor: valid_params }, format: :json
        assert_response :created

        get :show, params: { id: json_body['id'] }, format: :json
        assert_response :success
        assert_equal json_body['created_by'], user.full_name
      end

      test 'user without activity cannot delete contractor' do
        delete :destroy, params: { id: contractor.id }, format: :json

        assert_equal 403, response.status
      end

      test 'active contractor cannot be deleted' do
        user = users(:wiktoria)
        user.global_roles << global_roles(:global_admin)
        sign_in user

        delete :destroy, params: { id: contractor.id }, format: :json

        assert_equal 422, response.status
        assert_equal json_body['error'], 'Active Contractor cannot be deleted.'
      end

      test 'contractor with cost invoices cannot be deleted' do
        user = users(:wiktoria)
        user.global_roles << global_roles(:global_admin)
        sign_in user

        board_contractor = contractors(:board_contractor)
        board_contractor.update(state: :pending)

        delete :destroy, params: { id: board_contractor.id }, format: :json

        assert_equal 422, response.status
        assert_equal json_body['error'], 'Contractor has invoices and cannot be deleted.'
      end

      test 'contractor with active B2B cost invoices cannot be deleted' do
        user = users(:wiktoria)
        user.global_roles << global_roles(:global_admin)
        sign_in user

        board_contractor = contractors(:board_contractor)
        board_contractor.update(state: :pending)
        board_contractor.cost_invoices.where(type: 'Dms::CostInvoice').map(&:deleted!)

        delete :destroy, params: { id: board_contractor.id }, format: :json

        assert_equal 422, response.status
        assert_equal json_body['error'], 'Contractor has invoices and cannot be deleted.'
      end

      test 'contractor with active DMD cost invoices cannot be deleted' do
        user = users(:wiktoria)
        user.global_roles << global_roles(:global_admin)
        sign_in user

        board_contractor = contractors(:board_contractor)
        board_contractor.update(state: :pending)
        board_contractor.cost_invoices.where(type: 'B2B::CostInvoice').map(&:rejected!)

        delete :destroy, params: { id: board_contractor.id }, format: :json

        assert_equal 422, response.status
        assert_equal json_body['error'], 'Contractor has invoices and cannot be deleted.'
      end

      test 'contractor with deleted cost invoices can be deleted' do
        user = users(:wiktoria)
        user.global_roles << global_roles(:global_admin)
        sign_in user

        board_contractor = contractors(:board_contractor)
        board_contractor.update(state: :pending)
        board_contractor.cost_invoices.where(type: 'Dms::CostInvoice').map(&:deleted!)
        board_contractor.cost_invoices.where(type: 'B2B::CostInvoice').map(&:rejected!)

        assert_difference('Contractor.count', -1) do
          delete :destroy, params: { id: board_contractor.id }, format: :json
        end
        assert_equal 204, response.status
      end
    end
  end
end
