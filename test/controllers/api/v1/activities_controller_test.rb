require 'test_helper'

# debug sql:
# tail -n 20000 -f log/test.log | grep -A40 ": test_create_global_role_ids"
class Api::V1::ActivitiesControllerTest < ActionController::TestCase
  # include Minitest::XSwaggerSignInAs

  def test_index_scoped_for_programmer
    user = users(:mkalita_user_alternative)
    user.global_roles << global_roles(:global_admin_programmer)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert_equal Activity.all.uniq.size, json_body.uniq.size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_scoped_for_admin
    user = users(:mkalita_user_alternative)
    user.global_roles << global_roles(:global_admin)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert_equal Activity.admin.uniq.size, json_body.uniq.size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_scoped_for_user
    user = users(:mkalita_user_alternative)
    user.global_roles << global_roles(:global_user)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert_equal Activity.default.uniq.size, json_body.uniq.size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_search_term
    user = users(:mkalita_user_alternative)
    user.global_roles << global_roles(:global_user)
    authenticate(user)

    get :index, params: { f: { term: 'absences:show' } }, format: :json

    assert_response :success
    assert_equal 1, json_body.size
  end
end
