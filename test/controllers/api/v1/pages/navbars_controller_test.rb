require 'test_helper'

# debug sql:
# tail -n 20000 -f log/test.log | grep -A40 ": test_create_global_role_ids"
class Api::V1::Pages::NavbarsControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  fixtures :global_roles, :user_global_roles, :users

  def test_public_show
    get :public_show, format: :json
    assert_response :success, @response.body.to_s
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_show
    viewer = users(:mkalita_user)
    sign_in(viewer)
    get :show, format: :json
    assert_response :success, @response.body.to_s
    assert_includes json_body, 'my_abilities'
    assert_kind_of Hash, json_body['my_abilities']
    assert json_body['my_abilities']['my_access']
    assert_equal true, json_body['my_abilities']['my_roles']['global_admin']
    assert_equal false, json_body['my_abilities']['my_roles']['global_admin_programmer']
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_show_response_should_include_my_menu
    viewer = users(:mkalita_user)
    sign_in(viewer)
    get :show, format: :json
    assert_response :success, @response.body.to_s
    assert_includes json_body, 'my_menu'
    refute_empty json_body['my_menu']
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_show_response_should_include_my_menu_scoped
    viewer = users(:mkalita_user)
    viewer.global_roles.destroy_all
    sign_in(viewer)
    get :show, format: :json
    assert_response :success, @response.body.to_s
    assert_includes json_body, 'my_menu'
    assert_nil json_body['projects']
    assert_nil json_body['management']
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  test 'show response should include unaccepted_agreements' do
    viewer = users(:mkalita_user)
    sign_in(viewer)
    get :show, format: :json
    assert json_body.has_key?('unaccepted_agreements')
  end
end
