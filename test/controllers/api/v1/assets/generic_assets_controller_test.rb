require 'test_helper'

class Api::V1::Assets::GenericAssetsControllerTest < ActionController::TestCase
  setup do
    @user = users(:wiktoria)
    project = projects(:one)
    authenticate(@user)
    @asset = GenericAsset.create!(project: project, name: 'budogram',
                                  requester: @user,
                                  requested_date: Time.zone.now,
                                  notes: 'My awesome notes')

    @valid_params = {
      project_id: project.id,
      user_id: @user.id,
      name: 'My awesome name',
      notes: 'My awesome notes'
    }
  end

  test 'asset is created given valid params' do
    assert_difference('GenericAsset.count') do
      post :create, params: { generic_asset: @valid_params }, format: :json
    end

    assert_response :created
  end

  test 'validation fails properly on create' do
    post :create, params: { generic_asset: @valid_params.merge(name: '') }, format: :json
    assert_response :unprocessable_entity
  end

  test 'show works properly' do
    get :show, params: { id: @asset }, format: :json

    assert_response :success
    assert_equal @asset.id, json_body['id']
  end
end
