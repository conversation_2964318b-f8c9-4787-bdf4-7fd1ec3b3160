require 'test_helper'

module Api
  module V1
    class PublicActivitiesControllerTest < ActionController::TestCase
      setup do
        @user = users(:mkalita_global_admin_programmer)
        authenticate(@user)
        @project = projects(:one)
      end

      test 'index' do
        @project.memberships.create(member: @user, roles: [Role.first])

        get :index, params: { project_id: @project }, format: :json

        assert_response :success
        assert_equal 1, json_body.size
      end
    end
  end
end
