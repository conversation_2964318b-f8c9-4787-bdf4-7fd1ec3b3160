require "test_helper"

module Api
  module V1
    class PositionsControllerTest < ActionController::TestCase
      include Minitest::XSwaggerSignInAs

      fixtures :positions

      test 'shows all positions' do
        get :index, format: :json
        assert_response :success, @response.body.to_s
        assert_equal json_body.size, assigns(:positions).size
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      test 'shows only filtered positions' do
        get :index, params: { f: { term: 'java' } }, format: :json
        assert_equal json_body.size, 2
        assert_equal json_body.first['name'], 'Senior Java Developer'
        assert_equal json_body.last['name'], 'Java Developer'
      end

      test 'renders 404 if page is too much' do
        assert_difference('Position.count', 1) do
          post :create, params: { position: { name: 'Mid' } }, format: :json
        end
        assert_response 201, @response.body.to_s
      end

      test 'updates pointed posieion' do
        position = positions(:one)
        put :update, params: { id: position, position: { name: '<PERSON>' } }, format: :json
        position.reload
        assert_equal 'Junior', position.name
        assert_response 204, @response.body.to_s
      end

      test 'shows pointed position' do
        position = positions(:one)
        get :show, params: { id: position }, format: :json
        assert_response 200, @response.body.to_s
      end

      test 'destroys pointed position' do
        position = positions(:one)
        assert_difference('Position.count', -1) do
          delete :destroy, params: { id: position }, format: :json
          assert_response 204, @response.body.to_s
        end
      end
    end
  end
end
