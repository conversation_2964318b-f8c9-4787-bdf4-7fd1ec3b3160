require 'test_helper'

# debug sql:
# tail -n 20000 -f log/test.log | grep -A40 ": test_create_global_role_ids"
class Api::V1::ProjectsControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  fixtures :global_roles, :user_global_roles, :users

  def test_index_authorization
    user = users(:mkalita_user) # role doesn't matter
    policy = stub(index?: false)
    ProjectPolicy.stubs(:new).with(user, Project).returns(policy)
    sign_in(user)
    get :index, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_show_authorization
    project = projects(:mkalita_project)
    user = users(:mkalita_user) # role doesn't matter
    policy = stub(show?: false)
    ProjectPolicy.stubs(:new).with(user, project).returns(policy)
    sign_in(user)
    get :show, params: { id: projects(:mkalita_project).id }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_create_authorization
    user = users(:mkalita_user) # role doesn't matter
    policy = stub(create?: false, permitted_attributes: [])
    ProjectPolicy.stubs(:new).with(user, Project).returns(policy)
    sign_in(user)
    post :create, params: { project: { accounting_number_id: accounting_numbers(:one).id } },
                  format: :json
    assert_response 403, @response.body.to_s
  end

  def test_update_authorization
    project = projects(:mkalita_project)
    user = users(:mkalita_user) # role doesn't matter
    policy = stub(update?: false)
    ProjectPolicy.stubs(:new).with(user, project).returns(policy)
    sign_in(user)
    patch :update, params: { id: projects(:mkalita_project).id,
                   project: { accounting_number_id: accounting_numbers(:one).id } }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_destroy_authorization
    project = projects(:mkalita_project)
    user = users(:mkalita_user) # role doesn't matter
    policy = stub(destroy?: false)
    ProjectPolicy.stubs(:new).with(user, project).returns(policy)
    sign_in(user)
    delete :destroy, format: :json, params: { id: project.id }
    assert_response 403, @response.body.to_s
  end

  def test_rescues_module_inclusion
    assert_includes Api::V1::ProjectsController.ancestors, Api::V1::Concerns::Handlers::Rescues
  end

  def test_index
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:projects).size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_overhead_filter
    get :index, params: { f: { overhead: true } }, format: :json

    assert_response :success
    assert_equal Project.joins(:accounting_number)
                        .where(accounting_numbers: { overhead: true }).count,
                 json_body.size
  end

  def test_index_with_not_umbrella_filter
    project = projects(:one)
    project.accounting_number.update(number: 0)

    get :index, params: { f: { not_umbrella: true } }, format: :json

    assert_response :success
    refute_includes json_body.pluck('id'), project.id
  end

  def test_index_with_of_pm_filter
    pm = users(:wiktoria)
    get :index, params: { f: { of_pm: pm } }, format: :json

    assert_response :success
    assert_includes (json_body.map { |project| project['id'] }), projects(:one).id
  end

  def test_index_with_of_pm_or_responsible_filter
    get :index, params: { f: { of_pm_or_responsible: users(:mikolaj) } }, format: :json

    assert_response :success
    assert_same_elements [projects(:one), projects(:four)].pluck(:id), json_body.pluck('id')
  end

  def test_index_with_term_filter
    get :index, params: { f: { term: 'Prace wewnętrzne' } }, format: :json

    assert_response :success
    assert_equal 2, json_body.count
    assert_equal 'Artegence - prace wewnętrzne', json_body.first['name']
  end

  def test_created_after_filter
    project = projects(:artegence_wew)
    project.update_column :created_at, 1.month.from_now

    get :index, params: { f: { created_after: 2.days.from_now.to_date } }, format: :json

    assert_equal 1, json_body.count
    assert_equal project.id, json_body.first['id']
  end

  def test_created_before_filter
    project = projects(:artegence_wew)
    project.update_column :created_at, 1.month.before

    get :index, params: { f: { created_before: 2.days.before.to_date } }, format: :json

    assert_equal 1, json_body.count
    assert_equal project.id, json_body.first['id']
  end

  def test_index_page_2
    users_size = Project.count
    user_on_first_page = Project.order('id DESC').first!
    get :index, format: :json, params: { per_page: 1, page: 2 }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.last['id'].to_i > 0
    assert json_body.last['id'].to_i != user_on_first_page.id
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_param_collection_for_select
    get :index, format: :json, params: { f: { collection_for_select: true } }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:projects).active.size
    assert !json_body.include?('description')
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_param_collection_for_select_and_show_closed_on_true
    get :index, format: :json, params: { f: { collection_for_select: true, show_closed: true } }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:projects).size
    assert !json_body.include?('description')
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_param_collection_for_select_and_show_closed_on_false
    get :index, format: :json, params: { f: { collection_for_select: true, show_closed: false } }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:projects).active.size
    assert !json_body.include?('description')
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_show_closed_on_true
    get :index, format: :json, params: { f: { show_closed: true } }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:projects).size
    assert !json_body.include?('description')
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_show_closed_on_false
    get :index, format: :json, params: { f: { show_closed: false } }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:projects).size
    assert !json_body.include?('description')
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_for_select_with_company_filter_with_nil_value
    project = projects(:one)
    project.update_column :company_id, nil

    get :index, format: :json, params: { f: { collection_for_select: true, of_company: companies(:one) } }

    assert_response :success
    assert_not_includes json_body.pluck('id'), project.id
  end

  def test_index_with_param_roots
    get :index, format: :json, params: { f: { roots: true } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.last['parent_id'].nil?
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_param_immediate_children
    get :index, format: :json, params: { f: { immediate_children: projects(:mkalita_project).id } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    json_body.each do |obj|
      assert_equal projects(:mkalita_project).id, obj['parent_id']
    end
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_param_not_self_or_descendants
    get :index, format: :json,
                params: { f: { not_self_or_descendants: projects(:mkalita_project).id } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    json_body.each do |obj|
      refute_equal projects(:mkalita_project).id, obj['id']
    end
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_of_responsible_filter_and_regular_user
    user = users(:mkalita_user)
    user.global_roles = [global_roles(:global_pm)]
    user.save

    get :index, params: { f: { of_responsible: users(:wiktoria).id } }, format: :json

    assert_response :success
    assert_equal 0, json_body.count
  end

  def test_index_with_of_responsible_or_admin_filter_and_responsible_user
    get :index, params: { f: { of_responsible_or_admin: users(:mikolaj).id } }, format: :json

    assert_response :success
    assert_equal 1, json_body.count
  end

  def test_index_with_of_responsible_or_admin_filter_and_global_admin
    get :index, params: { f: { of_responsible_or_admin: users(:mkalita_global_admin_programmer).id }, per_page: 20, page: 1  }, format: :json

    assert_response :success
    assert_equal Project.all.count, json_body.count
  end

  def test_index_with_payment_schedule_filter
    get :index, params: { f: { with_payment_schedule: true } }, format: :json

    assert_response :success
    assert_equal Project.where(payment_schedule_required: true).count, json_body.count
  end

  def test_index_with_permission_filter
    get :index, params: { f: { permission: 'edit_project' } }, format: :json

    assert_response :success
    assert_equal 1, json_body.count
    assert_equal projects(:mkalita_project).id, json_body.first['id']

    get :index, params: { f: { permission: 'non_existent_permission' } }, format: :json

    assert_response :success
    assert_equal 0, json_body.count
  end

  def test_index_with_sort_by_name
    get :index, params: { f: { sort: 'name desc' } }, format: :json

    assert_response :success
    assert_equal Project.order(name: :desc).limit(10).pluck(:name), json_body.map { |project| project['name'] }
  end

  def test_index_with_status_filter
    get :index, params: { f: { status: 'active' }, per_page: 50 }, format: :json

    assert_response :success
    assert_equal Project.active.count, json_body.count
  end

  def test_index_with_memberships_filter
    user = users(:wiktoria)
    get :index, params: { f: { memberships_member_id: user, memberships_member_type: 'User' } }, format: :json

    assert_response :success
    assert_equal user.memberships.count, json_body.count
  end

  def test_index_with_not_self_or_descendant_with_non_existent_project
    get :index, params: { f: { not_self_or_descendants: 'not-self-and-descendent' } }, format: :json

    assert_response :success
    assert_equal 0, json_body.count
  end

  def test_index_with_sla_filter
    project = projects(:one)
    project.update(sla: true, sla_start_date: Date.today)

    get :index, params: { f: { sla: true } }, format: :json

    assert_response :success
    assert_equal 1, json_body.count
    assert_includes json_body.map { |p| p['id'] }, project.id
    assert json_body.all? { |p| p['sla'] }
  end

  def test_index_with_sla_ending_filter_in_future
    travel_to(Date.new(2025, 4, 25))

    project = projects(:one)
    project.update(sla: true, sla_start_date: Date.new(2024, 7, 1), sla_end_date: Date.new(2025, 7, 26))

    get :index, params: { f: { sla_ending: true } }, format: :json

    assert_response :success
    assert_equal 0, json_body.count
  end

  def test_index_with_sla_ending_filter_3_months_to_end
    travel_to(Date.new(2025, 4, 25))

    project = projects(:one)
    project.update(sla: true, sla_start_date: Date.new(2024, 7, 1), sla_end_date: Date.new(2025, 7, 24))

    get :index, params: { f: { sla_ending: true } }, format: :json

    assert_response :success
    assert_equal 1, json_body.count
  end

  def test_index_with_sla_ending_filter_in_past
    travel_to(Date.new(2025, 2, 25))

    project = projects(:one)
    project.update(sla: true, sla_start_date: Date.new(2024, 7, 1), sla_end_date: Date.new(2024, 12, 1))

    get :index, params: { f: { sla_ending: true } }, format: :json

    assert_response :success
    assert_equal 1, json_body.count
    assert_includes json_body.map { |p| p['id'] }, project.id
    assert json_body.all? { |p| p['sla'] }
  end

  def test_create_and_assign_system_role
    default_role = roles(:pm)
    Settings.new_project_user_role_id = default_role.id
    assert_difference('Project.count') do
      post :create, params: { project: {
        accounting_number_id: projects(:mkalita_project).accounting_number_id,
        description: projects(:mkalita_project).description,
        homepage: projects(:mkalita_project).homepage,
        identifier: projects(:mkalita_project).identifier + '_test_create',
        inherit_members: projects(:mkalita_project).inherit_members,
        name: projects(:mkalita_project).name,
        owncloud: projects(:mkalita_project).owncloud,
        parent_id: projects(:mkalita_project).parent_id,
        public: projects(:mkalita_project).public,
        status: projects(:mkalita_project).status,
        gid_number: projects(:mkalita_project).gid_number,
        company_id: companies(:two).id
      } }, format: :json
    end
    assert_response 201, @response.body.to_s
    project = Project.find(json_body['id'])
    assert_equal project.author, users(:mkalita_user)
    assert users(:mkalita_user).memberships
      .includes(:roles).where(
        project_id: project.id,
        roles: { id: default_role.id }
      ).exists?
  end

  def test_assign_system_role_if_inherit_members_is_true
    default_role = roles(:dev)
    Settings.new_project_user_role_id = default_role.id
    user = users(:mkalita_user)
    assert_difference('Project.count') do
      post :create, params: { project: {
        identifier: 'inherited-project',
        name: 'New project name',
        accounting_number_id: accounting_numbers(:zero).id,
        inherit_members: true,
        company_id: companies(:one).id
      } }, format: :json
    end
    assert_response :created
    project = Project.find(json_body['id'])
    assert user.memberships.joins(:roles)
               .where(project_id: project.id, roles: { id: default_role.id })
               .exists?
  end

  def test_create
    assert_difference('Project.count') do
      post :create, params: { project: {
        accounting_number_id: projects(:mkalita_project).accounting_number_id,
        description: projects(:mkalita_project).description,
        homepage: projects(:mkalita_project).homepage,
        identifier: projects(:mkalita_project).identifier + '_test_create',
        inherit_members: projects(:mkalita_project).inherit_members,
        name: projects(:mkalita_project).name,
        owncloud: projects(:mkalita_project).owncloud,
        parent_id: projects(:mkalita_project).parent_id,
        public: projects(:mkalita_project).public,
        status: projects(:mkalita_project).status,
        gid_number: projects(:mkalita_project).gid_number,
        company_id: companies(:two).id
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    project = Project.find(json_body['id'])
    assert_equal project.author, users(:mkalita_user)
    assert_equal project_url(project), response.location
  end

  def test_create_subproject
    assert_difference('Project.count') do
      post :create, params: { project: {
        accounting_number_id: projects(:mkalita_project).accounting_number_id,
        description: projects(:mkalita_project).description,
        homepage: projects(:mkalita_project).homepage,
        identifier: projects(:mkalita_project).identifier + '_test_create_subproject',
        inherit_members: projects(:mkalita_project).inherit_members,
        name: projects(:mkalita_project).name,
        owncloud: projects(:mkalita_project).owncloud,
        parent_id: projects(:mkalita_project).id,
        public: projects(:mkalita_project).public,
        status: projects(:mkalita_project).status,
        gid_number: projects(:mkalita_project).gid_number,
        company_id: companies(:two).id
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    project = Project.find(json_body['id'])
    assert_equal project.author, users(:mkalita_user)
    assert_equal project_url(project), response.location
    assert_equal json_body['parent_id'], projects(:mkalita_project).id
  end

  def test_create_project_with_personal_data
    assert_difference('Project.count', 1) do
      post :create, params: { project: {
        accounting_number_id: projects(:mkalita_project).accounting_number_id,
        description: projects(:mkalita_project).description,
        homepage: projects(:mkalita_project).homepage,
        identifier: projects(:mkalita_project).identifier + '_test_create',
        inherit_members: projects(:mkalita_project).inherit_members,
        name: projects(:mkalita_project).name,
        owncloud: projects(:mkalita_project).owncloud,
        parent_id: projects(:mkalita_project).parent_id,
        public: projects(:mkalita_project).public,
        status: projects(:mkalita_project).status,
        gid_number: projects(:mkalita_project).gid_number,
        company_id: companies(:two).id,
        personal_data: true,
        project_agreements_attributes: [{ company_id: companies(:one).id, business_to_business: true,  content: 'contentArteB2B', confirmation_button_text: 'Accept' },
                                        { company_id: companies(:one).id, business_to_business: false, content: 'contentArteUOP', confirmation_button_text: 'Accept' },
                                        { company_id: companies(:two).id, business_to_business: true,  content: 'contentEfiB2B',  confirmation_button_text: 'Accept' },
                                        { company_id: companies(:two).id, business_to_business: false, content: 'contentEfiUOP',  confirmation_button_text: 'Accept' }]
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    project = Project.find(json_body['id'])
    assert_equal project.author, users(:mkalita_user)
    assert_equal project_url(project), response.location
  end

  def test_create_project_agreements_while_creating_project_with_personal_data
    assert_difference('ProjectAgreement.count', 4) do
      post :create, params: { project: {
        accounting_number_id: projects(:mkalita_project).accounting_number_id,
        description: projects(:mkalita_project).description,
        homepage: projects(:mkalita_project).homepage,
        identifier: projects(:mkalita_project).identifier + '_test_create',
        inherit_members: projects(:mkalita_project).inherit_members,
        name: projects(:mkalita_project).name,
        owncloud: projects(:mkalita_project).owncloud,
        parent_id: projects(:mkalita_project).parent_id,
        public: projects(:mkalita_project).public,
        status: projects(:mkalita_project).status,
        gid_number: projects(:mkalita_project).gid_number,
        company_id: companies(:two).id,
        personal_data: true,
        project_agreements_attributes: [{ company_id: companies(:one).id, business_to_business: true,  content: 'contentArteB2B', confirmation_button_text: 'Accept' },
                                        { company_id: companies(:one).id, business_to_business: false, content: 'contentArteUOP', confirmation_button_text: 'Accept' },
                                        { company_id: companies(:two).id, business_to_business: true,  content: 'contentEfiB2B',  confirmation_button_text: 'Accept' },
                                        { company_id: companies(:two).id, business_to_business: false, content: 'contentEfiUOP',  confirmation_button_text: 'Accept' }]
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    assert_equal ProjectAgreement.last(4).first.business_to_business, true
    assert_equal ProjectAgreement.last(4).first.company_id, companies(:one).id
    assert_equal ProjectAgreement.last(4).first.content, 'contentArteB2B'
    assert_equal ProjectAgreement.last.business_to_business, false
    assert_equal ProjectAgreement.last.company_id, companies(:two).id
    assert_equal ProjectAgreement.last.content, 'contentEfiUOP'
  end

  def test_show
    get :show, params: { id: projects(:mkalita_project) }, format: :json
    assert_response :success, @response.body.to_s
    assert_includes json_body, 'url'
    assert_includes json_body, 'invoice_attachments_required'
  end

  def test_show_actions_for_current_user
    get :show, params: { id: projects(:mkalita_project) }, format: :json
    assert_response :success, @response.body.to_s
    assert_includes json_body, 'actions_for_current_user'
    assert_includes json_body['actions_for_current_user'], 'index'
    assert [true, false].include?(json_body['actions_for_current_user']['index'])
  end

  def test_update
    project = projects(:mkalita_project)

    put :update, params: { id: project, project: {
      accounting_number_id: project.accounting_number_id,
      company_id: project.company_id,
      description: project.description,
      homepage: project.homepage,
      identifier: project.identifier,
      inherit_members: project.inherit_members,
      name: project.name,
      owncloud: project.owncloud,
      parent_id: project.parent_id,
      public: project.public,
      status: project.status,
      gid_number: project.gid_number,
      invoice_attachments_required: true
    } }, format: :json

    assert_response 204, @response.body.to_s

    project.reload

    assert_equal true, project.invoice_attachments_required
  end

  def test_update_with_project_agreements_attributes
    post :create, params: { project: {
      accounting_number_id: projects(:mkalita_project).accounting_number_id,
      description: projects(:mkalita_project).description,
      homepage: projects(:mkalita_project).homepage,
      identifier: projects(:mkalita_project).identifier + '_test_create',
      inherit_members: projects(:mkalita_project).inherit_members,
      name: projects(:mkalita_project).name,
      owncloud: projects(:mkalita_project).owncloud,
      parent_id: projects(:mkalita_project).parent_id,
      public: projects(:mkalita_project).public,
      status: projects(:mkalita_project).status,
      gid_number: projects(:mkalita_project).gid_number,
      company_id: companies(:two).id,
      personal_data: true,
      project_agreements_attributes: [{ company_id: companies(:one).id, business_to_business: true,  content: 'contentArteB2B', confirmation_button_text: 'Accept' },
                                      { company_id: companies(:one).id, business_to_business: false, content: 'contentArteUOP', confirmation_button_text: 'Accept' },
                                      { company_id: companies(:two).id, business_to_business: true,  content: 'contentEfiB2B', confirmation_button_text: 'Accept' },
                                      { company_id: companies(:two).id, business_to_business: false, content: 'contentEfiUOP', confirmation_button_text: 'Accept' }]
    } }, format: :json
    put :update, params: { id: Project.last.id, project: {
      accounting_number_id: Project.last.accounting_number_id,
      company_id: Project.last.company_id,
      description: Project.last.description,
      homepage: Project.last.homepage,
      identifier: Project.last.identifier,
      inherit_members: Project.last.inherit_members,
      name: Project.last.name,
      owncloud: Project.last.owncloud,
      parent_id: Project.last.parent_id,
      public: Project.last.public,
      status: Project.last.status,
      gid_number: Project.last.gid_number,
      project_agreements_attributes: [{ company_id: companies(:one).id, business_to_business: true,  content: 'contentArteB2B', confirmation_button_text: 'Accept' },
                                      { company_id: companies(:one).id, business_to_business: false, content: 'contentArteUOP', confirmation_button_text: 'Accept' },
                                      { company_id: companies(:two).id, business_to_business: true,  content: 'contentEfiB2B',  confirmation_button_text: 'Accept' },
                                      { company_id: companies(:two).id, business_to_business: false, content: 'contentEfiUOP',  confirmation_button_text: 'Accept' }]
    } }, format: :json
    assert_response 204, @response.body.to_s
    assert Project.last.project_agreements.count, 4
  end

  def test_update_with_project_agreements_attributes_change
    post :create, params: { project: {
      accounting_number_id: projects(:mkalita_project).accounting_number_id,
      description: projects(:mkalita_project).description,
      homepage: projects(:mkalita_project).homepage,
      identifier: projects(:mkalita_project).identifier + '_test_create',
      inherit_members: projects(:mkalita_project).inherit_members,
      name: projects(:mkalita_project).name,
      owncloud: projects(:mkalita_project).owncloud,
      parent_id: projects(:mkalita_project).parent_id,
      public: projects(:mkalita_project).public,
      status: projects(:mkalita_project).status,
      gid_number: projects(:mkalita_project).gid_number,
      company_id: companies(:two).id,
      personal_data: true,
      project_agreements_attributes: [{ company_id: companies(:one).id, business_to_business: true,  content: 'contentArteB2B', confirmation_button_text: 'Accept' },
                                      { company_id: companies(:one).id, business_to_business: false, content: 'contentArteUOP', confirmation_button_text: 'Accept' },
                                      { company_id: companies(:two).id, business_to_business: true,  content: 'contentEfiB2B',  confirmation_button_text: 'Accept' },
                                      { company_id: companies(:two).id, business_to_business: false, content: 'contentEfiUOP',  confirmation_button_text: 'Accept' }]
    } }, format: :json
    put :update, params: { id: Project.last.id, project: {
      accounting_number_id: Project.last.accounting_number_id,
      company_id: Project.last.company_id,
      description: Project.last.description,
      homepage: Project.last.homepage,
      identifier: Project.last.identifier,
      inherit_members: Project.last.inherit_members,
      name: Project.last.name,
      owncloud: Project.last.owncloud,
      parent_id: Project.last.parent_id,
      public: Project.last.public,
      status: Project.last.status,
      gid_number: Project.last.gid_number,
      project_agreements_attributes: [{ company_id: companies(:one).id, business_to_business: true,  content: 'new_contentArteB2B', confirmation_button_text: 'Accept' },
                                      { company_id: companies(:one).id, business_to_business: false, content: 'contentArteUOP', confirmation_button_text: 'Accept' },
                                      { company_id: companies(:two).id, business_to_business: true,  content: 'contentEfiB2B', confirmation_button_text: 'Accept' },
                                      { company_id: companies(:two).id, business_to_business: false, content: 'contentEfiUOP', confirmation_button_text: 'Accept' }]
    } }, format: :json
    assert_response 204, @response.body.to_s
    assert Project.last.project_agreements.count, 5
  end

  def test_destroy
    assert_difference('Project.count', -2) do
      delete :destroy, format: :json, params: { id: projects(:mkalita_project) }
      assert_response 204, @response.body.to_s
    end
  end

  def test_archive
    p1 = Project.create(name: 'Parent',
                        identifier: 'parent',
                        accounting_number: accounting_numbers(:one),
                        company: companies(:two))
    p2 = Project.create(name: 'First child',
                        identifier: 'first_child',
                        accounting_number: accounting_numbers(:one),
                        parent_id: p1.id,
                        company: companies(:two))
    p3 = Project.create(name: 'Second child',
                        identifier: 'second_child',
                        accounting_number: accounting_numbers(:one),
                        parent_id: p1.id,
                        company: companies(:two))
    assert_equal 'active', p1.reload.status
    assert_equal 'active', p2.reload.status
    assert_equal 'active', p3.reload.status
    patch :archive, params: { id: p1.id }, format: :json
    assert_response 204, @response.body.to_s
    assert_equal 'archived', p1.reload.status
    assert_equal 'archived', p2.reload.status
    assert_equal 'archived', p3.reload.status
  end

  def test_unarchive
    p1 = Project.create(name: 'Parent',
                        identifier: 'parent',
                        accounting_number: accounting_numbers(:one),
                        company: companies(:two))
    p2 = Project.create(name: 'First child',
                        identifier: 'first_child',
                        accounting_number: accounting_numbers(:one),
                        parent_id: p1.id,
                        company: companies(:two))
    p3 = Project.create(name: 'Second child',
                        identifier: 'second_child',
                        accounting_number: accounting_numbers(:one),
                        parent_id: p1.id,
                        company: companies(:two))
    p1.archived!
    p2.archived!
    p3.archived!
    patch :unarchive, params: { id: p1.id }, format: :json
    assert_response 204, @response.body.to_s
    assert_equal 'active', p1.reload.status
    assert_equal 'archived', p2.reload.status
    assert_equal 'archived', p3.reload.status
  end

  def test_unarchive_errors
    p1 = Project.create(name: 'Parent',
                        identifier: 'parent',
                        accounting_number: accounting_numbers(:one),
                        company: companies(:two))
    p2 = Project.create(name: 'First child',
                        identifier: 'first_child',
                        accounting_number: accounting_numbers(:one),
                        parent_id: p1.id,
                        company: companies(:two))
    p1.archived!
    p2.archived!
    put :unarchive, params: { id: p2.id }, format: :json
    assert_response 422, @response.body.to_s
    assert_equal 'One of this project\'s parents is not active', json_body['errors']['parent'].first
    p2.reload
    assert_equal 'archived', p2.status
  end

  def test_close
    accounting_number = accounting_numbers(:one)
    accounting_number.update(overhead: true)
    p1 = Project.create(name: 'Parent',
                        identifier: 'parent',
                        accounting_number: accounting_number,
                        company: companies(:two))
    p2 = Project.create(name: 'First child',
                        identifier: 'first_child',
                        accounting_number: accounting_number,
                        parent_id: p1.id,
                        company: companies(:two))
    assert_equal 'active', p1.reload.status
    assert_equal 'active', p2.reload.status
    patch :close, params: { id: p1.id }, format: :json
    assert_response 204, @response.body.to_s
    assert_equal 'closed', p1.reload.status
    assert_equal 'closed', p2.reload.status
  end

  def test_unhappy_close # project has pending invoices
    payment = Payment.joins(:invoices).last
    invoice = payment.invoices.first
    invoice.update!(state: :pending)
    project = invoice.project

    assert_equal 'active', project.reload.status
    patch :close, params: { id: project.id }, format: :json

    assert_response 422, @response.body.to_s
    assert_contains json_body['errors']['project_invoices'], 'has pending invoices'

    assert_equal 'active', project.reload.status
  end

  def test_not_overhead_unhappy_close
    kubernetes_namespaces(:cluster_one_project_one).close!
    project = projects(:one)
    project.payments.without_issued_invoice.destroy_all

    patch :close, params: { id: project.id }, format: :json

    assert_response :unprocessable_entity
    assert_not_empty json_body['errors']['overhead']
    assert_includes json_body['accounting_number_projects'].map { |json_project|
      json_project['name']
    }, projects(:two).name
  end

  def test_not_overhead_close
    kubernetes_namespaces(:cluster_one_project_one).close!
    project = projects(:one)
    project.payments.without_issued_invoice.destroy_all

    patch :close, params: { id: project.id, overhead: false }, format: :json, as: :json

    assert_response :no_content
    assert project.reload.closed?
    assert_not project.accounting_number.reload.overhead?
  end

  def test_overhead_close
    kubernetes_namespaces(:cluster_one_project_one).close!
    project = projects(:one)
    project.payments.without_issued_invoice.destroy_all

    patch :close, params: { id: project.id, overhead: true }, format: :json, as: :json

    assert_response :no_content
    assert project.reload.closed?
    assert project.accounting_number.reload.overhead?
  end

  def test_reopen
    p1 = Project.create(name: 'Parent',
                        identifier: 'parent',
                        accounting_number: accounting_numbers(:one),
                        company: companies(:two))
    p2 = Project.create(name: 'First child',
                        identifier: 'first_child',
                        accounting_number: accounting_numbers(:one),
                        parent_id: p1.id,
                        company: companies(:two))
    p1.closed!
    p2.closed!
    patch :reopen, params: { id: p1.id }, format: :json
    assert_response 204, @response.body.to_s
    assert_equal 'active', p1.reload.status
    assert_equal 'active', p2.reload.status
  end

  def test_copy_source_vcr
    VCR.use_cassette("copy_source_redmine_project") do
      RedmineApi.get("/projects/8/copy_source.json")
    assert_response 200
    end
  end

  def test_copy_vcr
    VCR.use_cassette("copy_redmine_project") do
    req = RedmineApi.post("/projects/8/copy.json",
                      body: {"project"=>{"name"=>"Newcopyname",
                      "identifier"=>"newcopyname"}})
    assert req.response.class == Net::HTTPCreated
    end
  end

  def test_generate_csv
    get :report, params: { year: '2018', month: '1' }
    assert_equal "Accounting number,Name,Closed by,Closed at,Overhead\n", @response.body.to_s
  end

  def test_generate_csv_with_actual_data
    project = projects(:artegence_wew)
    patch :close, params: { id: project, overhead: false }, format: :json

    get :report, params: { year: Time.zone.now.year, month: Time.zone.now.month }, format: :json

    assert_match project.name, response.body
  end

  def test_company_change_success
    project = projects(:one)
    new_company = companies(:two)
    invoice = invoices(:payment_three_amendment_draft)

    invoice.update_column(:payment_id, project.payments.first.id)

    patch :update, params: { id: project.id, project: { company_id: new_company.id } },
                   format: :json

    assert_response 204, @response.body.to_s
    assert_equal new_company.id, project.reload.company.id
  end

  def test_company_change_fail_with_issued_invoice
    project = projects(:two)
    old_company = project.company
    new_company = companies(:one)
    invoice = invoices(:payment_three)

    invoice.update_column(:payment_id, project.payments.first.id)
    invoice.update_column(:state, 1)

    patch :update, params: { id: project.id, project: { company_id: new_company.id } },
                   format: :json

    assert_response 422, @response.body.to_s
    assert_not_equal new_company.id, project.reload.company.id
    assert_equal old_company.id, project.reload.company.id
  end

  def test_company_change_fail_with_accepted_invoice
    project = projects(:two)
    old_company = project.company
    new_company = companies(:one)
    invoice = invoices(:payment_three)

    invoice.update_column(:payment_id, project.payments.first.id)
    invoice.update_column(:state, 4)

    patch :update, params: { id: project.id, project: { company_id: new_company.id } },
                   format: :json

    assert_response 422, @response.body.to_s
    assert_not_equal new_company.id, project.reload.company.id
    assert_equal old_company.id, project.reload.company.id
  end

  test 'idle' do
    project = projects(:one)
    project.update(idle: true)
    SidekiqScheduler::RedisManager.expects(:get_job_last_time)
                                  .with(IdleProjectsWorker::JOB_NAME)
                                  .returns('2023-11-01 13:00:00 +0100')

    get :idle, format: :json

    assert_response :success
    assert_equal '2023-11-01', json_body['generated_on']
    assert_equal 1, json_body['projects'].length
    assert_equal project.id, json_body['projects'].first['id']
  end

  test 'regressive' do
    settings = Settings.redmine_api
    projects = [projects(:two), projects(:three)]
    redmine_response = projects.to_json(only: %i[id name identifier])

    stub_request(
      :get, "#{settings.uri}/regressive_projects.json"
    ).with(
      query: { date_from: '', date_to: '' },
      headers: {
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
        'X-Redmine-API-Key' => settings.redmine_api_key,
        'X-Imperator-API-Key' => settings.imperator_api_key
      }
    ).to_return(
      body: redmine_response,
      headers: { 'Content-Type' => 'application/json' }
    )

    get :regressive, format: :json

    assert_response :success
    assert_equal redmine_response, response.body
  end

  test 'regression_stats return data fetched from Redmine' do
    settings = Settings.redmine_api
    project = projects(:two)
    redmine_response =
      '[{"firstname":"Wiktoria","lastname":"Hanowerska","issues_count":3,"regressive_count":0}]'

    stub_request(
      :get, "#{settings.uri}/projects/#{project.identifier}/dashboard_stats.json"
    ).with(
      query: { role_ids: Settings.redmine_api.dashboard_stats.role_ids,
               tester_role_id: Settings.redmine_api.dashboard_stats.tester_role_id,
               date_from: '', date_to: '' },
      headers: {
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
        'X-Redmine-API-Key' => settings.redmine_api_key,
        'X-Imperator-API-Key' => settings.imperator_api_key
      }
    ).to_return(
      body: redmine_response
    )

    get :regression_stats, params: { id: project.id }, format: :json

    assert_response :success
    assert_equal JSON.parse(redmine_response), json_body
  end

  test 'implementation_stats' do
    project = projects(:two)
    issue_one = issues(:one)
    issue_two = issues(:two)

    get :implementation_stats, params: { id: project.id }, format: :json

    assert_response :success
    implementation_stats = json_body
    assert_equal 1, implementation_stats['estimated_issues_count']
    assert_equal 1, implementation_stats['not_estimated_issues_count']
    assert_equal '4.0', implementation_stats['total_spent_hours_estimated']
    assert_equal '8.0', implementation_stats['total_spent_hours_not_estimated']
    assert_equal 2, implementation_stats['issues'].length

    issue_one_stats = implementation_stats['issues'].find { |issue| issue['redmine_id'] == issue_one.redmine_id }
    assert_equal issue_one.subject, issue_one_stats['subject']
    assert_equal issue_one.estimated_hours.as_json, issue_one_stats['estimated_hours']
    assert_equal [{ 'activity' => '116 RoR', 'hours' => '4.0' }], issue_one_stats['spent_hours_activities']
    assert_equal users(:milosz).id, issue_one_stats['spent_hours_users'].first['user']['id']

    issue_two_stats = implementation_stats['issues'].find { |issue| issue['redmine_id'] == issue_two.redmine_id }
    assert_equal issue_two.subject, issue_two_stats['subject']
    assert_nil issue_two_stats['estimated_hours']
    assert_equal [], issue_two_stats['spent_hours_activities']
    assert_equal [], issue_two_stats['spent_hours_users']
  end

  test 'implementation_stats categories' do
    project = projects(:two)
    issues(:one).update(subject: 'KOMUNIKACJA WEWNĘTRZNA: STATUSY, DAILY, itp., itd.')
    resource_time_entries(:project_two_spent_time_entry).update(issue: issues(:two))

    get :implementation_stats, params: { id: project.id }, format: :json

    assert_response :success
    assert_equal({ 'internal_communication' => '4.0', 'bugs' => '8.0' }, json_body['spent_hours_categories'])
  end

  test 'implementation_stats with tracker filter' do
    get :implementation_stats, params: { id: projects(:two).id, f: { tracker: 'Bug' } }, format: :json

    assert_response :success
    assert_equal [issues(:two).redmine_id], json_body['issues'].pluck('redmine_id')
  end

  test 'implementation_stats with open filter' do
    get :implementation_stats, params: { id: projects(:two).id, f: { open: true } }, format: :json

    assert_response :success
    assert_equal [issues(:one).redmine_id], json_body['issues'].pluck('redmine_id')
  end

  test 'implementation_stats with closed filter' do
    get :implementation_stats, params: { id: projects(:two).id, f: { closed: true } }, format: :json

    assert_response :success
    assert_equal [issues(:two).redmine_id], json_body['issues'].pluck('redmine_id')
  end

  test 'implementation_stats with dates filter' do
    get :implementation_stats, params: { id: projects(:two).id, f: { date_from: '2023-09-01', date_to: '2023-09-01' } }, format: :json

    assert_response :success
    assert_equal [issues(:one).redmine_id], json_body['issues'].pluck('redmine_id')
  end

  test 'implementation_stats with underestimated filter' do
    resource_time_entries(:milosz_spent_time_entry).update(hours: 10.1)

    get :implementation_stats, params: { id: projects(:two).id, f: { underestimated: true } }, format: :json

    assert_response :success
    assert_equal [issues(:one).redmine_id], json_body['issues'].pluck('redmine_id')
  end

  test 'implementation_stats with overestimated filter' do
    get :implementation_stats, params: { id: projects(:two).id, f: { overestimated: true } }, format: :json

    assert_response :success
    assert_equal [issues(:one).redmine_id, issues(:two).redmine_id], json_body['issues'].pluck('redmine_id')
  end

  test 'effectiveness_stats' do
    spent_time_entry = resource_time_entries(:milosz_spent_time_entry)
    user = spent_time_entry.user
    issue = spent_time_entry.issue

    get :effectiveness_stats, format: :json

    assert_response :success

    assert_equal 1, json_body.length
    user_stats = json_body.first
    assert_equal user.full_name, user_stats['user']['full_name']

    assert_equal 1, user_stats['effectiveness_stats'].length
    assert_equal issue.project.name, user_stats['effectiveness_stats'].first['project']['name']
    assert_equal '40.0', user_stats['effectiveness_stats'].first['effectiveness']
    assert_equal 0, user_stats['effectiveness_stats'].first['not_estimated_issues_count']
  end

  test 'effectiveness_stats categories' do
    resource_time_entries(:project_two_spent_time_entry).update(user: users(:milosz), issue: issues(:one))

    get :effectiveness_stats, format: :json

    assert_response :success

    project_stats = json_body.first['effectiveness_stats'].first
    assert_equal '10.0', project_stats['total_estimated_hours']
    assert_equal '12.0', project_stats['total_spent_hours']
    assert_equal({ 'development' => '12.0' }, project_stats['spent_hours_categories'])
  end

  test 'effectiveneses stats for a chief' do
    @request.headers['X-Swagger-Sign-In-As'] = nil
    authenticate(users(:wiktoria))

    get :effectiveness_stats, format: :json

    assert_response :success
  end

  test 'utilization_stats with missing dates' do
    get :utilization_stats, format: :json
    assert_response :bad_request

    get :utilization_stats, params: { date_from: '2023-08-01' }, format: :json
    assert_response :bad_request

    get :utilization_stats, params: { date_to: '2023-10-31' }, format: :json
    assert_response :bad_request
  end

  test 'utilization_stats with invalid dates' do
    get :utilization_stats, params: { date_from: '2023-08-01', date_to: 'invalid-date' }, format: :json
    assert_response :bad_request
  end

  test 'utilization_stats' do
    expected_keys = ['month', 'working_hours', 'scheduled_hours', 'spent_hours', 'booking', 'utilization', 'has_active_contract']
    user = users(:milosz)
    resource_time_entries(:milosz_scheduled_time_entry).update(date_from: Date.new(2023, 9, 11), date_to: Date.new(2023, 10, 2))

    get :utilization_stats, params: { date_from: '2023-08-01', date_to: '2023-10-31' }, format: :json

    assert_response :success

    assert_equal 2, json_body.length
    user_stats = json_body.find { |stats| stats['user']['id'] == user.id }
    assert_equal user.full_name, user_stats['user']['full_name']

    assert_equal 1, user_stats['projects'].length
    project_stats = user_stats['projects'].first
    assert_equal projects(:two).name, project_stats['project']['name']
    project_stats['months'] do |item|
      expected_keys.each do |key|
        assert item.key?(key)
        assert_not_nil item[key]
      end
    end
  end

  test 'gitlab_repositories' do
    project = projects(:one)
    authenticate(users(:wiktoria))
    response = %w[repo1 repo2]
    stub_request(:get, "#{Settings.redmine_api.uri}/projects/#{project.identifier}/gitlab_repositories.json")
      .to_return(body: response.to_json, headers: { 'Content-Type' => 'application/json' })

    get :gitlab_repositories, params: { id: project.id }, format: :json

    assert_response :success
    assert_equal response, json_body
  end

  test 'test_create_subproject_where_parent_not_allowed' do
    params = { project: {
      accounting_number_id: projects(:mkalita_project).accounting_number_id,
      chat: false,
      company_id: companies(:two).id,
      identifier: 'new-test-mkalita-project',
      inherit_members: true,
      name: 'new test mkalita project',
      owncloud: true,
      parent_id: projects(:mkalita_project).id,
      personal_data: false
    } }

    user = users(:milosz)
    # add global role to create project
    user.global_roles << global_roles(:global_accounting)
    # add role without permission to create subprojects
    membership = Membership.create!(member: user, project: projects(:mkalita_project), roles: [roles(:observer)])

    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

    assert_no_difference('Project.count') do
      post :create, params: params, format: :json
      assert_response :unprocessable_entity
    end
    assert_not_empty json_body['errors']['parent_id']

    # now add permission to create subprojects
    membership.update(roles: [roles(:mkalita_role)])

    assert_difference('Project.count') do
      post :create, params: params, format: :json
      assert_response :created
    end

    assert_equal json_body['parent_id'], projects(:mkalita_project).id
  end

  test 'test_update_subproject_when_user_have_not_permission_to_create_subprojects' do
    child_project = projects(:two)
    parent_project = projects(:mkalita_project)

    user = users(:milosz)
    # add global role to update project
    user.global_roles << global_roles(:global_pm)
    # add role without permission to update subprojects for parent project and with permission to update subproject for child project
    Membership.create!(member: user, project: child_project, roles: [roles(:mkalita_role)])
    parent_membership = Membership.create!(member: user, project: parent_project, roles: [roles(:observer)])

    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

    patch :update, params: { id: child_project.id,
                             project: { parent_id: parent_project.id } }, format: :json
    assert_response :unprocessable_entity
    assert_not_empty json_body['errors']['parent_id']

    # now add permission to update subprojects
    parent_membership.update(roles: [roles(:mkalita_role)])

    patch :update, params: { id: child_project.id,
                             project: { parent_id: parent_project.id } }, format: :json
    assert_response :no_content

    assert_equal child_project.reload.parent_id, parent_project.id
  end

  test 'test_create_subproject_with_inherit_members_and_default_groups' do
    Settings.default_groups = { "#{groups(:two).id}" => [roles(:observer).id] }

    project = projects(:mkalita_project)

    params = { project: {
      accounting_number_id: project.accounting_number_id,
      chat: false,
      company_id: companies(:two).id,
      identifier: 'new-test-mkalita-project',
      inherit_members: true,
      name: 'new test mkalita project',
      owncloud: true,
      parent_id: project.id,
      personal_data: false
    } }

    user = users(:milosz)
    user.global_roles << global_roles(:global_accounting)
    Membership.create!(member: user, project: project, roles: [roles(:mkalita_role)])

    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

    assert_difference('Project.count') do
      post :create, params: params, format: :json
      assert_response :created
    end

    assert_equal json_body['parent_id'], project.id
    assert project.reload['lft'] <= json_body['lft']
    assert project.reload['rgt'] >= json_body['rgt']
  end
end
