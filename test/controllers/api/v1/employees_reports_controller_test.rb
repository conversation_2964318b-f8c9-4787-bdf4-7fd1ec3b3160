require 'test_helper'

module Api
  module V1
    class EmployeesReportsControllerTest < ActionController::TestCase
      setup do
        user = users(:w<PERSON><PERSON>)
        authenticate(user)
      end

      test 'GET #index' do
        get :index, format: :json

        assert_response :success
        assert_equal(EmployeesReport.count, assigns(:workers_reports).count)
      end

      test 'POST #create' do
        assert_difference('EmployeesReport.count', 2) do
          post :create, params: { employees_report: { year: 2020, month: 1 } }, format: :json
        end

        assert_response :created
      end

      test 'POST #create with invalid params' do
        post :create, params: { employees_report: { year: 0, month: 1 } }, format: :json

        assert_response :unprocessable_entity
      end

      test 'GET #file' do
        employees_report = employees_reports(:may_2020_report)
        stub_employees_report_success(employees_report)
        employees_report.generate

        get :file, params: { id: employees_report }, format: :json

        assert_response :success
        assert_equal @response.headers['Content-Transfer-Encoding'], 'binary'
        assert_match "inline; filename=\"#{employees_report.file_name}\"",
                     @response.headers['Content-Disposition']
      end
    end
  end
end
