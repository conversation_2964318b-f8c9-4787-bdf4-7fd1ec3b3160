require 'test_helper'

module Api
  module V1
    class B2BReportsControllerTest < ActionController::TestCase
      setup do
        user = users(:w<PERSON><PERSON>)
        authenticate(user)
      end

      test 'GET #index' do
        get :index, format: :json

        assert_response :success
        assert_equal(B2BReport.count, assigns(:workers_reports).count)
      end

      test 'POST #create' do
        assert_difference -> { B2BReport.count }, 2 do
          post :create, params: { b2b_report: { year: 2020, month: 2 } }, format: :json
        end

        assert_response :created
      end

      test 'POST #create with invalid params' do
        post :create, params: { b2b_report: { year: 0, month: 1 } }, format: :json

        assert_response :unprocessable_entity
      end

      test 'GET #file' do
        stub_b2b_report_success
        b2b_report = b2b_reports(:april_2020_b2b_report)
        b2b_report.generate

        get :file, params: { id: b2b_report }, format: :json

        assert_response :success
        assert_equal @response.headers['Content-Transfer-Encoding'], 'binary'
        assert_match "inline; filename=\"#{b2b_report.file_name}\"",
                     @response.headers['Content-Disposition']
      end
    end
  end
end
