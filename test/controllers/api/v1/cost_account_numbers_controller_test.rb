require 'test_helper'

module Api
  module V1
    class CostAccountNumbersControllerTest < ActionController::TestCase
      let(:cost_account_number) { cost_account_numbers(:one) }
      let(:valid_attributes) do
        {
          number: '401-51', description: '<PERSON>rod<PERSON> czystości i higieny i BHP', kind: 'both'
        }
      end

      setup do
        authenticate(users(:wik<PERSON>))
      end

      test 'index' do
        get :index, format: :json

        assert_response :success
        assert_equal CostAccountNumber.count, json_body.count
      end

      test 'index show only active' do
        cost_account_number.update(active: false)

        get :index, params: { f: { active: true } }, format: :json

        assert_response :success
        assert_equal CostAccountNumber.active.count, json_body.count
      end

      test 'index with kind filter' do
        get :index, params: { f: { kind: 'direct' } }, format: :json

        assert_response :success
        assert_equal CostAccountNumber.direct.or(CostAccountNumber.both).count, json_body.count
      end

      test 'show' do
        get :show, params: { id: cost_account_number }, format: :json
      end

      test 'create' do
        assert_difference -> { CostAccountNumber.count } do
          post :create, params: { cost_account_number: valid_attributes }, format: :json
        end

        assert_response :created
      end

      test 'update' do
        patch :update, params: { id: cost_account_number, cost_account_number: valid_attributes },
                       format: :json

        assert_equal valid_attributes[:description], cost_account_number.reload.description
        assert_response :no_content
      end
    end
  end
end
