require 'test_helper'

class Api::V1::BiosControllerTest < ActionController::TestCase

  setup do
    @user = users(:mkalita_global_admin_programmer)
    @bio = bios(:one)
    @bio.update(user_id: @user.id)
    authenticate(@user)
  end

  test 'create bio' do
    post :create, params: { user_id: @user.id,
                            bio: { desc_en: 'My awesoem desc',
                                   image: Rack::Test::UploadedFile.new(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'),
                                                                       'image/jpg') } },
                  format: :json
    assert_response :created
  end

  test 'index works properly showing only relevant data' do
    get :index, format: :json
    assert_equal User.internal.count, json_body.count
  end

  test 'index works properly for department chief' do
    user = users(:mikolaj)
    authenticate(user)

    get :index, format: :json

    assert_equal User.internal.active.where(
      department_id: user.departments_as_chief_or_substitute_chief.ids
    ).count, json_body.count
  end

  test 'index works properly with the department filter' do
    department = departments(:two)

    get :index, format: :json, params: { f: { departments_ids: [department.id] } }

    assert_response :success
    assert_equal department.users.count, json_body.count
  end

  test 'show returns only specific bios' do
    get :show, params: { id: @bio.user_id }, format: :json
    assert_equal json_body["id"], @user.id
  end
end
