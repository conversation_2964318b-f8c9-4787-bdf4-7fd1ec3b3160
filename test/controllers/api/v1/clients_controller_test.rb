require 'test_helper'

module Api
  module V1
    class ClientsControllerTest < ActionController::TestCase
      setup do
        @user = users(:w<PERSON><PERSON>)
        authenticate(@user)
        @valid_params = {
          name: 'Efigence',
          street: 'W<PERSON>łoska',
          street_number: '9a',
          city: 'Warszawa',
          post: 'Warszawa',
          voivodeship: 'mazowieckie',
          district: 'Warszawa',
          community: 'Mokotów',
          postcode: '00-999',
          country: 'pl',
          vat_number: '12234PL',
          invoice_sending_method: 'paper'
        }
      end

      test 'client is created given valid params' do
        assert_difference('Client.count') do
          post :create, params: { client: @valid_params }, format: :json
        end

        assert_response :created
        assert_equal @valid_params[:name], json_body['name']
        assert_equal @valid_params[:city], json_body['city']
        assert_equal 'active', json_body['state']
      end

      test 'client is created given valid params - auth as normal user' do
        user = users(:internal_user)
        user.global_roles << global_roles(:global_pm)
        authenticate(user)

        assert_difference('Client.count') do
          post :create, params: { client: @valid_params }, format: :json
        end

        assert_response :created
        assert_equal @valid_params[:name], json_body['name']
        assert_equal @valid_params[:city], json_body['city']
        assert_equal 'pending', json_body['state']
      end

      test 'validation fails properly on create' do
        post :create, params: { client: @valid_params.merge(name: '') }, format: :json
        assert_response :unprocessable_entity
        refute_empty json_body['errors']
      end

      test 'notification mail is sent if client is pending' do
        user = users(:internal_user)
        user.global_roles << global_roles(:global_pm)
        authenticate(user)

        ActiveJob::Base.queue_adapter.enqueued_jobs.clear

        assert_difference('Client.count') do
          post :create, params: { client: @valid_params }, format: :json
        end

        assert_response :created
        assert_equal 1, ActiveJob::Base.queue_adapter.enqueued_jobs.size
        assert_equal @valid_params[:name], json_body['name']
        assert_equal @valid_params[:city], json_body['city']
        assert_equal 'pending', json_body['state']
      end

      test 'notification mail is not sent if client is active' do
        ActiveJob::Base.queue_adapter.enqueued_jobs.clear

        assert_difference('Client.count') do
          post :create, params: { client: @valid_params }, format: :json
        end

        assert_response :created
        assert_equal 0, ActiveJob::Base.queue_adapter.enqueued_jobs.size
        assert_equal @valid_params[:name], json_body['name']
        assert_equal @valid_params[:city], json_body['city']
        assert_equal 'active', json_body['state']
      end

      test 'client is updated properly' do
        client = clients(:arte)
        patch :update, params: { id: client.id, client: { name: 'Efigence' } },
                       format: :json
        assert_response :no_content
        assert_equal 'Efigence', client.reload.name
      end

      test 'validation fails properly on update' do
        client = clients(:arte)
        patch :update, params: { id: client.id, client: { name: '' } }, format: :json
        assert_response :unprocessable_entity
      end

      test 'client is destroyed properly' do
        client = clients(:polexit)
        assert_difference('Client.count', -1) do
          delete :destroy, params: { id: client.id }, format: :json
        end
        assert_response :no_content
      end

      test 'client cannot be destroyed if is assigned to project' do
        client = clients(:arte)
        assert_no_difference('Client.count') do
          delete :destroy, params: { id: client.id }, format: :json
        end
        assert_response :unprocessable_entity
      end

      test 'index works properly showing only relevant data with showPending params' do
        client = clients(:arte)
        get :index, params: { f: { showPending: true } }, format: :json
        assert_equal Client.count, json_body.count
        returned_record = json_body.first
        assert_equal client.name, returned_record['name']
        assert_equal client.id, returned_record['id']
        assert_equal 3, returned_record.keys.count
      end

      test 'index with term filter param filters clients with showPending params' do
        get :index, params: { f: { term: 'Arte', showPending: true } }, format: :json

        assert_response :success
        assert_equal 1, json_body.count
      end

      test 'authorized user should be able to activate client' do
        client = clients(:arte)
        patch :activate, params: { id: client.id }, format: :json

        client.reload

        assert_response :success
        assert_equal 'active', client.state
      end

      test 'not authorized user should not be able to activate client' do
        user = users(:internal_user)
        user.global_roles << global_roles(:global_pm)
        authenticate(user)

        client = clients(:arte)
        patch :activate, params: { id: client.id }, format: :json

        client.reload

        assert_response :forbidden
        assert_equal 'pending', client.state
      end

      test 'index with term filter param filters clients with active client' do
        get :index, params: { f: { term: 'Arte' } }, format: :json

        assert_response :success
        assert_equal 0, json_body.count
        clients(:arte).active!

        get :index, params: { f: { term: 'Arte'} }, format: :json

        assert_response :success
        assert_equal 1, json_body.count
      end
    end
  end
end
