require 'test_helper'
require 'owncloud_provider'

class Api::V1::DocsFilesControllerTest < ActionController::TestCase
  let(:project) { projects(:two) }
  let(:file) do
    Rack::Test::UploadedFile.new(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'),
                                 'image/jpg')
  end
  let(:category) { DocsFile.categories.keys.third }
  let(:valid_attributes) do
    {
      file: file,
      category: category
    }
  end
  let(:user) { users(:wiktoria) }

  setup do
    authenticate(user)
  end

  test 'GET #index' do
    get :index, params: { project_id: project }, format: :json

    assert_response :success
    assert_equal 1, json_body.count
  end

  test 'GET #index with sort by created_at' do
    get :index, params: { f: { sort: 'created_at desc' } }, format: :json

    assert_response :success
    assert_equal 2, json_body.count
    assert_equal docs_files(:one).id, json_body.first['id']
  end

  test 'GET #index with manage_docs_files permission' do
    user.global_roles.destroy(global_roles(:global_accounting))
    memberships(:four_inherited_wiktoria).update(project: project)

    get :index, format: :json

    assert_response :success
    assert_equal 1, json_body.count
  end

  test 'GET #index with project_id filter' do
    get :index, params: { f: { project_id: project.id } }, format: :json

    assert_response :success
    assert_equal 1, json_body.count
  end

  test 'GET #index with category filter' do
    get :index, params: { f: { category: '00. Przetarg NB' } }, format: :json

    assert_response :success
    assert_equal 1, json_body.count
  end

  test 'POST #create success' do
    double = mock('owncloud_provider')
    OwncloudProvider.stubs(:new).returns(double)
    double.expects(:config_valid?).once.returns(true)
    double.expects(:send_file)
          .returns(nil)
    double.expects(:get_project_files).with(project.identifier, category).returns(
      [
        OwncloudProvider::FileInfo.new(3, 'jpg_705kB.jpg')
      ]
    )

    assert_difference -> { DocsFile.count } do
      post :create, params: { project_id: project, docs_file: valid_attributes }, format: :json
    end

    assert_response :created
    docs_file = DocsFile.find(json_body['id'])
    assert_equal user, docs_file.created_by
  end

  test 'POST #create failure' do
    post :create, params: { project_id: project, docs_file: valid_attributes.merge(category: nil) },
                  format: :json

    assert_response :unprocessable_entity
    assert_not_empty json_body['errors']['category']
  end

  test 'GET #new returns categories' do
    get :new, params: { project_id: project }, format: :json

    assert_response :success
    assert_equal json_body['categories'], DocsFile.categories.keys
  end
end
