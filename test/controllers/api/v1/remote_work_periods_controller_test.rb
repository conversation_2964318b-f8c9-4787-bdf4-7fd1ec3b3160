require 'test_helper'

class Api::V1::RemoteWorkPeriodsControllerTest < ActionController::TestCase
  let(:valid_attributes) do
    {
      starts_on: Time.zone.tomorrow,
      ends_on: Time.zone.tomorrow + 1.week,
      place_of_work: 'My place'
    }
  end
  let(:remote_work_period) { remote_work_periods(:milosz_remote_period) }

  test 'index' do
    authenticate users(:wiktoria)

    get :index, format: :json

    assert_response :success
    assert_equal remote_work_period.id, json_body['remote_work_periods'].first['id']
  end

  test 'index with date_from filter' do
    authenticate users(:wiktoria)

    get :index, format: :json, params: { f: { date_from: '2023-06-02' } }

    assert_response :success
    assert_equal 0, json_body['remote_work_periods'].count
  end

  test 'index with date_to filter' do
    authenticate users(:wiktoria)

    get :index, format: :json, params: { f: { date_to: '2023-05-31' } }

    assert_response :success
    assert_equal 0, json_body['remote_work_periods'].count
  end

  test 'index with user filter' do
    authenticate users(:wiktoria)
    user = users(:mikolaj)

    get :index, format: :json, params: { f: { user_id: user } }

    assert_equal 0, json_body['remote_work_periods'].count
    user = users(:milosz)

    get :index, format: :json, params: { f: { user_id: user } }
    assert_equal 1, json_body['remote_work_periods'].count
  end

  test 'index with pending filter' do
    user = users(:wiktoria)
    authenticate(user)

    get :index, format: :json, params: { f: { state: %w[pending] } }

    assert_equal 1, json_body['remote_work_periods'].count

    remote_work_period.accept!(examiner: user)

    get :index, format: :json, params: { f: { state: %w[pending] } }

    assert_equal 0, json_body['remote_work_periods'].count
  end

  test 'index with accepted filter' do
    user = users(:wiktoria)
    authenticate(user)

    get :index, format: :json, params: { f: { state: %w[accepted] } }

    assert_equal 0, json_body['remote_work_periods'].count

    remote_work_period.accept!(examiner: user)

    get :index, format: :json, params: { f: { state: %w[accepted] } }

    assert_equal 1, json_body['remote_work_periods'].count
  end

  test 'index with rejected filter' do
    user = users(:wiktoria)
    authenticate(user)

    get :index, format: :json, params: { f: { state: %w[rejected] } }

    assert_equal 0, json_body['remote_work_periods'].count

    remote_work_period.reject!(examiner: user)

    get :index, format: :json, params: { f: { state: %w[rejected] } }

    assert_equal 1, json_body['remote_work_periods'].count
  end

  test 'index with pending and accepted filter' do
    user = users(:wiktoria)
    authenticate(user)

    get :index, format: :json, params: { f: { state: %w[pending accepted] } }

    assert_equal 1, json_body['remote_work_periods'].count
  end

  test 'index with department_ids filter' do
    user = users(:wiktoria)
    authenticate(user)

    get :index, format: :json,
                params: { f: { department_ids: [users(:milosz).department.id.to_s] } }

    assert_equal 1, json_body['remote_work_periods'].count
  end

  test 'index with my_subordinates department_ids filter' do
    authenticate users(:wiktoria)

    get :index, format: :json, params: { f: { department_ids: %w[my_subordinates] } }

    assert_equal 1, json_body['remote_work_periods'].count
  end

  test 'index with my_teams department_ids filter' do
    authenticate users(:wiktoria)

    get :index, format: :json, params: { f: { department_ids: %w[my_teams] } }

    assert_equal 0, json_body['remote_work_periods'].count
  end

  test 'index with less permissions' do
    authenticate users(:mikolaj)

    get :index, format: :json

    assert_response :success
    assert_equal 0, json_body['remote_work_periods'].count
  end

  test 'report' do
    user = users(:wiktoria)
    remote_work_period.accept!(examiner: user, examiner_comment: 'Comment')
    authenticate user

    get :report, format: :csv, params: { f: { date_from: '2023-06-01', date_to: '2023-06-30' } }

    assert_response :success
    assert_equal remote_work_period.business_days.first.to_fs(:sql),
                 CSV.parse(response.body).second.second
  end

  test 'report with invalid date' do
    authenticate(users(:wiktoria))

    get :report, format: :csv, params: { f: { date_from: '2023', date_to: '2023-06-30' } }

    assert_response :success
  end

  test 'users' do
    authenticate users(:wiktoria)
    user = users(:milosz)

    get :users, params: { year_month: '2023-06' }, format: :json

    assert_response :success
    assert_equal user.id, json_body['users'].first['id']
    assert_not_empty json_body['users'].first['remote_work_periods']
  end

  test 'users for other month' do
    authenticate users(:wiktoria)

    get :users, params: { year_month: '2023-07' }, format: :json

    assert_response :success
    assert_empty json_body['users'].first['remote_work_periods']
  end

  test 'users for invalid month' do
    authenticate users(:wiktoria)

    get :users, params: { year_month: '2023-35' }, format: :json

    assert_response :bad_request
  end

  test 'users with projects filter' do
    authenticate users(:wiktoria)

    get :users, params: { year_month: '2023-06', f: { project_id: projects(:one) } }, format: :json

    assert_not_empty json_body['users'].first['remote_work_periods']

    @controller = Api::V1::RemoteWorkPeriodsController.new

    get :users, params: { year_month: '2023-06', f: { project_id: projects(:two) } }, format: :json

    assert_empty json_body['users']
  end

  test 'users with pending filter' do
    user = users(:wiktoria)
    authenticate user

    get :users, params: { year_month: '2023-06', f: { state: ['pending'] } }, format: :json

    assert_not_empty json_body['users'].first['remote_work_periods']

    remote_work_period.accept!(examiner: user)

    get :users, params: { year_month: '2023-06', f: { state: ['pending'] } }, format: :json

    assert_empty json_body['users'].first['remote_work_periods']
  end

  test 'users with accepted filter' do
    user = users(:wiktoria)
    authenticate user

    get :users, params: { year_month: '2023-06', f: { state: ['accepted'] } }, format: :json

    assert_empty json_body['users'].first['remote_work_periods']

    remote_work_period.accept!(examiner: user)

    get :users, params: { year_month: '2023-06', f: { state: ['accepted'] } }, format: :json

    assert_not_empty json_body['users'].first['remote_work_periods']
  end

  test 'users with rejected filter' do
    user = users(:wiktoria)
    authenticate user

    get :users, params: { year_month: '2023-06', f: { state: ['rejected'] } }, format: :json

    assert_empty json_body['users'].first['remote_work_periods']

    remote_work_period.reject!(examiner: user)

    get :users, params: { year_month: '2023-06', f: { state: ['rejected'] } }, format: :json

    assert_not_empty json_body['users'].first['remote_work_periods']
  end

  test 'users with pending and accepted filter' do
    user = users(:wiktoria)
    authenticate user

    get :users, params: { year_month: '2023-06', f: { state: %w[pending accepted] } }, format: :json

    assert_not_empty json_body['users'].first['remote_work_periods']

    remote_work_period.accept!(examiner: user)

    get :users, params: { year_month: '2023-06', f: { state: %w[pending accepted] } }, format: :json

    assert_not_empty json_body['users'].first['remote_work_periods']
  end

  test 'users with department filter' do
    authenticate users(:wiktoria)

    get :users, params: { year_month: '2023-06', f: { departments_ids: [departments(:two)] } },
                format: :json

    assert_not_empty json_body['users'].first['remote_work_periods']

    @controller = Api::V1::RemoteWorkPeriodsController.new

    get :users, params: { year_month: '2023-06', f: { departments_ids: [departments(:one)] } },
                format: :json

    assert_empty json_body['users']
  end

  test 'users with my_teams filter' do
    authenticate users(:wiktoria)

    get :users, params: { year_month: '2023-06', f: { department_ids: ['my_teams'] } },
                format: :json

    assert_empty json_body['users'].first['remote_work_periods']
  end

  test 'users with my_subordinates filter' do
    authenticate users(:wiktoria)

    get :users, params: { year_month: '2023-06', f: { department_ids: ['my_subordinates'] } },
                format: :json

    assert_not_empty json_body['users'].first['remote_work_periods']
  end

  test 'users returns holidays' do
    authenticate users(:wiktoria)

    get :users, params: { year_month: '2023-06' }, format: :json

    assert_not_empty json_body['holidays']
    assert_includes json_body['holidays'], '2023-06-24'
  end

  test 'users for lower permissions' do
    authenticate users(:mikolaj)

    get :users, params: { year_month: '2023-06' }, format: :json

    assert_empty json_body['users'].first['remote_work_periods']
  end

  test 'create' do
    user = users(:milosz)
    authenticate user

    assert_difference -> { user.remote_work_periods.count } do
      post :create, params: { remote_work_period: valid_attributes }, format: :json
    end

    assert_response :created
  end

  test 'create sends email' do
    user = users(:milosz)
    authenticate(user)

    assert_difference -> { RemoteWorkPeriodMailer.deliveries.count } do
      perform_enqueued_jobs do
        post :create, params: { remote_work_period: valid_attributes }, format: :json
      end
    end

    email = RemoteWorkPeriodMailer.deliveries.last
    assert_equal I18n.t('mailers.remote_work_period_mailer.remote_work_period_created.subject',
                        user_name: user.full_name), email.subject

  end

  test 'create for another user' do
    user = users(:milosz)
    authenticate users(:wiktoria)

    assert_difference -> { user.remote_work_periods.count } do
      post :create, params: { remote_work_period: valid_attributes.merge(user_id: user) },
                    format: :json
    end

    assert_response :created
  end

  test 'create for another user as regular user' do
    user = users(:mikolaj)
    author = users(:milosz)
    user.update(contract_of_employment: true, remote_allowed: true, remote_yearly_limit: 10)
    authenticate author

    assert_no_difference -> { user.remote_work_periods.count } do
      assert_difference -> { author.remote_work_periods.count } do
        post :create, params: { remote_work_period: valid_attributes.merge(user_id: user) },
                      format: :json
      end
    end

    assert_response :created
  end

  test 'new for low-privileged' do
    authenticate users(:milosz)

    get :new, format: :json

    assert_response :success
    assert_not json_body['global_create']
  end

  test 'new for high-privileged' do
    authenticate users(:wiktoria)

    get :new, format: :json

    assert_response :success
    assert json_body['global_create']
  end

  test 'accept' do
    authenticate users(:wiktoria)

    patch :accept, params: { id: remote_work_period, examiner_comment: 'Comment' }, format: :json

    assert_response :no_content
    assert remote_work_period.reload.accepted?
  end

  test 'accept sends email' do
    authenticate users(:wiktoria)

    assert_difference -> { RemoteWorkPeriodMailer.deliveries.count } do
      perform_enqueued_jobs do
        patch :accept, params: { id: remote_work_period, examiner_comment: 'Comment' }, format: :json
      end
    end

    email = RemoteWorkPeriodMailer.deliveries.last
    assert_equal I18n.t('mailers.remote_work_period_mailer.remote_work_period_accepted.subject'), email.subject
  end

  test 'failed acceptation' do
    authenticate users(:wiktoria)
    remote_work_period.user.update_column(:remote_yearly_limit, 0)

    patch :accept, params: { id: remote_work_period, examiner_comment: 'Comment' }, format: :json

    assert_response :unprocessable_entity
    assert_not_empty json_body['errors']['starts_on']
  end

  test 'forced acceptation' do
    authenticate users(:wiktoria)
    remote_work_period.user.update_column(:remote_yearly_limit, 0)

    patch :accept, params: { id: remote_work_period, examiner_comment: 'Comment', force: true },
                   format: :json

    assert_response :no_content
    assert remote_work_period.reload.accepted?
  end

  test 'reject' do
    authenticate users(:wiktoria)
    patch :reject, params: { id: remote_work_period }, format: :json

    assert_response :no_content
    assert remote_work_period.reload.rejected?
  end

  test 'reject sends email' do
    authenticate users(:wiktoria)

    assert_difference -> { RemoteWorkPeriodMailer.deliveries.count } do
      perform_enqueued_jobs do
        patch :reject, params: { id: remote_work_period }, format: :json
      end
    end

    email = RemoteWorkPeriodMailer.deliveries.last
    assert_equal I18n.t('mailers.remote_work_period_mailer.remote_work_period_rejected.subject'), email.subject
  end

  test 'show' do
    authenticate users(:milosz)

    get :show, params: { id: remote_work_period }, format: :json

    assert_response :success
    assert_equal remote_work_period.id, json_body['id']
  end

  test 'update' do
    authenticate users(:wiktoria)
    new_ends_on = Date.new(2023, 6, 5)

    patch :update, params: { id: remote_work_period, remote_work_period: { ends_on: new_ends_on } },
                   format: :json

    assert_response :no_content
    assert_equal new_ends_on, remote_work_period.reload.ends_on
  end

  test 'update for unprivileged user' do
    new_ends_on = Date.new(2023, 6, 5)
    authenticate remote_work_period.user
    remote_work_period.accept!(examiner: users(:wiktoria))

    patch :update, params: { id: remote_work_period, remote_work_period: { ends_on: new_ends_on } },
                   format: :json

    assert_response :forbidden
  end

  test 'destroy' do
    authenticate users(:wiktoria)

    assert_difference -> { RemoteWorkPeriod.count }, -1 do
      delete :destroy, params: { id: remote_work_period }, format: :json
    end

    assert_response :no_content
  end

  test 'destroy for unprivileged user' do
    authenticate remote_work_period.user
    remote_work_period.accept!(examiner: users(:wiktoria))

    assert_no_difference -> { RemoteWorkPeriod.count }, -1 do
      delete :destroy, params: { id: remote_work_period }, format: :json
    end

    assert_response :forbidden
  end

  test 'history' do
    user = users(:mkalita_global_admin_programmer)
    authenticate user
    remote_work_period.accept!(examiner: user, examiner_comment: 'Comment')

    get :history, params: { id: remote_work_period }, format: :json

    assert_response :success
    assert_equal 1, json_body.count
    assert_not_empty json_body.first['changeset']
  end
end
