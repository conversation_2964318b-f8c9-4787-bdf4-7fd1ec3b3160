require 'test_helper'

class Api::V1::RefreshAccountControllerTest < ActionController::TestCase
  test 'sets last_sign_in_at to present date' do
    travel_to Time.zone.now
    user = users(:external_user)
    token = SecureRandom.urlsafe_base64
    user.update(current_sign_in_at: 1.year.ago, activation_token: token)

    patch :perform, params: { token: token }, format: :json

    assert_response :no_content
    assert_equal Time.zone.now, user.reload.current_sign_in_at
  end

  test 'returns 404 in case of invalid token' do
    token = SecureRandom.urlsafe_base64

    patch :perform, params: { token: token }, format: :json

    assert_response :not_found
  end
end
