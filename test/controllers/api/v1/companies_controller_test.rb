require 'test_helper'

# debug sql:
# tail -n 20000 -f log/test.log | grep -A40 ": test_create_global_company_ids"
class Api::V1::CompaniesControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  fixtures :companies

  def company
    @company ||= companies(:mkalita_company)
    # http://chriskottom.com/blog/2014/10/4-fantastic-ways-to-set-up-state-in-minitest/
    # HOWTO: test specific version
    # @request.headers['Accept'] = 'application/vnd.api+json; version=2'
    # @request.headers['Content-Type'] = 'application/vnd.api+json; version=2' # for sending data via POST etc.
    # or
    # post '/humans',
    #      { human: { name: 'John', brain_type: 'small' } }.to_json,
    #      { 'Accept' => 'application/vnd.api+json; version=2',
    #        'Content-Type' => 'application/vnd.api+json; version=2' }
  end

  def test_rescues_module_inclusion
    assert_includes Api::V1::CompaniesController.ancestors, Api::V1::Concerns::Handlers::Rescues
  end

  def test_index
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:companies).size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_as_regular_user
    @request.headers['X-Swagger-Sign-In-As'] = users(:milosz).id.to_s

    get :index, format: :json

    assert_response :success
    assert_equal Company.native.order(id: :asc).pluck(:id),
                 json_body.map { |company| company['id'].to_i }.sort
  end

  def test_index_page_2
    user_on_first_page = Company.order('id DESC').first!
    get :index, format: :json, params: { per_page: 1, page: 2 }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.last['id'].to_i > 0
    assert json_body.last['id'].to_i != user_on_first_page.id
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_term
    get :index, format: :json, params: { f: { term: 'efi' } }

    assert_response :success
    assert_equal 1, json_body.count
  end

  def test_index_with_created_after
    company = companies(:one)
    company.update(created_at: 1.year.ago)

    get :index, format: :json, params: { f: { created_after: 1.week.ago.to_date } }

    assert_response :success
    assert_equal Company.where('companies.created_at > ?', 1.week.ago).count, json_body.count
  end

  def test_index_with_native_filter
    get :index, format: :json, params: { f: { native: 'true' } }

    assert_response :success
    assert_equal Company.native.count, json_body.count
  end

  def test_index_with_created_before
    company = companies(:one)
    company.update(created_at: 1.year.ago)

    get :index, format: :json, params: { f: { created_before: 1.week.ago.to_date } }

    assert_response :success
    assert_equal 1, json_body.count
    assert_equal company.name, json_body.first['name']
  end

  def test_create
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    assert_difference('Company.count') do
      post :create, params: { company: {
        name: company.name + '_test_create',
        domain: company.domain + '_test_create'
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    assert_equal company_url(Company.find(json_body['id'])), response.location
  end

  def test_show
    get :show, params: { id: company }, format: :json
    assert_response :success, @response.body.to_s
    assert_includes json_body, 'url'
  end

  def test_update
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    put :update, params: { id: company, company: {
      name: company.name
    } }, format: :json
    assert_response 204, @response.body.to_s
  end

  def test_destroy
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    assert_difference('Company.count', -1) do
      delete :destroy, format: :json, params: { id: company }
      assert_response 204, @response.body.to_s
    end
  end

  def test_index_authorization
    user = users(:mkalita_user)
    policy = stub(index?: false)
    CompanyPolicy.stubs(:new).with(user, Company).returns(policy)
    sign_in(user)
    get :index, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_show_authorization
    user = users(:mkalita_user)
    company = companies(:mkalita_company)
    policy = stub(show?: false)
    CompanyPolicy.stubs(:new).with(user, company).returns(policy)
    sign_in(user)
    get :show, params: { id: company.id }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_create_authorization
    user = users(:mkalita_user)
    policy = stub(create?: false)
    CompanyPolicy.stubs(:new).with(user, Company).returns(policy)
    sign_in(user)
    post :create, params: { company: { name: 'Rola1', domain: 'domain.com' } }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_update_authorization
    user = users(:mkalita_user)
    company = companies(:mkalita_company)
    policy = stub(update?: false)
    CompanyPolicy.stubs(:new).with(user, company).returns(policy)
    sign_in(user)
    patch :update, params: { id: company.id, company: { name: 'Dev1' } }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_destroy_authorization
    user = users(:mkalita_user)
    company = companies(:mkalita_company)
    policy = stub(destroy?: false)
    CompanyPolicy.stubs(:new).with(user, company).returns(policy)
    sign_in(user)
    delete :destroy, params: { id: company.id }, format: :json
    assert_response 403, @response.body.to_s
  end
end
