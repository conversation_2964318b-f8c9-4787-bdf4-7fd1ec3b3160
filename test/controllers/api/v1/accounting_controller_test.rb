require 'test_helper'

module Api
  module V1
    class AccountingControllerTest < ActionController::TestCase
      setup do
        @user = users(:wik<PERSON>)
        authenticate(@user)
        recalculate_payments
      end

      test 'index returns company summary' do
        get :index,
            params: { f: { date_from: Time.zone.today.beginning_of_month, date_to: Time.zone.today.end_of_month } },
            format: :json
        assert_response :success

        assert_equal 2, json_body['companies'].count
      end

      test 'client search filter works properly' do
        client = clients(:arte)
        get :index, params: { f: { of_client: client,
                              date_from: Time.zone.today.beginning_of_month, date_to: Time.zone.today.end_of_month } },
                    format: :json
        assert_response :success
        assert_equal 2, json_body['companies'].count
      end

      test 'responsible search filter works properly' do
        get :index, params: { f: { of_responsible: @user,
                              date_from: Time.zone.today.beginning_of_month, date_to: Time.zone.today.end_of_month } },
                    format: :json
        assert_response :success
      end

      test 'company filter works properly' do
        company = companies(:one)

        get :index, params: { f: { of_company: company,
                              date_from: Time.zone.today.beginning_of_month, date_to: Time.zone.today.end_of_month } },
                    format: :json
        assert_response :success
      end

      test 'mpk_number_ids filter works properly' do
        client = Client.first
        project = client.projects.joins(:payments).first
        company = project.company
        mpk_number_id = project.payments.first.mpk_positions.first.mpk_number_id

        get :index, params: { f: { mpk_number_ids: mpk_number_id,
                              date_from: Time.zone.today.beginning_of_month, date_to: Time.zone.today.end_of_month } },
                    format: :json

        assert_response :success

        company_ids = json_body['companies'].map { |x| x['company']['id'] }
        client_ids = json_body['clients'].map { |x| x['client_id'] }

        assert_contains company_ids, company.id
        assert_contains client_ids, client.id
      end

      test 'accounting_number_ids filter works properly' do
        accounting_number = accounting_numbers(:two)
        project = projects(:two)
        project.update(accounting_number: accounting_number)

        get :index, params: { f: { accounting_number_ids: [accounting_number.id],
                              date_from: Time.zone.today.beginning_of_month, date_to: Time.zone.today.end_of_month } },
                    format: :json

        assert_response :success
        assert_equal 1, json_body['companies'].count
        assert_equal 1, json_body['clients'].count
      end

      test 'show, invoices_summary, mpk_summary and payments_per_month' do
        project = projects(:two)

        project.update(created_at: 6.months.ago)

        payment_one = project.payments.joins(:invoices).first
        payment_two = project.payments.joins(:invoices).last

        payment_one.mpk_positions.first.update!(mpk_number_id: MpkNumber.last.id)

        project.payments.where.not(id: [payment_one.id, payment_two.id]).destroy_all

        invoice_one = payment_one.invoices.first
        invoice_two = payment_two.invoices.first

        payment_one.update_column(:issued_on, 3.months.ago)
        payment_one.update_column(:predicted_amount, 301)
        payment_one.mpk_positions.delete_all

        payment_two.update_column(:issued_on, 3.months.ago)
        payment_two.update_column(:predicted_amount, 402)
        payment_two.mpk_positions.first.update_column(:amount, 402)

        invoice_one.update_column(:issued_at, 1.month.ago)
        invoice_two.update_column(:issued_at, 2.months.ago)

        recalculate_payments

        get :show, params: { id: project }, format: :json
        assert_response :success

        assert_equal 500, json_body['payments_per_month'].first['scheduled_payments'].first['amount']

        invoice_summary_scheduled = json_body['invoices_summary'].map { |x| x['invoices'].first['scheduled_payments'] }

        assert_contains invoice_summary_scheduled, 500
        assert_contains invoice_summary_scheduled, 402

        assert_equal (301 + 402 + 500), json_body['payments_summary'].first['scheduled_payments']
      end

      test 'client_projects work properly' do
        client = clients(:arte)
        get :client_projects, params: { id: client }, format: :json

        assert_response :success
        assert_equal client.projects.for_accounting(assigns(:date_from),
                                                    assigns(:date_to)).to_a.size,
                     json_body['projects'].count
      end

      test 'client_projects with accounting_number_ids filter works properly' do
        accounting_number = accounting_numbers(:two)
        project = projects(:one)
        project.update(accounting_number: accounting_number)
        client = clients(:arte)

        get :client_projects, params: { id: client, f: { accounting_number_ids: [accounting_number.id] } }, format: :json

        assert_response :success
        assert_equal [project.id], json_body['projects'].pluck('project_id')
      end

      test 'payments_report' do
        today = Time.zone.today
        get :payments_report, params: { f: { date_from: today.beginning_of_month,
                                             date_to: today.end_of_month } },
                              format: :xlsx

        assert_response :success
      end

      private

      def recalculate_payments
        Payment.find_each { |payment| payment.send :recalculate_scheduled_payments }
        Invoice.where(state: %i[pending accepted issued]).find_each { |invoice| invoice.send :recalculate_payments }
      end
    end
  end
end
