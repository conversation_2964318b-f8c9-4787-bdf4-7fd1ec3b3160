require "test_helper"

module Api
  module V1
    class MyPaymentSchedulesControllerTest < ActionController::TestCase
      test 'when user does not have `invoices:index` activity it returns 403' do
        user = users(:internal_user)
        authenticate(user)
        get :index, format: :json
        assert_response :forbidden
      end

      test 'when user is global admin programmer he gets all results' do
        user = users(:mkalita_global_admin_programmer)
        authenticate(user)
        get :index, format: :json
        assert json_body.is_a?(Array)
        refute_empty json_body
      end

      test 'does not have results with invoice' do
        user = users(:mkalita_global_admin_programmer)
        authenticate(user)
        get :index, format: :json

        payments = json_body.map do |project|
          project['payments']
        end.flatten
        payment_ids = payments.map { |payment| payment['id'] }
        assert Invoice.where(payment_id: payment_ids).empty?
      end

      test 'when user has accounting global role he gets all results' do
        user = users(:wiktoria)
        authenticate(user)
        get :index, format: :json
        assert json_body.is_a?(Array)
        refute_empty json_body
      end

      test 'when user is a board member he gets all results' do
        user = users(:internal_user)
        user.update_columns(department_id: departments(:board_member_department).id)
        authenticate(user)
        get :index, format: :json
        assert json_body.is_a?(Array)
        refute_empty json_body
      end

      test 'when user is a regular PM, he gets scoped results' do
        user = users(:wiktoria)
        user.global_roles = [global_roles(:global_pm)]
        user.save

        authenticate(user)

        get :index, format: :json
        assert json_body.is_a?(Array)
        assert_empty json_body
      end

      test 'with of_project filter' do
        user = users(:mkalita_global_admin_programmer)
        authenticate(user)

        get :index, params: { f: { of_project: projects(:one) } }, format: :json

        assert_response :success
        assert_equal 1, json_body.count
        assert_equal projects(:one).payment_schedule.id, json_body.first['id']
      end

      test 'with date_from filter' do
        user = users(:wiktoria)
        authenticate(user)
        payments(:five).invoices.destroy_all

        get :index, params: { f: { date_from: 2.weeks.from_now.to_date } }, format: :json

        assert_response :success
        assert_equal 1, json_body.count
        assert_equal payment_schedules(:project_two_schedule).id, json_body.first['id']
      end

      test 'with date_to filter' do
        user = users(:wiktoria)
        authenticate(user)
        payment = payments(:three)
        payment.invoices.destroy_all

        get :index, params: { f: { date_to: 2.weeks.ago.to_date } }, format: :json

        assert_response :success
        payment_schedule = json_body.detect do |ps|
          ps['id'] == payment_schedules(:project_two_schedule).id
        end
        assert_equal 1, payment_schedule['payments'].count
        assert_equal payment.id, payment_schedule['payments'].first['id']
      end

      test 'with of_company filter' do
        user = users(:wiktoria)
        authenticate(user)
        get :index, params: { f: { of_company: companies(:three) } }, format: :json

        assert_response :success
        assert_equal 1, json_body.count
        assert_equal payment_schedules(:project_three_schedule).id, json_body.first['id']
      end
    end
  end
end
