require "test_helper"

module Api
  module V1
    class ApiKeysControllerTest < ActionController::TestCase
      include Minitest::XSwaggerSignInAs

      fixtures :api_keys

      test 'shows all api_keys' do
        get :index, format: :json
        assert_response :success, @response.body.to_s
        assert_equal json_body.size, assigns(:api_keys).size
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      test 'shows only filtered api keys' do
        get :index, params: { f: { term: 'on' } }, format: :json
        assert_equal json_body.size, 1
        assert_equal json_body.first['name'], 'One'
      end

      test 'shows only 1 page of api keys' do
        get :index, format: :json, params: { per_page: 1 }
        assert_response :success, @response.body.to_s
        refute json_body.empty?
        assert_equal json_body.size, 1
      end

      test 'creates api_key' do
        assert_difference('ApiKey.count', 1) do
          post :create, params: { api_key: { name: 'Test api key' } }, format: :json
        end
        assert_response 201, @response.body.to_s
      end

      test 'does not create api key without name' do
        assert_difference('ApiKey.count', 0) do
          post :create, params: { api_key: { expires_on: Time.now.end_of_month } }, format: :json
        end
        assert_response 422, @response.body.to_s
      end

      test 'destroys pointed api_key' do
        api_key = api_keys(:key)
        assert_difference('ApiKey.count', -1) do
          delete :destroy, params: { id: api_key }, format: :json
          assert_response 204, @response.body.to_s
        end
      end
    end
  end
end
