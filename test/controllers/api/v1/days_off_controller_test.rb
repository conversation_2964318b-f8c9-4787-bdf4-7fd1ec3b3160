require 'test_helper'

module Api
  module V1
    class DaysOffControllerTest < ActionController::TestCase
      test 'index with insufficient params returns 400' do
        get :index, format: :json

        assert_response(:bad_request)
      end

      test 'index with proper params returns list of days off in chronological order' do
        get :index, params: { date_from: '2022-11-01', date_to: '2022-11-20' }, format: :json

        assert_response :success
        days_off = json_body['days_off']
        assert_equal %w[2022-11-01 2022-11-05 2022-11-06 2022-11-11 2022-11-12 2022-11-13 2022-11-19
                        2022-11-20], days_off
      end
    end
  end
end
