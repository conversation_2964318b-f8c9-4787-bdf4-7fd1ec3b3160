require "test_helper"

module Api
  module V1
    class MpkNumbersControllerTest < ActionController::TestCase
      include Minitest::XSwaggerSignInAs

      fixtures :mpk_numbers

      test 'show all mpk numbers' do
        get :index, format: :json
        assert_response :success, @response.body.to_s
        assert_equal json_body.size, assigns(:mpk_numbers).size
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end
    end
  end
end
