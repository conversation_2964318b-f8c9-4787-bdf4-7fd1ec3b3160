require 'test_helper'

module Api
  module V1
    class DeletionRequestsControllerTest < ActionController::TestCase
      let(:admin_user) { users(:mkalita_user) }
      let(:regular_user) { users(:milosz) }
      let(:deletion_request) { deletion_requests(:registered_request) }
      let(:processing_request) { deletion_requests(:processing_request) }
      let(:completed_request) { deletion_requests(:completed_request) }
      let(:company) { companies(:two) }
      let(:valid_attributes) do
        {
          full_name: 'Test User',
          description: 'Test deletion request',
          taken_actions: 'Removed from all systems',
          company_id: company.id,
          submission_date: Date.current,
          comment: 'Test comment'
        }
      end

      test 'index returns success for authorized user' do
        authenticate(admin_user)

        get :index, format: :json

        assert_response :success
        assert_not_nil assigns(:deletion_requests)
        assert_operator json_body.size, :>=, 1
        assert_includes json_body.map { |r| r['id'] }, deletion_request.id
      end

      test 'index returns unauthorized for regular user' do
        authenticate(regular_user)

        get :index, format: :json

        assert_response :forbidden
      end

      test 'index with search filters' do
        authenticate(admin_user)

        get :index, params: { f: { term: '<PERSON> Doe' } }, format: :json

        assert_response :success
        json_body&.each do |r|
          assert_includes r['description'], 'John Doe'
        end
      end

      test 'index with company filter' do
        authenticate(admin_user)

        get :index, params: { f: { company_id: company.id } }, format: :json

        assert_response :success
        json_body&.each do |r|
          assert_equal r['company'], company.name
        end
      end

      test 'index with sorting' do
        authenticate(admin_user)

        get :index, params: { f: { sort: 'submission_date desc' } }, format: :json

        assert_response :success
        assert_not_empty json_body
      end

      test 'show returns success for authorized user' do
        authenticate(admin_user)

        get :show, params: { id: deletion_request.id }, format: :json

        assert_response :success
        assert_equal deletion_request.id, json_body['deletion_request']['id']
        assert_equal deletion_request.full_name, json_body['deletion_request']['full_name']
        assert_equal deletion_request.description, json_body['deletion_request']['description']
        assert_equal deletion_request.taken_actions, json_body['deletion_request']['taken_actions']
        assert_equal deletion_request.company_id, json_body['deletion_request']['company_id']
        assert_equal deletion_request.submission_date.to_s, json_body['deletion_request']['submission_date']
        assert_equal deletion_request.comment, json_body['deletion_request']['comment']
        assert_equal deletion_request.state, json_body['deletion_request']['state']
        assert_equal deletion_request.created_by.full_name, json_body['deletion_request']['creator']
      end

      test 'show returns unauthorized for regular user' do
        authenticate(regular_user)

        get :show, params: { id: deletion_request.id }, format: :json

        assert_response :forbidden
      end

      test 'show returns not found for non-existent record' do
        authenticate(admin_user)

        get :show, params: { id: 999999 }, format: :json

        assert_response :not_found
        assert_not_empty json_body['errors']
      end

      test 'create returns success with valid attributes' do
        authenticate(admin_user)

        assert_difference('DeletionRequest.count') do
          post :create, params: { deletion_request: valid_attributes }, format: :json
        end

        assert_response :success
        created_request = DeletionRequest.find(json_body['id'])
        assert_equal admin_user.id, created_request.created_by_id
        assert_equal valid_attributes[:full_name], created_request.full_name
        assert_equal valid_attributes[:description], created_request.description
        assert_equal valid_attributes[:taken_actions], created_request.taken_actions
        assert_equal valid_attributes[:company_id], created_request.company_id
        assert_equal valid_attributes[:submission_date], created_request.submission_date
        assert_equal valid_attributes[:comment], created_request.comment
      end

      test 'create returns unauthorized for regular user' do
        authenticate(regular_user)

        assert_no_difference('DeletionRequest.count') do
          post :create, params: { deletion_request: valid_attributes }, format: :json
        end

        assert_response :forbidden
      end

      test 'create returns unprocessable entity with invalid attributes' do
        authenticate(admin_user)

        assert_no_difference('DeletionRequest.count') do
          post :create, params: { deletion_request: valid_attributes.merge(taken_actions: '') }, format: :json
        end

        assert_response :unprocessable_entity
        assert_not_empty json_body['errors']['taken_actions']
      end

      test 'create with project_ids associates projects' do
        authenticate(admin_user)

        projects(:one).save
        projects(:two).save
        project_ids = [projects(:one).id, projects(:two).id]

        assert_difference('DeletionRequest.count') do
          post :create, params: { deletion_request: valid_attributes.merge(project_ids: project_ids) }, format: :json
        end

        assert_response :success
        created_request = DeletionRequest.find(json_body['id'])
        assert_equal 2, created_request.projects.count
        assert_includes created_request.project_ids, projects(:one).id
        assert_includes created_request.project_ids, projects(:two).id
      end

      test 'update returns success with valid attributes' do
        authenticate(admin_user)
        new_description = 'Updated description'

        patch :update, params: { id: deletion_request.id, deletion_request: { description: new_description } }, format: :json

        assert_response :success
        assert_equal new_description, deletion_request.reload.description
      end

      test 'update returns unauthorized for regular user' do
        authenticate(regular_user)

        patch :update, params: { id: deletion_request.id, deletion_request: { description: 'Updated' } }, format: :json

        assert_response :forbidden
      end

      test 'update returns unprocessable entity with invalid attributes' do
        authenticate(admin_user)

        patch :update, params: { id: deletion_request.id, deletion_request: { taken_actions: '' } }, format: :json

        assert_response :unprocessable_entity
        assert_not_empty json_body['errors']['taken_actions']
      end

      test 'destroy returns success and deletes record' do
        authenticate(admin_user)

        assert_difference('DeletionRequest.count', -1) do
          delete :destroy, params: { id: deletion_request.id }, format: :json
        end

        assert_response :no_content
      end

      test 'destroy returns unauthorized for regular user' do
        authenticate(regular_user)

        assert_no_difference('DeletionRequest.count') do
          delete :destroy, params: { id: deletion_request.id }, format: :json
        end

        assert_response :forbidden
      end

      test 'processing transitions state from registered to processing' do
        authenticate(admin_user)
        assert_equal 'registered', deletion_request.state

        patch :processing, params: { id: deletion_request.id }, format: :json

        assert_response :no_content
        deletion_request.reload
        assert_equal 'processing', deletion_request.state
      end

      test 'processing returns unauthorized for regular user' do
        authenticate(regular_user)

        patch :processing, params: { id: deletion_request.id }, format: :json

        assert_response :forbidden
      end

      test 'processing returns error for invalid state transition' do
        authenticate(admin_user)

        patch :processing, params: { id: completed_request.id }, format: :json

        assert_response :unprocessable_entity
        assert_not_empty json_body['errors']
      end

      test 'complete transitions state from processing to completed' do
        authenticate(admin_user)
        assert_equal 'processing', processing_request.state

        patch :complete, params: { id: processing_request.id }, format: :json

        assert_response :no_content
        processing_request.reload
        assert_equal 'completed', processing_request.state
      end

      test 'complete returns unauthorized for regular user' do
        authenticate(regular_user)

        patch :complete, params: { id: processing_request.id }, format: :json

        assert_response :forbidden
      end
    end
  end
end
