require 'test_helper'

class Api::V1::DepartmentMembersControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  fixtures :departments

  setup do
    @department = departments(:two)
  end

  test 'show users in departments on index' do
    get :index, format: :json, params: { department_id: @department.id }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, @department.users.count
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  test 'should get index with filters' do
    get :index, format: :json, params: { f: { term: 'a' }, per_page: 1,
                                         department_id: @department.id }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, 1
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end
end
