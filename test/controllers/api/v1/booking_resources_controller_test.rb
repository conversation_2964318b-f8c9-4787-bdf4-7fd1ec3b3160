require "test_helper"

module Api
  module V1
    class BookingResourcesControllerTest < ActionController::TestCase
      include Minitest::XSwaggerSignInAs

      fixtures :booking_resources

      test 'shows all booking_resources' do
        get :index, format: :json
        assert_response :success, @response.body.to_s
        assert_equal json_body.size, assigns(:booking_resources).size
        assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
      end

      test 'shows only filtered booking_resources' do
        get :index, params: { f: { term: 'sma' } }, format: :json
        assert_equal json_body.size, 1
        assert_equal json_body.first['name'], 'Small Room'
      end

      test 'creates booking_resource' do
        assert_difference('BookingResource.count', 1) do
          post :create, params: {
            booking_resource: { name: 'Valid Resource', identifier: 'valid-resource', kind: 'br_location', multiple_bookings: 2, email: '<EMAIL>' }
          }, format: :json
        end
        assert_response 201, @response.body.to_s
      end

      test 'does not create booking_resource with bad email' do
        assert_difference('BookingResource.count', 0) do
          post :create, params: { booking_resource: { name: 'Valid Resource',
                                                      kind: 'br_location',
                                                      multiple_bookings: 2,
                                                      email: 'aaaamail' } },
                        format: :json
        end
        assert_response 422, @response.body.to_s
      end

      test 'does not create booking_resource with negative multiple_bookings' do
        assert_difference('BookingResource.count', 0) do
          post :create, params: { booking_resource: { name: 'Valid Resource',
                                                      identifier: 'valid-resource',
                                                      kind: 'br_location',
                                                      multiple_bookings: -2,
                                                      email: '<EMAIL>' } },
                        format: :json
        end
        assert_response 422, @response.body.to_s
      end

      test 'updates pointed booking_resource' do
        booking_resource = booking_resources(:big_conference_room)
        put :update, params: { id: booking_resource,
                               booking_resource: { name: 'New resource name' } },
                     format: :json
        booking_resource.reload
        assert_equal 'New resource name', booking_resource.name
        assert_response 204, @response.body.to_s
      end

      test 'does not update pointed booking_resource with bad mail' do
        booking_resource = booking_resources(:small_meeting_room)
        put :update, params: { id: booking_resource,
                               booking_resource: { email: 'aaamail' } }, format: :json
        booking_resource.reload
        assert_response 422, @response.body.to_s
      end

      test 'does not update pointed booking_resource with negative multiple_bookings' do
        booking_resource = booking_resources(:small_meeting_room)
        put :update, params: { id: booking_resource,
                               booking_resource: { multiple_bookings: -2 } }, format: :json
        booking_resource.reload
        assert_response 422, @response.body.to_s
      end

      test 'shows pointed booking_resource' do
        booking_resource = booking_resources(:big_conference_room)
        get :show, params: { id: booking_resource }, format: :json
        assert_response 200, @response.body.to_s
      end

      test 'destroys pointed booking_resource' do
        booking_resource = booking_resources(:big_conference_room)
        assert_difference('BookingResource.count', -1) do
          delete :destroy, params: { id: booking_resource }, format: :json
          assert_response 204, @response.body.to_s
        end
      end
    end
  end
end
