require 'test_helper'

module Api
  module V1
    class InventoryItemsControllerTest < ActionController::TestCase
      let(:valid_params) do
        {
          user_id: users(:milosz).id,
          name: '<PERSON>pt<PERSON>',
          description: 'Dell 2137',
          inventory_no: '3'
        }
      end

      let(:inventory_item) { inventory_items(:dell) }

      setup do
        @user = users(:mkalita_user)
        authenticate(@user)
      end

      test 'inventory item is created with valid params' do
        assert_difference('InventoryItem.count') do
          post :create, params: { inventory_item: valid_params }, format: :json
        end

        assert_response :created
        inventory_item = InventoryItem.last
        assert_equal @user, inventory_item.requester
      end

      test 'validation fails properly on create' do
        post :create, params: { inventory_item: valid_params.merge(user_id: '') }, format: :json

        assert_response :unprocessable_entity
      end

      test 'show returns inventory item properly' do
        get :show, params: { id: inventory_item }, format: :json

        assert_response :success
        assert_equal inventory_item.id, json_body['id']
      end

      test 'index returns inventory items list properly' do
        get :index, format: :json

        assert_response :success
        assert_equal 1, json_body.count
      end

      test 'index filters inventory items by user' do
        get :index, params: { f: { user_id: users(:milosz) }}, format: :json

        assert_response :success
        assert_equal 0, json_body.count

        get :index, params: { f: { user_id: users(:wiktoria) } }, format: :json
        assert_response :success
        assert_equal 1, json_body.count
      end

      test 'index filters inventory items by company' do
        get :index, params: { f: { company_id: companies(:two) } }, format: :json

        assert_response :success
        assert_equal 0, json_body.count

        get :index, params: { f: { company_id: companies(:one) } }, format: :json
        assert_response :success
        assert_equal 1, json_body.count
      end

      test 'index filters inventory items by state' do
        get :index, params: { f: { state: 1 } }, format: :json

        assert_response :success
        assert_equal 0, json_body.count

        get :index, params: { f: { state: 0 } }, format: :json
        assert_response :success
        assert_equal 1, json_body.count
      end

      test 'update updates record with valid params' do
        patch :update, params: { id: inventory_item, inventory_item: { name: 'Laptop' } },
                       format: :json

        assert_response :success
        assert_equal 'Laptop', inventory_item.reload.name
      end

      test 'update returns 422 given invalid params' do
        patch :update, params: { id: inventory_item, inventory_item: { name: '' } }, format: :json

        assert_response :unprocessable_entity
      end

      test 'activate changes status from pending to active' do
        patch :activate, params: { id: inventory_item }, format: :json

        assert_response :no_content
        assert_equal 'active', inventory_item.reload.state
      end

      test 'close changes status from active to closed' do
        inventory_item.activate!

        patch :close, params: { id: inventory_item }, format: :json

        assert_response :no_content
        assert_equal 'closed', inventory_item.reload.state
      end

      test 'close returns 400 if transition is forbidden' do
        patch :close, params: { id: inventory_item }, format: :json

        assert_response :bad_request
      end

      test 'reject changes status from pending to rejected' do
        patch :reject, params: { id: inventory_item }, format: :json

        assert_response :no_content
        assert_equal 'rejected', inventory_item.reload.state
      end
    end
  end
end
