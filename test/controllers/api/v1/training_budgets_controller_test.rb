require 'test_helper'

class Api::V1::TrainingBudgetsControllerTest < ActionController::TestCase
  let(:valid_attributes) do
    {
      year: Date.current.year,
      department_id: departments(:one).id,
      amount: 500
    }
  end
  let(:training_budget) { training_budgets(:global_budget) }

  setup do
    authenticate(users(:mkalita_user))
  end

  test 'index' do
    get :index, format: :json

    assert_response :success
    assert_equal 2, json_body.size
  end

  test 'create' do
    assert_difference -> { TrainingBudget.count } do
      post :create, params: { training_budget: valid_attributes }, format: :json
    end

    assert_response :created
  end

  test 'create with invalid attributes' do
    assert_no_difference -> { TrainingBudget.count } do
      post :create, params: { training_budget: valid_attributes.merge(year: nil) }, format: :json
    end

    assert_response :unprocessable_entity
    assert_equal ['is not a number'], json_body['errors']['year']
  end

  test 'show' do
    get :show, params: { id: training_budget.id }, format: :json

    assert_response :success
    assert_equal training_budget.id, json_body['id']
  end

  test 'update' do
    put :update, params: { id: training_budget.id, training_budget: { amount: 1000 } }, format: :json

    assert_response :success
    assert_equal 1000, training_budget.reload.amount
  end

  test 'update with invalid attributes' do
    put :update, params: { id: training_budget.id, training_budget: { year: nil } }, format: :json

    assert_response :unprocessable_entity
    assert_equal ['is not a number'], json_body['errors']['year']
  end

  test 'destroy' do
    assert_difference -> { TrainingBudget.count }, -1 do
      delete :destroy, params: { id: training_budget.id }, format: :json
    end

    assert_response :no_content
  end

  test 'index as an unprivileged user' do
    authenticate(users(:mikolaj))

    get :index, format: :json

    assert_response :forbidden
  end
end
