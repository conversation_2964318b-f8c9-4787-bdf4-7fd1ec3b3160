require 'test_helper'

class RegistryCategoryPolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL

  let(:record_model) { RegistryCategory }
  let(:registry_category) { registry_categories(:one) }

  let(:nil_user) { nil }

  let(:global_admin) do
    user = users(:mkalita_user)
    user.global_roles << global_roles(:global_admin)
    user
  end

  let(:user_with_registry_category_permissions) do
    user = users(:mikolaj)
    user.global_roles << global_roles(:global_admin)
    user
  end

  let(:regular_user) do
    user = users(:milosz)
    user.global_roles << global_roles(:global_user)
    user
  end

  def test_index
    assert_permit global_admin, record_model
    assert_permit user_with_registry_category_permissions, record_model
    refute_permit regular_user, record_model
    refute_permit nil_user, record_model
  end

  def test_show
    assert_permit global_admin, registry_category
    assert_permit user_with_registry_category_permissions, registry_category
    refute_permit regular_user, registry_category
    refute_permit nil_user, registry_category
  end

  def test_create
    assert_permit global_admin, record_model
    assert_permit user_with_registry_category_permissions, record_model
    refute_permit regular_user, record_model
    refute_permit nil_user, record_model
  end

  def test_update
    assert_permit global_admin, registry_category
    assert_permit user_with_registry_category_permissions, registry_category
    refute_permit regular_user, registry_category
    refute_permit nil_user, registry_category
  end

  def test_destroy
    assert_permit global_admin, registry_category
    assert_permit user_with_registry_category_permissions, registry_category
    refute_permit regular_user, registry_category
    refute_permit nil_user, registry_category
  end

  def test_activate
    assert_permit global_admin, registry_category
    assert_permit user_with_registry_category_permissions, registry_category
    refute_permit regular_user, registry_category
    refute_permit nil_user, registry_category
  end

  def test_close
    assert_permit global_admin, registry_category
    assert_permit user_with_registry_category_permissions, registry_category
    refute_permit regular_user, registry_category
    refute_permit nil_user, registry_category
  end
end
