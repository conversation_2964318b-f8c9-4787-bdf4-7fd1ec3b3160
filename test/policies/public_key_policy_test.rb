require 'test_helper'

class PublicKeyPolicyTest < PolicyAssertions::Test
  def test_index_and_create_and_destroy
    assert_permit(users(:wiktoria), PublicKey)
    refute_permit(users(:external_user), PublicKey)
  end

  def test_default_permitted_attributes
    user = users(:wiktoria)
    policy = PublicKeyPolicy.new(user, PublicKey)
    actual = policy.permitted_attributes
    expected = [:key, :identifier]
    compare_as_sets(expected, actual)
  end

  def test_private_only_scope
    user = users(:mikolaj)
    scope = PublicKeyPolicy::Scope.new(user, PublicKey)
    assert_equal(0, scope.resolve.count)
  end

  def test_admin_scope
    user = users(:mkalita_global_admin_programmer)
    target_user = users(:wiktoria)
    scope = PublicKeyPolicy::Scope.new(user, PublicKey, target_user)
    assert_equal(1, scope.resolve.count)
  end

  private

  def compare_as_sets(expected, actual)
    assert_equal(Set.new(expected), Set.new(actual))
  end
end
