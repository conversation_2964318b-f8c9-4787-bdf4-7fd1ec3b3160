require 'test_helper'

class ApprovalPolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL
  include UsersHelper

  let(:agreements_admin) do
    user = generate_user
    user.global_roles << global_roles(:global_role_agreements_admin)
    user
  end

  let(:global_admin) do
    user = generate_user
    user.global_roles << global_roles(:global_admin)
    user
  end

  let(:signed_in_user) do
    user = generate_user
    user.global_roles << global_roles(:global_user)
    user
  end

  let(:signed_in_user_with_agreements_approval) do
    user = generate_user
    user.global_roles << global_roles(:global_user)
    user
  end

  let(:nil_user) { nil }

  let(:record_model) { Approval }
  let(:agreement) { agreements(:one) }

  before do
    User.any_instance.stubs(:update_approvals_cache).returns(nil)
    Approval.create(approvable_id: agreement.id,
                    approvable_type: 'Agreement',
                    user_id: signed_in_user_with_agreements_approval.id)
  end

  def test_index
    assert_permit agreements_admin, record_model
    assert_permit global_admin, record_model

    refute_permit nil_user, record_model
    refute_permit signed_in_user_with_agreements_approval, record_model
    refute_permit signed_in_user, record_model
  end

  def test_not_accepted
    assert_permit agreements_admin, record_model
    assert_permit global_admin, record_model
    assert_permit signed_in_user_with_agreements_approval, record_model
    assert_permit signed_in_user, record_model

    refute_permit nil_user, record_model
  end

  def test_accept
    assert_permit agreements_admin, record_model
    assert_permit global_admin, record_model
    assert_permit signed_in_user_with_agreements_approval, record_model
    assert_permit signed_in_user, record_model

    refute_permit nil_user, record_model
  end
end
