require 'test_helper'

class LogsPolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL
  include UsersHelper

  let(:agreements_admin) do
    user = generate_user
    user.global_roles << global_roles(:global_role_agreements_admin)
    user
  end

  let(:global_admin) do
    user = generate_user
    user.global_roles << global_roles(:global_admin)
    user
  end

  let(:signed_in_user) do
    user = generate_user
    user.global_roles << global_roles(:global_user)
    user
  end

  def test_index
    assert_permit global_admin, :logs

    refute_permit signed_in_user, :logs
    refute_permit agreements_admin, :logs
  end
end
