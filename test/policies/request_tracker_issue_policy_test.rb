require 'test_helper'

# tail -n 20000 -f log/test.log | grep -A55 "ProjectPolicyTest: test_create"
class RequestTrackerIssuePolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL

  let(:record_model) { RequestTrackerIssue }
  let(:regular_record) do
    regular_record = RequestTrackerIssue.create(title: 'My test mail to admin', user_ids: [users(:wiktoria).id], project_id: projects(:one).id, content: 'content')
  end
  let(:nil_user) do
    nil
  end
  let(:imperator_programmer_user) do
    user = User.create!(email: '<EMAIL>',
                       username: 'imperator_programmer_user_gmail',
                       first_name: '<PERSON>',
                       last_name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today,
                       company: companies(:one))
    user.global_roles << global_roles(:global_admin_programmer)
    user
  end
  let(:admin_user) do
    user = User.create!(email: '<EMAIL>',
                       username: 'admin_user_gmail',
                       first_name: '<PERSON>',
                       last_name: '<PERSON><PERSON>',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today,
                       company: companies(:one))
    user.global_roles << global_roles(:global_admin)
    user
  end
  let(:signed_in_external_user) do
    user = User.create!(email: '<EMAIL>',
                       username: 'client_user_gmail',
                       first_name: 'Client',
                       last_name: 'Cliento',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today,
                       company: nil)
    user.global_roles << global_roles(:global_client)
    user
  end
  let(:signed_in_internal_user) do
    user = User.create!(email: '<EMAIL>',
                       username: 'record_user_gmail',
                       first_name: 'Adam',
                       last_name: 'Anderson',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today,
                       company: companies(:one))
    user.global_roles << global_roles(:global_user)
    membership_params = { project_id: projects(:mkalita_project).id,
                          member_id: user.id,
                          member_type: 'User',
                          role_ids: [roles(:mkalita_role).id] }
    Membership.create!(membership_params)
    user.reload
  end

  def test_create
    assert_permit imperator_programmer_user, record_model
    assert_permit admin_user, record_model
    assert_permit signed_in_internal_user, record_model
    refute_permit nil_user, record_model
    refute_permit signed_in_external_user, record_model
  end
end
