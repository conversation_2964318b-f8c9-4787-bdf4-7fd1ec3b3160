require 'test_helper'

class AgreementPolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL
  include UsersHelper

  let(:agreements_admin) do
    user = generate_user
    user.global_roles << global_roles(:global_role_agreements_admin)
    user
  end

  let(:global_admin) do
    user = generate_user
    user.global_roles << global_roles(:global_admin)
    user
  end

  let(:signed_in_user) do
    user = generate_user
    user.global_roles << global_roles(:global_user)
    user
  end

  let(:signed_in_user_with_agreements_approval) do
    user = generate_user
    user.global_roles << global_roles(:global_user)
    user
  end

  let(:nil_user) {nil}

  let(:record_model) {Agreement}
  let(:agreement) {agreements(:one)}

  before do
    User.any_instance.stubs(:update_approvals_cache).returns(nil)
    Approval.create(approvable: agreement, user: signed_in_user_with_agreements_approval)
  end

  def test_index
    assert_permit agreements_admin, record_model
    assert_permit global_admin, record_model

    refute_permit nil_user, record_model
    refute_permit signed_in_user_with_agreements_approval, record_model
    refute_permit signed_in_user, record_model
  end

  def test_show

    assert_permit agreements_admin, agreement
    assert_permit signed_in_user_with_agreements_approval, agreement
    assert_permit global_admin, agreement
    assert_permit signed_in_user, agreement
    refute_permit nil_user, agreement
  end

  def test_create
    assert_permit agreements_admin, record_model
    assert_permit global_admin, record_model

    refute_permit nil_user, record_model
    refute_permit signed_in_user_with_agreements_approval, record_model
    refute_permit signed_in_user, record_model
  end

  def test_destroy
    assert_permit agreements_admin, agreement
    assert_permit global_admin, agreement

    refute_permit nil_user, agreement
    refute_permit signed_in_user_with_agreements_approval, agreement
    refute_permit signed_in_user, agreement
  end

  def test_form_data
    assert_permit agreements_admin, agreement
    assert_permit global_admin, agreement

    refute_permit nil_user, agreement
    refute_permit signed_in_user_with_agreements_approval, agreement
    refute_permit signed_in_user, agreement
  end

  def test_file
    assert_permit agreements_admin, agreement
    assert_permit global_admin, agreement

    refute_permit nil_user, agreement
    assert_permit signed_in_user_with_agreements_approval, agreement
    assert_permit signed_in_user, agreement
  end
end
