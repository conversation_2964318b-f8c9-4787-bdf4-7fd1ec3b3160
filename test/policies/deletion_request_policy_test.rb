require 'test_helper'

class DeletionRequestPolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL

  let(:record_model) { DeletionRequest }
  let(:deletion_request) { deletion_requests(:registered_request) }

  let(:admin_user) { users(:mkalita_global_admin_programmer) }
  let(:regular_user) { users(:mikolaj) }
  let(:hr_user) { users(:hr_user) }
  let(:nil_user) { nil }
  let(:agreements_admin_user) do
    user = users(:wilhelm)
    user.global_roles << global_roles(:global_role_agreements_admin)
    user
  end

  def test_index
    assert_permit admin_user, record_model, :index?
    assert_permit agreements_admin_user, record_model, :index?
    refute_permit regular_user, record_model, :index?
    refute_permit hr_user, record_model, :index?
    refute_permit nil_user, record_model, :index?
  end

  def test_show
    assert_permit admin_user, deletion_request, :show?
    assert_permit agreements_admin_user, deletion_request, :show?
    refute_permit regular_user, deletion_request, :show?
    refute_permit hr_user, deletion_request, :show?
    refute_permit nil_user, deletion_request, :show?
  end

  def test_new
    assert_permit admin_user, record_model, :new?
    assert_permit agreements_admin_user, record_model, :new?
    refute_permit regular_user, record_model, :new?
    refute_permit hr_user, record_model, :new?
    refute_permit nil_user, record_model, :new?
  end

  def test_create
    assert_permit admin_user, deletion_request, :create?
    assert_permit agreements_admin_user, deletion_request, :create?
    refute_permit regular_user, deletion_request, :create?
    refute_permit hr_user, deletion_request, :create?
    refute_permit nil_user, deletion_request, :create?
  end

  def test_update
    assert_permit admin_user, deletion_request, :update?
    assert_permit agreements_admin_user, deletion_request, :update?
    refute_permit regular_user, deletion_request, :update?
    refute_permit hr_user, deletion_request, :update?
    refute_permit nil_user, deletion_request, :update?
  end

  def test_destroy
    assert_permit admin_user, deletion_request, :destroy?
    assert_permit agreements_admin_user, deletion_request, :destroy?
    refute_permit regular_user, deletion_request, :destroy?
    refute_permit hr_user, deletion_request, :destroy?
    refute_permit nil_user, deletion_request, :destroy?
  end

  def test_processing
    assert_permit admin_user, deletion_request, :processing?
    assert_permit agreements_admin_user, deletion_request, :processing?
    refute_permit regular_user, deletion_request, :processing?
    refute_permit hr_user, deletion_request, :processing?
    refute_permit nil_user, deletion_request, :processing?
  end

  def test_complete
    assert_permit admin_user, deletion_request, :complete?
    assert_permit agreements_admin_user, deletion_request, :complete?
    refute_permit regular_user, deletion_request, :complete?
    refute_permit hr_user, deletion_request, :complete?
    refute_permit nil_user, deletion_request, :complete?
  end
end
