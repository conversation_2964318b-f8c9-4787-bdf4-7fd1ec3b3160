require 'test_helper'

# tail -n 20000 -f log/test.log | grep -A55 "ProjectPolicyTest: test_create"
class Pages::ProfilePolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL

  let(:record_model) { Pages::Profile }
  let(:regular_record_user) do
    User.create(username: 'Somename',
                first_name: 'Some',
                last_name: 'Name',
                email: '<EMAIL>',
                password: 'asdasdasd',
                password_confirmation: 'asdasdasd',
                activates_on: Time.zone.today)
  end
  let(:regular_record) do
    Pages::Profile.new(regular_record_user)
  end
  let(:nil_user) do
    nil
  end
  let(:admin_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'admin_user_gmail',
                       first_name: '<PERSON>',
                       last_name: '<PERSON><PERSON>',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today)
    user.global_roles << global_roles(:global_admin)
    user
  end
  let(:signed_in_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'record_user_gmail',
                       first_name: '<PERSON>',
                       last_name: '<PERSON>',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today)
    user.global_roles << global_roles(:global_user)
    membership_params = { project_id: projects(:mkalita_project).id,
                          member_id: user.id,
                          member_type: 'User',
                          role_ids: [roles(:mkalita_role).id] }
    Membership.create!(membership_params)
    user.reload
  end

  def test_index
    assert_raise(NotImplementedError) do
      Pages::ProfilePolicy.new(regular_record_user, regular_record).index?
    end
    assert_raise(NotImplementedError) do
      Pages::ProfilePolicy.new(admin_user, regular_record).index?
    end
    assert_raise(NotImplementedError) do
      Pages::ProfilePolicy.new(signed_in_user, regular_record).index?
    end
    assert_raise(NotImplementedError) do
      Pages::ProfilePolicy.new(nil_user, regular_record).index?
    end
  end

  def test_show
    assert Pages::ProfilePolicy.new(regular_record_user, regular_record).show?
    refute Pages::ProfilePolicy.new(admin_user, regular_record).show?
    refute Pages::ProfilePolicy.new(signed_in_user, regular_record).show?
    refute Pages::ProfilePolicy.new(nil_user, regular_record).show?
  end

  def test_create
    assert_raise(NotImplementedError) do
      Pages::ProfilePolicy.new(regular_record_user, regular_record).create?
    end
    assert_raise(NotImplementedError) do
      Pages::ProfilePolicy.new(admin_user, regular_record).create?
    end
    assert_raise(NotImplementedError) do
      Pages::ProfilePolicy.new(signed_in_user, regular_record).create?
    end
    assert_raise(NotImplementedError) do
      Pages::ProfilePolicy.new(nil_user, regular_record).create?
    end
  end

  def test_update
    assert Pages::ProfilePolicy.new(regular_record_user, regular_record).update?
    refute Pages::ProfilePolicy.new(admin_user, regular_record).update?
    refute Pages::ProfilePolicy.new(signed_in_user, regular_record).update?
    refute Pages::ProfilePolicy.new(nil_user, regular_record).update?
  end

  def test_destroy
    assert_raise(NotImplementedError) do
      Pages::ProfilePolicy.new(regular_record_user, regular_record).destroy?
    end
    assert_raise(NotImplementedError) do
      Pages::ProfilePolicy.new(admin_user, regular_record).destroy?
    end
    assert_raise(NotImplementedError) do
      Pages::ProfilePolicy.new(signed_in_user, regular_record).destroy?
    end
    assert_raise(NotImplementedError) do
      Pages::ProfilePolicy.new(nil_user, regular_record).destroy?
    end
  end
end
