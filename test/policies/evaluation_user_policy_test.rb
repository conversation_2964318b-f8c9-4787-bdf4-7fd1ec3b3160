require 'test_helper'

class EvaluationUserPolicytest < PolicyAssertions::Test
  let(:allowed_user) { users(:w<PERSON><PERSON>) }
  let(:unallowed_user) { users(:milosz) }
  let(:subject) { users(:mikolaj) }

  def test_users_index
    assert_permit allowed_user, :evaluation_user
    refute_permit unallowed_user, :evaluation_user
  end

  def test_users_show
    assert evaluation_user_policy(allowed_user).users_show?
    refute evaluation_user_policy(unallowed_user).users_show?
  end

  def test_evaluations_new
    assert evaluation_user_policy(allowed_user).evaluations_new?
    refute evaluation_user_policy(unallowed_user).evaluations_new?
  end

  def test_evaluations_create
    assert evaluation_user_policy(allowed_user).evaluations_create?
    refute evaluation_user_policy(unallowed_user).evaluations_create?
  end

  def test_evaluations_update
    assert evaluation_user_policy(allowed_user).evaluations_update?
    refute evaluation_user_policy(unallowed_user).evaluations_update?
  end

  def test_evaluations_show
    assert evaluation_user_policy(allowed_user).evaluations_show?
    refute evaluation_user_policy(unallowed_user).evaluations_show?
  end

  def test_scope_with_board_user
    allowed_user.department.update_attribute(:board_member, true)
    scope = User.active
    assert_equal scope.count, EvaluationUserPolicy::Scope.new(allowed_user, scope).resolve.count
  end

  def test_scope_with_regular_user
    scope = User.active
    assert_equal scope.joins(:department).where(departments: { chief_id: allowed_user.id }),
                 EvaluationUserPolicy::Scope.new(allowed_user, scope).resolve
  end

  private

  def evaluation_user_policy(user)
    EvaluationUserPolicy.new(user, subject)
  end
end
