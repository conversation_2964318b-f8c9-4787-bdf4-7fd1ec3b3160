require 'set'
require 'test_helper'

# tail -n 20000 -f log/test.log | grep -A55 "ProjectPolicyTest: test_create"
class ProjectPolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL

  let(:record_model) { Project }
  let(:regular_record_parent) do
    record_model.create(name: 'MyString',
                        identifier: 'projectname',
                        accounting_number: accounting_numbers(:zero),
                        description: 'Project description',
                        company: companies(:one))
  end
  let(:regular_record) do
    record_model.create(name: 'MyStringSubproject',
                        identifier: 'subprojectname',
                        accounting_number: accounting_numbers(:one),
                        description: 'Project description',
                        parent_id: regular_record_parent.id,
                        company: companies(:one))
  end
  let(:non_associated_record) do
    projects(:one)
  end
  let(:accounting_user) do
    user = users(:wiktoria)
    membership_params = { project_id: regular_record.id, member_id: user.id,
                          member_type: 'User',
                          role_ids: [roles(:accounting).id] }
    Membership.create(membership_params)
    user.reload
  end
  let(:nil_user) do
    nil
  end
  let(:admin_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'admin_user_gmail',
                       first_name: '<PERSON>',
                       last_name: '<PERSON>i',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today)
    user.global_roles << global_roles(:global_admin)
    user
  end
  let(:record_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'record_user_gmail',
                       first_name: 'Adam',
                       last_name: 'Anderson',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today)
    user.global_roles << global_roles(:global_pm)
    # czlonkostwo w projekcie
    membership_params = { project_id: regular_record.id, member_id: user.id, member_type: 'User', role_ids: [roles(:pm).id] }
    Membership.create!(membership_params)

    # czlonkostwo w parencie projektu
    membership_params = { project_id: regular_record_parent.id, member_id: user.id, member_type: 'User', role_ids: [roles(:pm).id] }
    Membership.create!(membership_params)
    user.reload
  end
  let(:record_user_via_group) do
    user = User.create(email: '<EMAIL>',
                       username: 'record_user_via_group_gmail',
                       first_name: 'Bo',
                       last_name: 'Derek',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today)
    user.global_roles << global_roles(:global_user)
    group = Group.create(name: 'Project Policy Test Group')
    raise "could\'t create Group: #{group.errors.inspect}" if group.errors.any?
    # czlonkowstwo w projekcie poprzez grupe
    membership_params = { project_id: regular_record.id, member_id: group.id, member_type: 'Group', role_ids: [roles(:pm).id] }
    Membership.create!(membership_params)
    # czlonkowstwo w parencie projektu poprzez grupe
    membership_params = { project_id: regular_record_parent.id, member_id: group.id, member_type: 'Group', role_ids: [roles(:pm).id] }
    Membership.create!(membership_params)
    user.groups << group
    user.reload
  end
  let(:not_record_user) do
    user = User.create(email: '<EMAIL>',
                       username: 'not_record_user_gmail',
                       first_name: 'John',
                       last_name: 'Doe',
                       password: generate_valid_password,
                       password_confirmation: generate_valid_password,
                       activates_on: Time.zone.today)
    user.global_roles << global_roles(:global_user)
    user.reload
  end

  let(:scope_archived) { record_model.archived }
  let(:archived_record) do
    project = record_model.create(name: 'MyString',
                                  identifier: 'project-name',
                                  accounting_number: accounting_numbers(:zero),
                                  description: 'Project description',
                                  company: companies(:one))
    project.archived!
    project
  end

  def test_scope_archived
    assert_equal [archived_record], ProjectPolicy::Scope.new(admin_user, scope_archived).resolve.to_a
    assert_empty ProjectPolicy::Scope.new(record_user, scope_archived).resolve.to_a
    assert_empty ProjectPolicy::Scope.new(record_user_via_group, scope_archived).resolve.to_a
    assert_empty ProjectPolicy::Scope.new(not_record_user, scope_archived).resolve.to_a
    assert_empty ProjectPolicy::Scope.new(nil_user, scope_archived).resolve.to_a
  end

  def test_scope
    assert_equal Project.all.to_a, ProjectPolicy::Scope.new(admin_user, record_model.all).resolve.to_a
    assert_equal Set[regular_record_parent, regular_record], Set.new(ProjectPolicy::Scope.new(record_user, record_model.all).resolve.to_a)
    assert_equal Set[regular_record_parent, regular_record], Set.new(ProjectPolicy::Scope.new(record_user_via_group, record_model.all).resolve.to_a)
    assert_empty ProjectPolicy::Scope.new(not_record_user, record_model.all).resolve.to_a
    assert_empty ProjectPolicy::Scope.new(nil_user, record_model.all).resolve.to_a
  end

  def test_index
    assert_permit admin_user, record_model
    assert_permit record_user, record_model
    assert_permit record_user_via_group, record_model
    assert_permit not_record_user, record_model
    refute_permit nil_user, record_model
  end

  def test_show
    assert_permit admin_user, regular_record
    assert_permit record_user, regular_record
    assert_permit record_user_via_group, regular_record
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_manage_payment_schedule
    refute_permit admin_user, regular_record
    assert_permit record_user, regular_record
    refute_permit record_user, non_associated_record
    assert_permit accounting_user, regular_record
  end

  def test_show_payment_schedule
    refute_permit admin_user, regular_record
    assert_permit record_user, regular_record
    refute_permit record_user, non_associated_record
    assert_permit accounting_user, regular_record
  end

  def test_update_payment_schedule_payments_with_issued_invoices
    refute_permit admin_user, regular_record
    refute_permit record_user, regular_record
    refute_permit record_user, non_associated_record
    assert_permit accounting_user, regular_record
  end

  def test_create
    assert_permit admin_user, record_model
    refute_permit record_user, record_model
    refute_permit record_user_via_group, record_model
    refute_permit not_record_user, record_model
    refute_permit nil_user, record_model
  end

  def test_update
    assert_permit admin_user, regular_record
    refute_permit record_user, regular_record # bo mimo ze ma :edit_project to nie ma activity 'projects:update'
    refute_permit record_user_via_group, regular_record # bo mimo ze ma :edit_project to nie ma activity 'projects:update'
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_destroy
    assert_permit admin_user, regular_record
    refute_permit record_user, regular_record
    refute_permit record_user_via_group, regular_record
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_archive
    assert_permit admin_user, regular_record
    refute_permit record_user, regular_record
    refute_permit record_user_via_group, regular_record
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_unarchive
    assert_permit admin_user, regular_record
    refute_permit record_user, regular_record
    refute_permit record_user_via_group, regular_record
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_close
    assert_permit admin_user, regular_record
    refute_permit record_user, regular_record
    refute_permit record_user_via_group, regular_record
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_reopen
    assert_permit admin_user, regular_record
    refute_permit record_user, regular_record
    refute_permit record_user_via_group, regular_record
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
  end

  def copy_source?
    assert_permit admin_user, regular_record
    assert_permit record_user, regular_record
    assert_permit record_user_via_group, regular_record
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_copy
    assert_permit admin_user, regular_record
    refute_permit record_user, regular_record
    refute_permit record_user_via_group, regular_record
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
  end

  def test_report
    assert_permit admin_user, regular_record
    assert_permit accounting_user, regular_record
    refute_permit record_user, regular_record
    refute_permit record_user_via_group, regular_record
    refute_permit not_record_user, regular_record
    refute_permit nil_user, regular_record
  end
end
