require 'test_helper'
require 'mailer_interceptor'

class MailerInterceptorTest < ActiveSupport::TestCase
  setup do
    ActionMailer::Base.register_interceptor(MailerInterceptor)
    Settings.overwrite_mail_to = true
  end

  teardown do
    Settings.overwrite_mail_to = false
  end

  test 'intercepts and redirects mails if set to' do
    user = users(:milosz)

    DeviseMailer.confirmation_instructions(user, '1234').deliver_now
    mail = DeviseMailer.deliveries.last

    assert_equal ['<EMAIL>'], mail.to
  end

  test 'does not intercept mails from whitelist' do
    user = users(:mikolaj)

    DeviseMailer.confirmation_instructions(user, '1234').deliver_now
    mail = DeviseMailer.deliveries.last

    assert_equal [user.email], mail.to
  end
end
