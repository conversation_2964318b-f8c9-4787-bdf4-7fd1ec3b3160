require 'test_helper'
require 'wifi_token_client'

class WifiTokenClientTest < ActiveSupport::TestCase
  let(:body) { { key: '123512351' } }

  setup do
    Settings.rabbit_enabled = true
  end

  teardown do
    Settings.rabbit_enabled = false
  end

  test 'works well in normal conditions' do
    # mocks for initialization:
    conn = mock('connection')
    Bunny.expects(:new).with(Settings.rabbit.connection.to_h).returns(conn)
    conn.expects(:start)
    channel = mock('channel')
    conn.expects(:create_channel).returns(channel)
    exchange = mock('exchange')
    queue = Object.new
    channel.expects(:topic).with('events', durable: true).returns(exchange)
    channel.expects(:queue).with('amq.rabbitmq.reply-to').returns(queue)

    subject = WifiTokenClient.new

    # mocks for call:
    options = subject.send(:options)
    delivery_info = {}
    properties = {}
    payload = { ok: true }.to_json
    consumer = mock('consumer')
    subject.stubs(:options).returns(options)

    queue.define_singleton_method(:subscribe) do |&block|
      # Simulate new thread waiting for the amqp response and returning consumer:
      Thread.new do
        sleep(0.1)

        block.call(delivery_info, properties, payload)
      end

      consumer
    end

    exchange.expects(:publish).with(body.to_json, options)
    consumer.expects(:cancel)

    result = subject.call(body)

    assert_equal JSON.parse(payload), result
  end
end
