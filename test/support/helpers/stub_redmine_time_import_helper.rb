module StubRedmineTimeImportHelper
  MOCK_DATA = [
    {
      'user' => { 'id' => 1535, 'name' => '<PERSON>', 'login' => 'mkalita_user_4' },
      'activity' => { 'id' => 9, 'name' => 'Development' },
      'hours' => 8.0,
      'comments' => '.',
      'spent_on' => '2020-10-08',
      'created_on' => '2020-09-30T15:25:50+02:00',
      'updated_on' => '2020-09-30T15:25:50+02:00'
    },
    {
      'user' => { 'id' => 1535, 'name' => '<PERSON>', 'login' => 'mromanow' },
      'activity' => { 'id' => 9, 'name' => 'Development' },
      'hours' => 6.0,
      'comments' => '.',
      'spent_on' => '2020-10-08',
      'created_on' => '2020-09-30T15:25:50+02:00',
      'updated_on' => '2020-09-30T15:25:50+02:00'
    },
    {
      'user' => { 'id' => 1535, 'name' => 'Johnatan Swift', 'login' => 'mgrabowski' },
      'activity' => { 'id' => 9, 'name' => 'Development' },
      'hours' => 1000.0,
      'comments' => '.',
      'spent_on' => '2020-10-08',
      'created_on' => '2020-09-30T15:25:50+02:00',
      'updated_on' => '2020-09-30T15:25:50+02:00'
    }
  ].freeze

  def stub_redmine_time_import_success
    RedmineWorkingTimeApi::Importer.any_instance.stubs(:import).returns(MOCK_DATA)
  end
end
