# frozen_string_literal: true

module TestData
  module_function

  def document_data
    attacher = Shrine::Attacher.new
    attacher.set(uploaded_document)

    attacher.column_data
  end

  def uploaded_document
    file = File.open('test/fixtures/files/sample.pdf', binmode: true)

    # for performance we skip metadata extraction and assign test metadata
    uploaded_file = Shrine.upload(file, :store, metadata: false)
    uploaded_file.metadata.merge!(
      'size' => File.size(file.path),
      'mime_type' => 'application/pdf',
      'filename' => 'sample.pdf'
    )

    uploaded_file
  end
end
