# local chromedriver installation required to run capybara tests (see README)

CHROME_EXECUTABLE_PATH = Settings.capybara.chrome_executable_path.freeze

capabilities_opts = {
  # --log-level=0 # wszystko
  chrome_options: {
    'args' => %w[
      --flag
      --window-size=1920,1080
      --disable-web-security
      --no-sandbox
      --disable-gpu
      --incognito
    ],
    'binary' => CHROME_EXECUTABLE_PATH
  },
  acceptSslCerts: true
}

# https://sites.google.com/a/chromium.org/chromedriver/capabilities
# https://peter.sh/experiments/chromium-command-line-switches/
# https://docs.travis-ci.com/user/chrome
capabilities = Selenium::WebDriver::Remote::Capabilities.chrome(capabilities_opts)

# FIXME: log_path przestalo dzialac
driver_opts = {
  # port_server: '2323',
  # whitelisted_ips: ['***********', '***********'],
  silent: false,
  verbose: true,
  url_base: '127.0.0.1',
  log_path: 'tmp/capybara/capybara.log'
}

selenium_opts = {
  browser: :chrome,
  desired_capabilities: capabilities,
  driver_opts: driver_opts,
}

# FIXME: nie dziala, dziala tylko headless, chyba ze sie to wywali
Capybara.register_driver :chrome do |app|
  # https://github.com/teamcapybara/capybara/blob/master/lib/capybara/selenium/driver.rb
  Capybara::Selenium::Driver.new(app, selenium_opts)
end

headless_capabilities_opts = capabilities_opts.dup
headless_capabilities_opts[:chrome_options]['args'] << '--headless'
headless_capabilities = Selenium::WebDriver::Remote::Capabilities.chrome(headless_capabilities_opts)

headless_selenium_opts = selenium_opts.dup
headless_selenium_opts[:desired_capabilities] = headless_capabilities

Capybara.register_driver :headless_chrome do |app|
  Capybara::Selenium::Driver.new(app, headless_selenium_opts)
end

Capybara.default_driver = CAPYBARA_DEFAULT_DRIVER
Capybara.javascript_driver = CAPYBARA_DEFAULT_DRIVER
