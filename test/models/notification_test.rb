require 'test_helper'

class NotificationTest < ActiveSupport::TestCase
  should validate_presence_of(:user_id)
  should validate_presence_of(:subject_id)
  should validate_presence_of(:subject_type)
  should validate_presence_of(:state)

  def test_create
    Notification.delete_all
    notification_count = Notification.count
    save_notification
    assert_equal(notification_count + 1, @original_notification_count)
  end

  def test_update
    Notification.delete_all
    save_notification
    @notification.update(
      user_id: users(:mkalita_user).id,
      subject: holiday_requests(:visible_holiday_request)
      # global_subject: holiday_requests(:visible_holiday_request).to_sgid
    )
    assert_equal(@original_notification_count, Notification.count)
  end

  private

  def build_notification
    Notification.new(
      user_id: users(:mkalita_user).id,
      subject: holiday_requests(:visible_holiday_request)
      # global_subject: holiday_requests(:visible_holiday_request).to_sgid
    )
  end

  def save_notification
    @notification = build_notification
    assert @notification.save, @notification.errors.inspect
    @original_notification_count = Notification.count
  end
end
