require 'test_helper'

class B2BReportTest < ActiveSupport::TestCase
  let(:b2b_report) do
    b2b_reports(:april_2020_b2b_report)
  end

  should belong_to(:user)
  should belong_to(:company)

  should validate_presence_of(:user_id)
  should validate_presence_of(:month)
  should validate_presence_of(:year)
  should validate_numericality_of(:month)
  should validate_numericality_of(:year)

  test 'generate' do
    cost_invoices(:john_cost_invoice).update_column(:state, :accepted)
    workbook = Spreadsheet::Workbook.new
    workbook.create_worksheet
    date_from = Date.new(b2b_report.year, b2b_report.month, 1)
    date_to = date_from.end_of_month
    scope = User.joins(:user_contracts, contractor: :cost_invoices)
                .where(cost_invoices: { sell_date: date_from..date_to })
                .where(company: b2b_report.company)
                .where('activates_on <= ?', date_to)
                .merge(UserContract.overlapping(date_from, date_to).b2b)
                .distinct
    RedmineWorkingTimeApi::ReportBuilder.stubs(:generate_summary)
                                        .with(b2b_report.year, b2b_report.month, b2b_report.company,
                                              scope)
                                        .returns(workbook)

    b2b_report.generate

    assert b2b_report.reload.file.present?
  end

  test 'B2BMonthlyReportWorker is scheduled on creation' do
    assert_difference -> { B2BMonthlyReportWorker.jobs.count }, 1 do
      B2BReport.create(
        user: users(:wiktoria),
        company: companies(:one),
        month: Time.zone.today.month,
        year: Time.zone.today.year
      )
    end
  end
end
