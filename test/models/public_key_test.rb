require 'test_helper'

class PublicKeyTest < ActiveSupport::TestCase
  setup do
    @valid_params = {
      identifier: 'work_key',
      key: 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDDQiJ8ShMS7WGPXcQdau/874l5Dg+CfBkt50mny0QDod0KRWjvfcvs8b/WpfJujSTyFp1As42rNxI4L8S0aYF77Muu7sILi05jzI1c6UCtoIZYLTKkU3kXWP06iStSFb8WZgV0oaoMjCqOUCzCoBG9JEzVwU7VQWzLvUrtyZP+wMsWxGxilUzlrD77gEcfSmmVhwjwc+Zk2DoIwMbDzRpvoxL71MUvV9InJy8IcmjtxRkr4e/BK3m/G4bhtz/kThI+UZkPowhIXZnRiTNF/pg+3kiHIjRdGsu2zgc3MIdKZd8AB9JC4669ALiDYOpi4JttSI+d02va7gYCJJi3Fsgb <EMAIL>',
      user: users(:mikolaj)
    }
  end

  test 'validates identifier presence' do
    public_key = PublicKey.new
    assert public_key.invalid?
    refute_empty public_key.errors[:identifier]
  end

  test 'validates identifier uniqueness' do
    public_key = PublicKey.new(
      identifier: 'work key',
      user: users(:wiktoria)
    )
    assert public_key.invalid?
    refute_empty public_key.errors[:identifier]
  end

  test 'identifier should be unique only in scope of user' do
    public_key = PublicKey.new(
      identifier: 'work_key',
      user: users(:mikolaj)
    )
    public_key.valid?
    assert_empty public_key.errors[:identifier]
  end

  test 'validates user presence' do
    public_key = PublicKey.new
    assert public_key.invalid?
    refute_empty public_key.errors['user']
  end

  test 'identifier format is validated' do
    public_key = PublicKey.new(identifier: 'foo bar')
    assert public_key.invalid?
    refute_empty public_key.errors[:identifier]
  end

  test 'identifier length is validated' do
    public_key = PublicKey.new(identifier: 'a' * 71)
    assert public_key.invalid?
    refute_empty public_key.errors[:identifier]
  end

  test 'key presence is validated' do
    public_key = PublicKey.new
    assert public_key.invalid?
    refute_empty public_key.errors[:key]
  end

  test 'key is validated' do
    public_key = PublicKey.new(key: 'foo')
    assert public_key.invalid?
    refute_empty public_key.errors[:key]
  end

  test 'fingerprint is generated' do
    public_key = PublicKey.new(key: public_keys(:wiktoria_key).key)
    public_key.valid?
    assert public_key.fingerprint.present?
  end

  test 'key uniqueness is validated' do
    public_key = PublicKey.new(key: public_keys(:wiktoria_key).key)
    assert public_key.invalid?
    refute_empty public_key.errors[:key]
    public_key.user = users(:wiktoria)
    assert public_key.invalid?
    refute_empty public_key.errors[:key]
  end

  test 'existing public key cannot be changed' do
    public_key = public_keys(:wiktoria_key)
    public_key.identifier = 'foo_bar'
    assert public_key.invalid?
    refute_empty public_key.errors[:identifier]
  end

  test 'is valid with valid params' do
    public_key = PublicKey.new(@valid_params)
    assert public_key.valid?
  end

  test 'redmine_public_keys_worker is run upon public key creation' do
    assert_difference('RedminePublicKeysWorker.jobs.count', 1) do
      PublicKey.create!(@valid_params)
    end
  end

  test 'redmine_public_keys_worker is run upon public key destruction' do
    key = public_keys(:wiktoria_key)
    key.update_attribute(:redmine_id, 202)
    assert_difference('RedminePublicKeysWorker.jobs.count', 1) do
      key.destroy
    end
  end
end
