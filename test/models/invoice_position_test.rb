require 'test_helper'

class InvoicePositionTest < ActiveSupport::TestCase
  def invoice_position
    @invoice_position ||= InvoicePosition.new(
      amount: 1, invoice: invoices(:payment_three), unit_price: 1, tax_rate: :'23'
    )
  end

  test 'name presence is validated' do
    invoice_position.name = nil
    assert invoice_position.invalid?
    assert_not_empty invoice_position.errors[:name]
  end

  test 'amount presence is validated' do
    invoice_position.amount = nil
    assert invoice_position.invalid?
    refute_empty invoice_position.errors[:amount]
  end

  test 'amount numericality is validated' do
    invoice_position.amount = -2
    assert invoice_position.invalid?
    refute_empty invoice_position.errors[:amount]
  end

  test 'tax_rate presence is validated' do
    invoice_position.tax_rate = nil
    assert invoice_position.invalid?
    refute_empty invoice_position.errors[:tax_rate]
  end

  test 'unit_price presence is validated' do
    invoice_position.unit_price = nil
    assert invoice_position.invalid?
    refute_empty invoice_position.errors[:unit_price]
  end

  test 'unit_price numericality is validated' do
    invoice_position.unit_price = -2
    assert invoice_position.invalid?
    refute_empty invoice_position.errors[:unit_price]
  end

  test 'net_value is equal to amount * unit_price' do
    invoice_position = invoice_positions(:payment_four_position)
    assert_equal(0.5, invoice_position.net_value)
  end

  test 'JPK GTU can be nil' do
    invoice_position.jpk_gtu = nil
    invoice_position.valid?

    assert_empty invoice_position.errors[:jpk_gtu]
  end

  test 'JPK GTU is invalid if value not whitelisted' do
    invoice_position.jpk_gtu = 'foobar'
    invoice_position.valid?

    refute_empty invoice_position.errors[:jpk_gtu]
  end

  test 'JPK GTU is valid if value is whitelisted' do
    invoice_position.jpk_gtu = 'GTU_11'
    invoice_position.valid?

    assert_empty invoice_position.errors[:jpk_gtu]
  end

  test 'JPK transaction_code can be nil' do
    invoice_position.jpk_transaction_code = nil
    invoice_position.valid?

    assert_empty invoice_position.errors[:jpk_transaction_code]
  end

  test 'JPK transaction_code is invalid if value not whitelisted' do
    invoice_position.jpk_transaction_code = 'foobar'
    invoice_position.valid?

    refute_empty invoice_position.errors[:jpk_transaction_code]
  end

  test 'JPK transaction_code is valid if value is whitelisted' do
    invoice_position.jpk_gtu = 'SW'
    invoice_position.valid?

    assert_empty invoice_position.errors[:jpk_transaction_code]
  end
end
