require 'test_helper'

module Searches
  class RiskAnalysisSearchTest < ActiveSupport::TestCase
    let(:risk_analysis_one) { risk_analyses(:one) }
    let(:risk_analysis_two) { risk_analyses(:two) }
    let(:risk_analysis_three) { risk_analyses(:three) }
    let(:risk_analysis_four) { risk_analyses(:four) }
    let(:risk_analysis_five) { risk_analyses(:five) }
    let(:scope) { RiskAnalysis.all }

    test "searches by name with exact match" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { term: 'Marketing' })
      results = search.results.to_a

      assert_equal 1, results.size
      assert_equal risk_analysis_one.id, results.first.id
    end

    test "searches by name with partial match" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { term: 'Risk' })
      results = search.results.to_a

      ids = results.map(&:id)
      assert_equal 5, results.size
      assert_includes ids, risk_analysis_one.id
      assert_includes ids, risk_analysis_two.id
      assert_includes ids, risk_analysis_three.id
      assert_includes ids, risk_analysis_four.id
      assert_includes ids, risk_analysis_five.id
    end

    test "searches by name case insensitive" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { term: 'marketing' })
      results = search.results.to_a

      assert_equal 1, results.size
      assert_equal risk_analysis_one.id, results.first.id

      search = RiskAnalysisSearch.new(scope: scope, filters: { term: 'MARKETING' })
      results = search.results.to_a

      assert_equal 1, results.size
      assert_equal risk_analysis_one.id, results.first.id
    end

    test "returns empty results for non-matching term" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { term: 'nonexistent' })
      results = search.results.to_a

      assert_equal 0, results.size
    end

    test "handles special characters in search term" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { term: 'HR%' })
      results = search.results.to_a

      assert_equal 0, results.size
    end

    test "filters by company_id" do
      company_one_id = companies(:one).id
      search = RiskAnalysisSearch.new(scope: scope, filters: { company_id: company_one_id })
      results = search.results.to_a

      results.each do |risk_analysis|
        assert_equal company_one_id, risk_analysis.company_id
      end
    end

    test "returns empty results for non-existing company_id" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { company_id: 99999 })
      results = search.results.to_a

      assert_equal 0, results.size
    end

    test "returns all results when company_id is nil" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { company_id: nil })
      results = search.results.to_a

      assert_equal 5, results.size
    end

    test "filters by active state" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { state: 'active' })
      results = search.results.to_a

      active_registry_activity_ids = RegistryActivity.active.pluck(:id)
      results.each do |risk_analysis|
        assert_includes active_registry_activity_ids, risk_analysis.registry_activity_id
      end
    end

    test "filters by closed state" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { state: 'closed' })
      results = search.results.to_a

      closed_registry_activity_ids = RegistryActivity.closed.pluck(:id)
      results.each do |risk_analysis|
        assert_includes closed_registry_activity_ids, risk_analysis.registry_activity_id
      end
    end

    test "returns all results when state is empty" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { state: '' })
      results = search.results.to_a

      assert_equal 5, results.size
    end

    test "sorts by id ascending" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { sort: 'id asc' })
      results = search.results.to_a

      assert_equal RiskAnalysis.order(id: :asc).pluck(:id), results.map(&:id)
    end

    test "sorts by id descending" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { sort: 'id desc' })
      results = search.results.to_a

      assert_equal RiskAnalysis.order(id: :desc).pluck(:id), results.map(&:id)
    end

    test "sorts by name ascending" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { sort: 'name asc' })
      results = search.results.to_a

      assert_equal RiskAnalysis.order(name: :asc).pluck(:id), results.map(&:id)
    end

    test "sorts by administrator_type using registry_activity relationship" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { sort: 'administrator_type asc' })
      results = search.results.to_a

      assert_equal 5, results.size
    end

    test "sorts by creation_date using registry_activity relationship" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { sort: 'creation_date asc' })
      results = search.results.to_a

      assert_equal 5, results.size
    end

    test "defaults to ascending direction for invalid direction" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { sort: 'name invalid' })
      results = search.results.to_a

      assert_equal RiskAnalysis.order(name: :asc).pluck(:id), results.map(&:id)
    end

    test "ignores invalid sort fields" do
      search = RiskAnalysisSearch.new(scope: scope, filters: { sort: 'invalid_field asc' })
      results = search.results.to_a

      assert_equal 5, results.size
    end
  end
end
