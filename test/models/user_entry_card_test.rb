require 'test_helper'

class UserEntryCardTest < ActiveSupport::TestCase
  should belong_to(:user)
  should validate_presence_of(:card_number)
  should validate_numericality_of(:card_number).only_integer.is_greater_than(0)
                                               .is_less_than_or_equal_to(2_147_483_647)
  should validate_presence_of(:starts_on)

  test 'validates uniqueness of card number in specific time range' do
    user = users(:mikolaj)
    entry_card = user.user_entry_cards.new(card_number: 1, starts_on: Time.zone.yesterday)

    assert entry_card.invalid?
    assert_equal 'must be unique in specific time period', entry_card.errors[:card_number].first
  end

  test 'validates uniqueness of user_id in specific time range' do
    user = users(:milosz)
    entry_card = user.user_entry_cards.new(card_number: 2, starts_on: Time.zone.yesterday)

    assert entry_card.invalid?
    assert_equal 'user cannot have more than one card simultaneously',
                 entry_card.errors[:user_id].first
  end

  test 'validates starts_on less or equal to ends_on' do
    user_entry_card = user_entry_cards(:wiktoria_card)
    user_entry_card.ends_on = Date.new(2023, 9, 8)

    assert user_entry_card.invalid?
    assert_equal 'must be after or equal to 2023-09-09', user_entry_card.errors[:ends_on].first
  end
end
