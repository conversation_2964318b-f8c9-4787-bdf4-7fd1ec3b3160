require 'test_helper'

class LdapDataTest < ActiveSupport::TestCase
  setup do
    @provider = Minitest::Mock.new
    @ldap_data = LdapData.new(@provider)
    @entry = ldap_entries(:wiktoria_root)
  end

  test 'ldap entry is created with proper parameters' do
    @provider.expect(:add, true, dn: @entry.dn, attributes: @entry.ldap_attributes)
    @provider.expect(:get_operation_result, OpenStruct.new(code: 0))
    @ldap_data.create_entry_in_ldap_server(@entry)
    assert @provider.verify
  end

  test 'ldap entry is destroyed with proper params' do
    dn = @entry.dn
    @provider.expect(:delete, true, dn: dn)
    @provider.expect(:get_operation_result, OpenStruct.new(code: 0))
    @ldap_data.destroy_entry_in_ldap_server(dn)
    assert @provider.verify
  end

  test 'ldap entry is renamed properly' do
    changes = [@entry.dn, 'uid=wwindsor,ou=users,dc=root,dc=efigence,dc=com']
    @provider.expect(:rename, true, olddn: @entry.dn, newrdn: 'uid=wwindsor')
    @provider.expect(:get_operation_result, OpenStruct.new(code: 0))
    @ldap_data.rename_entry_in_ldap_server(changes)
    assert @provider.verify
  end

  test 'ldap entry is modified properly' do
    pre_value = { foo: 'bar', baz: 'qux', quux: 'corge' }.with_indifferent_access
    aft_value = { foo: 'bar', baz: 'quux', qux: 'corge' }.with_indifferent_access
    changes = [pre_value, aft_value]
    dn = @entry.dn
    @provider.expect(:get_operation_result, OpenStruct.new(code: 0))
    @provider.expect(:modify, true, dn: dn, operations:
                                        [[:replace, 'baz', 'quux'],
                                         [:replace, 'qux', 'corge'],
                                         [:replace, 'quux', nil]])
    @ldap_data.modify_entry_in_ldap_server(dn, changes)
    assert @provider.verify
  end

  test 'raise exception if status code is not 0' do
    assert_raises Exceptions::LdapException do
      @provider.expect(:add, true, dn: @entry.dn, attributes: @entry.ldap_attributes)
      @provider.expect(:get_operation_result, OpenStruct.new(
        code: 16, message: 'Fake message', error_message: 'Fake error message')
      )
      @ldap_data.create_entry_in_ldap_server(@entry)
      assert @provider.verify
    end
  end
end
