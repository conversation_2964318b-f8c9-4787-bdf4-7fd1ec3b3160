require 'test_helper'

class GroupTest < ActiveSupport::TestCase
  setup do
    @valid_params = { name: 'Group name' }
  end

  let(:ldap_tree) do
    {
      name: 'root', base: 'dc=root,dc=efigence,dc=com', users_tree: 'ou=users',
      groups_tree: 'ou=groups', projects_tree: 'ou=projects', active_users_tree: 'ou=active_users',
      membership_column: 'docs_ldap'
    }
  end

  test 'validates name' do
    group = Group.new
    assert group.invalid?
    refute_empty group.errors[:name], 'name presence should be validated'
    group.name = groups(:one).name
    assert group.invalid?
    refute_empty group.errors[:name], 'name uniqueness should be validated'
  end

  test 'defines cn from name without special chars' do
    group = Group.new(name: 'Staff & Developers')
    group.save
    assert_equal 'group-staff-developers', group.cn
  end

  test 'dn works as expected' do
    group = Group.new(name: 'Staff & Developers')
    group.save
    assert_equal 'cn=group-staff-developers', group.dn
  end

  test 'validates redmine_id uniqueness' do
    group = Group.new(redmine_id: 1)
    assert group.invalid?
    refute_empty group.errors[:redmine_id]
  end

  test 'is valid with valid parameters' do
    group = Group.new(@valid_params)
    assert group.valid?
  end

  test 'redmine_groups_worker is run upon group creation' do
    assert_difference('RedmineGroupsWorker.jobs.count', 1) do
      Group.create(@valid_params)
    end
  end

  test 'redmine_groups_worker is run upon group edition' do
    assert_difference('RedmineGroupsWorker.jobs.count', 1) do
      groups(:two).update_attribute(:name, 'Edited name')
    end
  end

  test 'redmine_groups_worker is run upon group destruction' do
    assert_difference('RedmineGroupsWorker.jobs.count', 1) do
      groups(:two).destroy
    end
  end

  test 'gets saved with user ids passed' do
    g = Group.new(@valid_params.merge(user_ids: [users(:wiktoria).id]))
    assert g.save
  end

  test 'remove users from group removes user members from projects with this group as member' do
    group = groups(:four)
    projects_with_group_as_member = Membership.where(member_id: group, member_type: 'Group').map(&:project_id)
    assert_difference( 'Membership.where(member_type: \'User\', project_id: projects_with_group_as_member).count', -1) do
      group.users.last.destroy
    end
  end

  test 'adds users to group creates user members in projects with this group as member' do
    group = groups(:four)
    user = users(:mkalita_user)
    projects_with_group_as_member = Membership.where(member_id: group, member_type: 'Group').map(&:project_id)
    assert_difference( 'Membership.where(member_type: \'User\', project_id: projects_with_group_as_member).count', 1) do
      group.users << user
    end
  end

  test 'should touch its active project at user add' do
    group = groups(:four)
    @project = projects(:five)
    user = users(:mkalita_global_admin_programmer)
    assert_difference('LdapWorker.jobs.count', 5) do
      group.users << user
    end
  end

  test 'ldap_raw_attributes' do
    group = groups(:four)

    attributes = group.ldap_raw_attributes(ldap_tree)

    assert_equal group.name, attributes[:description]
    assert_equal group.users.active.pluck(:username).sort, attributes[:memberUid].sort
  end
end
