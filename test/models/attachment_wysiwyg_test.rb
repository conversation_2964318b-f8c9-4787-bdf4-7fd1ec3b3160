require "test_helper"

class AttachmentWysiwygTest < ActiveSupport::TestCase
  test 'create attachment from wysiwyg with jpg' do
    file = Rack::Test::UploadedFile.new(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'), 'image/jpg')
    attachment = AttachmentWysiwyg.new(file: file)
    assert_difference('AttachmentWysiwyg.count', 1) { attachment.save }
  end

  test 'create attachment from wysiwyg with gif' do
    file = Rack::Test::UploadedFile.new(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'), 'image/gif')
    attachment = AttachmentWysiwyg.new(file: file)
    assert_difference('AttachmentWysiwyg.count', 1) { attachment.save }
  end

  test 'create attachment from wysiwyg with mp4' do
    file = Rack::Test::UploadedFile.new(Rails.root.join('test/fixtures/files/mp4.mp4'), 'video/mp4')
    attachment = AttachmentWysiwyg.new(file: file)
    assert_difference('AttachmentWysiwyg.count', 1) { attachment.save }
  end

  test 'does not save attachment from wysiwyg with unvalidated file' do
    file = (Rack::Test::UploadedFile.new(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'), 'image/jpg'))
    file.stubs(:size).returns(15.megabytes)
    attachment = AttachmentWysiwyg.new(file: file)
    assert_no_difference('AttachmentWysiwyg.count') {  attachment.save }
  end
end
