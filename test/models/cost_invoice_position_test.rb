require 'test_helper'

class CostInvoicePositionTest < ActiveSupport::TestCase
  let(:cost_invoice_position) { cost_invoice_positions(:wiktoria_cost_position) }

  should belong_to(:cost_invoice)

  test 'name presence is validated' do
    cost_invoice_position.name = nil

    assert cost_invoice_position.invalid?
    assert_not_empty cost_invoice_position.errors[:name]
  end

  test 'amount presence is validated' do
    cost_invoice_position.amount = nil

    assert cost_invoice_position.invalid?
    refute_empty cost_invoice_position.errors[:amount]
  end

  test 'tax_rate presence is validated' do
    cost_invoice_position.tax_rate = nil

    assert cost_invoice_position.invalid?
    refute_empty cost_invoice_position.errors[:tax_rate]
  end

  test 'unit_price presence is validated' do
    cost_invoice_position.unit_price = nil

    assert cost_invoice_position.invalid?
    refute_empty cost_invoice_position.errors[:unit_price]
  end

  test 'name length is validated' do
    cost_invoice_position.name = 'z' * 256

    assert_not cost_invoice_position.save
    assert_not_empty cost_invoice_position.errors[:name]
  end

  test 'gross_value returns correct output' do
    assert_equal 123, cost_invoice_position.gross_value
  end

  test 'vat_value returns correct output' do
    assert_equal 23, cost_invoice_position.vat_value
  end

  test 'net_value is set to be compatible with unit_price and amount' do
    cost_invoice_position.net_value = 16.82
    cost_invoice_position.amount = 17
    cost_invoice_position.unit_price = 0.99
    cost_invoice_position.valid?

    assert_equal 16.82, cost_invoice_position.net_value

    cost_invoice_position.net_value = 16.2
    cost_invoice_position.valid?

    assert_equal 16.83, cost_invoice_position.net_value

    cost_invoice_position.amount = 0.06
    cost_invoice_position.net_value = 1
    cost_invoice_position.unit_price = 16.66
    cost_invoice_position.valid?

    assert_equal 1, cost_invoice_position.net_value

    cost_invoice_position.unit_price = 16.5
    cost_invoice_position.valid?

    assert_equal 0.99, cost_invoice_position.net_value
  end
end
