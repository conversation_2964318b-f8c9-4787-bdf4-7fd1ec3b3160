require 'test_helper'

class UserLdapEntryableTest < ActiveSupport::TestCase
  setup do
    @valid_params = {
      first_name: '<PERSON><PERSON>',
      last_name: '<PERSON><PERSON><PERSON>',
      password: generate_valid_password,
      password_confirmation: generate_valid_password,
      activates_on: Time.zone.today,
      company_id: companies(:one).id
    }
  end

  let(:ldap_tree) do
    {
      name: 'root', base: 'dc=root,dc=efigence,dc=com', users_tree: 'ou=users',
      groups_tree: 'ou=groups', projects_tree: 'ou=projects', active_users_tree: 'ou=active_users',
      membership_column: 'docs_ldap'
    }
  end

  test 'User#uid is generated properly for existing records' do
    u1 = users(:mikolaj)

    u1.generate_uid_number
    u1.save

    assert_equal('20161801', u1.uid_number)
  end

  test 'User#uid is generated properly if one of the users was removed' do
    Timecop.freeze('2016-05-03') do
      u1 = users(:mikolaj)
      u2 = users(:wik<PERSON>)

      [u1, u2].each(&:generate_uid_number)
      u1.destroy
      u2.save

      user = CreateUser.new.call(@valid_params)
      assert user.valid?
    end
  end

  test 'User#uid is generated properly for new records' do
    Timecop.freeze('2016-05-04') do
      user = CreateUser.new.call(@valid_params)
      assert_equal('20161801', user.uid_number)
    end
  end

  test 'gidNumber is set properly if user has admin global role' do
    user = users(:mkalita_user)
    assert_equal(Settings.ldap_provider['default_admin_gid'], user.gid_number)
  end

  test 'gidNumber is set properly if user has regular global role' do
    user = users(:wiktoria)
    assert_equal(Settings.ldap_provider['default_user_gid'], user.gid_number)
  end

  test 'gidNumber is set properly if overriden in user' do
    gid_number = '1234'
    user = users(:wiktoria)
    user.gid_number = gid_number
    assert_equal(gid_number, user.gid_number)
  end

  test 'ldap_raw_attributes with email aliases work properly' do
    email_alias = '<EMAIL>'
    user = users(:wiktoria)
    user.email_aliases.create!(email: email_alias)

    ldap_raw_attributes = user.ldap_raw_attributes(ldap_tree)

    assert_equal [email_alias], ldap_raw_attributes[:mailAlternateAddress]
  end
end
