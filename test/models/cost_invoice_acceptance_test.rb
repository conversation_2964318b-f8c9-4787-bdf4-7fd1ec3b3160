require 'test_helper'

describe CostInvoiceAcceptance do
  include ActiveSupport::Testing::TimeHelpers

  let(:user) { users(:w<PERSON><PERSON>) }
  let(:comment) { 'comment' }

  should belong_to(:department).optional
  should belong_to(:cost_invoice)

  context 'kind chief' do
    test 'acceptors returns department chief' do
      chief = users(:mkalita_user)
      acceptance = cost_invoice_acceptances(:dms_pending_cost_invoice_acceptance)
      acceptance.chief!
      acceptance.department.update(chief: chief)

      assert_equal acceptance.acceptors, [chief]
    end
  end

  context 'kind uber' do
    test 'acceptors returns department uber chief' do
      uber_chief = users(:mkalita_user)
      acceptance = cost_invoice_acceptances(:dms_pending_cost_invoice_acceptance)
      acceptance.uber!
      acceptance.department.update(uber_chief: uber_chief)

      assert_equal acceptance.acceptors, [uber_chief]
    end
  end

  context 'kind nil' do
    test 'acceptors returns Financial Controllers' do
      acceptance = cost_invoice_acceptances(:dms_pending_cost_invoice_acceptance)
      acceptance.update(kind: nil)

      assert_equal acceptance.acceptors, global_roles(:global_accounting).users.active.to_a
    end
  end
end
