require "test_helper"

class QuestionAnswerTest < ActiveSupport::TestCase
  let(:subject) { question_answers(:onboarding_chief_question_answer) }
  let(:survey_answer) { subject.survey_answer }

  should validate_presence_of :question_content

  test 'validates answer if the survey answer is completed' do
    survey_answer.state = :completed
    assert subject.invalid?
    refute_empty subject.errors[:content]
  end

  test "doesn't validate answer presence if survey_answer is pending" do
    assert subject.valid?
  end
end
