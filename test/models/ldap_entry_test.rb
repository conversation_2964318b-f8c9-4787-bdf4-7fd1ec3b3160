require 'test_helper'

class LdapEntryTest < ActiveSupport::TestCase
  setup do
    @ldap_data = Minitest::Mock.new
  end

  should belong_to(:entryable)

  describe 'callbacks' do
    let(:user) { users(:milosz) }
    let(:ldap_entry) { ldap_entries(:wiktoria_root) }

    test 'creates entry in ldap server after LdapEntry create' do
      @ldap_data.expect(:create_entry_in_ldap_server, nil, [LdapEntry])

      LdapData.stub :new, @ldap_data do
        LdapEntry.create(entryable: user)
      end

      assert_mock @ldap_data
    end

    test 'destroys entry in ldap server after LdapEntry destroy' do
      @ldap_data.expect(:destroy_entry_in_ldap_server, nil, [ldap_entry.dn])

      LdapData.stub :new, @ldap_data do
        ldap_entry.destroy
      end

      assert_mock @ldap_data
    end

    test 'changes entry in ldap server after LdapEntry update' do
      old_dn, new_dn = ldap_entry.dn, FFaker::Lorem.word
      old_ldap_attributes, new_ldap_attributes = ldap_entry.ldap_attributes.stringify_keys, FFaker::Lorem.word

      @ldap_data.expect(:rename_entry_in_ldap_server, nil, [[old_dn, new_dn]])
      @ldap_data.expect(:modify_entry_in_ldap_server, nil, [new_dn, [old_ldap_attributes, new_ldap_attributes]])

      LdapData.stub :new, @ldap_data do
        ldap_entry.update(dn: new_dn, ldap_attributes: new_ldap_attributes)
      end

      assert_mock @ldap_data
    end
  end
end
