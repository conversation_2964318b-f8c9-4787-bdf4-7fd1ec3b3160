require "test_helper"

describe RiskAnalysisItem do
  let(:risk_analysis) { risk_analyses(:one) }
  let(:valid_attributes) do
    {
      risk_analysis: risk_analysis,
      property: :confidentiality,
      danger: "Unauthorized access to personal data",
      vulnerability_description: "Weak password policies and lack of two-factor authentication",
      security: "Implement strong password policies and enable two-factor authentication",
      probability: :possible,
      effect: :high,
      data_processing_impact_assessment: :required
    }
  end

  should belong_to(:risk_analysis)

  should validate_presence_of(:property)
  should validate_presence_of(:danger)
  should validate_presence_of(:vulnerability_description)
  should validate_presence_of(:security)
  should validate_presence_of(:probability)
  should validate_presence_of(:effect)
  should validate_presence_of(:data_processing_impact_assessment)

  describe "validations" do
    it "is valid with valid attributes" do
      item = RiskAnalysisItem.new(valid_attributes)
      assert item.valid?
    end
  end

  describe "risk calculation" do
    it "calculates low risk for rare probability and very_low effect" do
      item = RiskAnalysisItem.new(valid_attributes.merge(probability: :rare, effect: :very_low))
      item.save!
      assert_equal "low", item.risk
      assert item.risk_low?
    end

    it "calculates medium risk for unlikely probability and medium effect" do
      item = RiskAnalysisItem.new(valid_attributes.merge(probability: :unlikely, effect: :medium))
      item.save!
      assert_equal "medium", item.risk
    end

    it "calculates high risk for probable probability and medium effect" do
      item = RiskAnalysisItem.new(valid_attributes.merge(probability: :probable, effect: :medium))
      item.save!
      assert_equal "high", item.risk
      assert item.risk_high?
    end

    it "calculates critical risk for almost_certain probability and very_high effect" do
      item = RiskAnalysisItem.new(valid_attributes.merge(probability: :almost_certain, effect: :very_high))
      item.save!
      assert_equal "critical", item.risk
      assert item.risk_critical?
    end

    it "recalculates risk when probability changes" do
      item = RiskAnalysisItem.create!(valid_attributes.merge(probability: :rare, effect: :very_low))
      assert_equal "low", item.risk

      item.update!(probability: :almost_certain)
      assert_equal "medium", item.risk
    end

    it "recalculates risk when effect changes" do
      item = RiskAnalysisItem.create!(valid_attributes.merge(probability: :possible, effect: :low))
      assert_equal "medium", item.risk

      item.update!(effect: :very_high)
      assert_equal "critical", item.risk
    end
  end
end
