require "test_helper"

class MpkPositionTest < ActiveSupport::TestCase
  def mpk_position
    @mpk_position ||= MpkPosition.new(
      amount: 5, invoice: invoices(:payment_three),
      mpk_number: mpk_numbers(:motion_design)
    )
  end

  should belong_to(:project).optional

  test 'amount presence is validated' do
    mpk_position.amount = nil
    assert mpk_position.invalid?
    refute_empty mpk_position.errors[:amount]
  end

  test 'amount numericality is validated' do
    mpk_position.amount = -1
    assert mpk_position.invalid?
    refute_empty mpk_position.errors[:amount]
  end

  test 'mpk number presence is validated' do
    mpk_position.mpk_number = nil
    assert mpk_position.invalid?
    refute_empty mpk_position.errors[:mpk_number]
  end

  test 'invoice presence is validated' do
    mpk_position.invoice = nil
    assert mpk_position.invalid?
    refute_empty mpk_position.errors[:invoice]
  end

  test 'project has the same client and company as the main project' do
    mpk_position = mpk_positions(:payment_ten_position)
    mpk_position.project = projects(:three)

    assert mpk_position.invalid?
    assert_not_empty mpk_position.errors[:project]
  end

  test 'project has a payment schedule' do
    mpk_position = mpk_positions(:payment_ten_position)
    mpk_position.project = projects(:six)

    assert mpk_position.invalid?
    assert_includes mpk_position.errors[:project], 'must have a payment schedule.'
  end

  test 'valid' do
    assert mpk_position.valid?
  end
end
