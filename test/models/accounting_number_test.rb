require 'test_helper'

class AccountingNumberTest < ActiveSupport::TestCase
  include ActiveJob::TestHelper

  should belong_to(:company)
  should belong_to(:user).optional
  should have_many(:projects)

  should validate_presence_of(:company_id)
  should validate_presence_of(:description)
  should validate_uniqueness_of(:description).case_insensitive

  test 'accepts user on creation' do
    accounting_number = AccountingNumber.new(user: users(:wiktoria),
                                             number: 6,
                                             company: companies(:two),
                                             description: 'Project name')

    assert accounting_number.valid?
  end

  test 'accepts lack of user on creation' do
    accounting_number = AccountingNumber.new(number: 6,
                                             company: companies(:two),
                                             description: 'Project name')

    assert accounting_number.valid?
  end

  test 'rejects user change on existing record' do
    accounting_number = accounting_numbers(:one)
    accounting_number.user = users(:milosz)

    assert accounting_number.invalid?
    assert_not_empty accounting_number.errors[:user_id]
  end

  test 'generates number' do
    accounting_number = AccountingNumber.create(company: companies(:one),
                                                description: 'Short description',
                                                user_id: users(:wiktoria).id)

    assert accounting_number.number.present?
  end

  test 'notifies on overhead change' do
    accounting_number = accounting_numbers(:one)

    assert_difference -> { enqueued_jobs.count } do
      accounting_number.update(overhead: true)
    end
  end

  test 'does not notify if overhead is not changed' do
    accounting_number = accounting_numbers(:one)

    assert_no_difference -> { enqueued_jobs.count } do
      accounting_number.update(description: 'Example description')
    end
  end

  test 'accounting number with projects cannot be destroyed' do
    accounting_number = accounting_numbers(:two)

    assert accounting_number.projects.present?
    assert_not accounting_number.destroy
    assert accounting_number.errors.added?(:projects, :present)
  end

  test 'accounting number with no project can be destroyed' do
    accounting_number = AccountingNumber.create(company: companies(:one),
                                                description: 'Short description',
                                                user_id: users(:wiktoria).id)

    assert accounting_number.projects.blank?
    assert accounting_number.destroy
  end
end
