require "test_helper"

describe RiskAnalysis do
  let(:valid_attributes) do
    {
      name: "Test Risk Analysis",
      company: companies(:one),
      registry_activity: registry_activities(:one),
      creation_date: Date.today.to_s
    }
  end

  should belong_to(:company)
  should belong_to(:registry_activity).optional
  should have_many(:risk_analysis_items).dependent(:destroy)

  should validate_presence_of(:name)
  should validate_presence_of(:company_id)
  should validate_presence_of(:registry_activity_id)
  should validate_presence_of(:creation_date)

  describe "validations" do
    it "is valid with valid attributes" do
      risk_analysis = RiskAnalysis.new(valid_attributes)
      assert risk_analysis.valid?
    end
  end

  describe "associations" do
    it "can have multiple risk analysis items" do
      risk_analysis = RiskAnalysis.create!(valid_attributes)

      item1 = risk_analysis.risk_analysis_items.create!(
        property: :confidentiality,
        danger: "Data breach",
        vulnerability_description: "Weak passwords",
        security: "Strong passwords",
        probability: :possible,
        effect: :high,
        data_processing_impact_assessment: :required
      )

      item2 = risk_analysis.risk_analysis_items.create!(
        property: :availability,
        danger: "System downtime",
        vulnerability_description: "Single point of failure",
        security: "Redundancy",
        probability: :rare,
        effect: :medium,
        data_processing_impact_assessment: :not_required
      )

      assert_equal 2, risk_analysis.risk_analysis_items.count
      assert_includes risk_analysis.risk_analysis_items, item1
      assert_includes risk_analysis.risk_analysis_items, item2
    end
  end
end
