require 'test_helper'

class ClientAddressTest < ActiveSupport::TestCase
  let(:client_address_name) { 'EFIGENCE SPÓŁKA AKCYJNA' }
  let(:vat_number) { '5213018541' }

  before do
    double = mock('gus_bir_client')
    double.stubs(:find_by).with(nip: vat_number).returns(
      [OpenStruct.new(
        name: client_address_name,
        regon: '014988443',
        province: 'MAZOWIECKIE',
        district: 'm. st. Warszawa',
        community: 'Mokotów',
        city: 'Warszawa',
        zip_code: '02-583',
        street: 'ul. Test-Krucza',
        street_number: '9A',
        house_number: nil,
        type: 'P',
        silos_id: '6',
        type_desc: 'Typ jednostki – jednostka prawna',
        silos_desc: '<PERSON><PERSON><PERSON><PERSON> prowadzenia działalności jednostki prawnej',
        report: 'PublDaneRaportPrawna',
        post_city: 'Warszawa'
      )]
    )
    GUS_BIR_CLIENT.stubs(:with).yields(double)

    VATRegister.stubs(:vat_subject?).with(vat_number).returns(true)
  end

  should validate_presence_of :name
  should validate_presence_of :city
  should validate_presence_of :postcode
  should validate_presence_of :post
  should validate_presence_of :country
  should validate_presence_of :street_number
  should validate_presence_of :identifier

  it 'validates presence of invoice_sending_email if electronic sending' do
    client_address = ClientAddress.new
    client_address.invoice_sending_method = :electronic

    assert client_address.invalid?
    assert_not_empty client_address.errors[:invoice_sending_email]

    client_address.invoice_sending_method = :paper
    client_address.valid?

    assert_empty client_address.errors[:invoice_sending_email]
  end

  test 'downloads client address from gus if download_from_gus is checked' do
    client = clients(:arte)

    client_address = ClientAddress.new(
      client: client,
      vat_number: vat_number,
      download_from_gus: true,
      identifier: 'Efi-identifier'
    )

    assert client_address.save

    assert_equal client_address_name, client_address.name
    assert_equal client, client_address.client
  end

  test 'downloads client address from gus if download_from_gus is checked and manually changed later' do
    client = clients(:arte)
    updated_name = 'EFIGENCE SPÓŁKA AKCYJNA UPDATED'

    client_address = ClientAddress.new(
      client: client,
      vat_number: vat_number,
      download_from_gus: true,
      identifier: 'Efi-identifier'
    )

    assert client_address.save

    assert_equal client_address_name, client_address.name
    assert_equal client, client_address.client
    assert client_address.download_from_gus

    client_address.download_from_gus = false
    client_address.name = updated_name

    assert client_address.save

    assert_equal updated_name, client_address.name
    assert_equal client, client_address.client
    assert_not client_address.download_from_gus
  end

  test 'returns validation error if download_from_gus is checked and gus returns nothing' do
    vat_number = '56486621586'
    client = clients(:arte)
    double = mock('gus_bir_client')
    double.stubs(:find_by).with(nip: vat_number).returns(
      [OpenStruct.new(
        name: nil,
        regon: nil,
        province: nil,
        district: nil,
        community: nil,
        city: nil,
        zip_code: nil,
        street: nil,
        street_number: nil,
        house_number: nil,
        street_address: ' ',
        type: nil,
        silos_id: nil,
        type_desc: nil,
        silos_desc: nil,
        report: nil,
        post_city: nil
      )]
    )
    GUS_BIR_CLIENT.stubs(:with).yields(double)

    client_address = ClientAddress.new(
      client: client,
      vat_number: vat_number,
      download_from_gus: true,
      identifier: 'Efi-identifier'
    )

    assert client_address.invalid?
    assert_not_empty client_address.errors[:download_from_gus]
  end
end
