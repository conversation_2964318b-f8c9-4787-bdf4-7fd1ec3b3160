require 'test_helper'

class CostAccountNumberTest < ActiveSupport::TestCase
  should validate_presence_of(:number)
  should validate_length_of(:number).is_at_most(255)
  should validate_uniqueness_of(:number).case_insensitive
  should validate_presence_of(:description)
  should validate_length_of(:description).is_at_most(255)

  test 'check other cost account numbers as not default while checking one as default' do
    currently_default = cost_account_numbers(:one)
    default_to_be = cost_account_numbers(:two)

    default_to_be.update(default: true)

    assert_not currently_default.reload.default?
  end
end
