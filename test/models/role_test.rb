require 'test_helper'

class RoleTest < ActiveSupport::TestCase
  setup do
    @valid_params = { name: 'New role name' }
  end

  test 'validate name' do
    role = Role.new
    assert role.invalid?
    refute_empty role.errors[:name]
    role.name = roles(:dev).name
    assert role.invalid?
    refute_empty role.errors[:name]
  end

  test 'is valid with valid params' do
    role = Role.new(name: 'Role name')
    assert role.valid?
  end

  test 'redmine_roles_worker is run upon role creation' do
    assert_difference('RedmineRolesWorker.jobs.count', 1) do
      Role.create(@valid_params)
    end
  end

  test 'redmine_roles_worker is run upon role edition' do
    assert_difference('RedmineRolesWorker.jobs.count', 1) do
      roles(:dev).update_attribute(:name, 'Ruby Developer')
    end
  end

  test 'redmine_roles_worker is run upon role destruction' do
    assert_difference('RedmineRolesWorker.jobs.count', 1) do
      roles(:unused_role).destroy
    end
  end

  test 'raises an error on attempt of destroying used role' do
    assert_no_difference('Role.count') { roles(:dev).destroy }
  end
end
