require 'test_helper'

describe RemoteWorkPeriod do
  let(:examiner) { users(:wik<PERSON>) }

  should belong_to(:user)
  should belong_to(:examiner).optional

  should validate_presence_of(:starts_on)
  should validate_presence_of(:ends_on)
  should validate_presence_of(:place_of_work)

  test 'validates user remote_allowed' do
    remote_work_period = RemoteWorkPeriod.new(user: users(:wiktoria), starts_on: 1.week.ago.to_date,
                                              ends_on: Time.zone.now)

    assert remote_work_period.invalid?
    assert_not_empty remote_work_period.errors[:user]
  end

  test 'validates user yearly limit exceeded' do
    user = users(:milosz)
    user.remote_yearly_limit = 1

    remote_work_period = RemoteWorkPeriod.create(user: user, starts_on: Time.zone.today,
                                                 ends_on: 1.week.from_now.to_date,
                                                 place_of_work: 'My place')
    remote_work_period.accept(examiner:, comment: 'Examiner comment')

    assert remote_work_period.invalid?
    assert_not_empty remote_work_period.errors[:starts_on]

    remote_work_period.reload
    remote_work_period.accept(examiner:, comment: 'Examiner comment', force: true)

    assert remote_work_period.valid?
  end

  test 'validates other period overlapping' do
    user = users(:milosz)
    remote_work_period = user.remote_work_periods.new(starts_on: '2023-06-01',
                                                      ends_on: '2023-06-05')

    assert remote_work_period.invalid?
    assert_not_empty remote_work_period.errors[:starts_on]
  end

  test 'validates covers at least one business day' do
    user = users(:milosz)
    remote_work_period = user.remote_work_periods.new(starts_on: '2023-06-25',
                                                      ends_on: '2023-06-25')

    assert remote_work_period.invalid?
    assert_equal 'At least one business day should be covered.',
                 remote_work_period.errors[:starts_on].first
  end

  test 'validate user change' do
    user = users(:mikolaj)
    user.update(remote_allowed: true, remote_yearly_limit: 10, contract_of_employment: true)
    remote_work_period = remote_work_periods(:milosz_remote_period)

    remote_work_period.user = user

    assert remote_work_period.invalid?
    assert_equal 'User should never be changed.', remote_work_period.errors[:user].first
  end

  test 'examiner and examiner comment are saved' do
    comment = 'Examiner comment'
    remote_work_period = remote_work_periods(:milosz_remote_period)
    remote_work_period.accept!(examiner:, examiner_comment: comment)

    assert_equal examiner, remote_work_period.reload.examiner
    assert_equal comment, remote_work_period.examiner_comment
  end
end
