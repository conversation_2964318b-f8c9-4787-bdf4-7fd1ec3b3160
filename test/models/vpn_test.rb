require "test_helper"

class VpnTest < ActiveSupport::TestCase
  fixtures :vpns

  should validate_presence_of(:requested_date)

  should belong_to(:project).optional
  should belong_to(:requester)
  should belong_to(:activated_by).optional

  describe 'validate_user_or_project' do
    setup do
      @asset = vpns(:vpn_two)
    end

    it 'is valid if only user assigned' do
      @asset.project_id = nil

      assert @asset.valid?
    end

    it 'is valid if only project assigned' do
      @asset.user_id = nil

      assert @asset.valid?
    end

    it 'returns error if no user or project assigned' do
      @asset.user_id = nil
      @asset.project_id = nil

      assert_not @asset.valid?
    end
  end

  describe 'validate user department in case of project absence' do
    let(:asset) { vpns(:vpn_two) }

    it 'is valid with user with department' do
      asset.project = nil

      assert asset.valid?
    end

    it 'is invalid with user without department' do
      user = users(:wiktoria)
      user.update(department: nil)
      asset.project = nil
      asset.user = user

      assert asset.invalid?
      assert_not_empty asset.errors[:user_id]
    end
  end
end
