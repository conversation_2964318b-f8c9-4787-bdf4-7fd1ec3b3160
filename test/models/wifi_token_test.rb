require 'test_helper'

class WifiTokenTest < ActiveSupport::TestCase
  setup do
    @mock = Minitest::Mock.new
    @wifi_token = WifiToken.new
    @wifi_token.client = @mock
    @user = users(:mkalita_user)
  end

  test 'generates token properly' do
    travel_to(Time.zone.now)
    @wifi_token.purpose = 'Wifi token'
    @wifi_token.user = @user
    @wifi_token.hours_valid = 5
    @mock.expect(:call, { 'ok' => true }) do |arg|
      assert arg.is_a?(Hash)
      assert arg.key?('ttl')
      assert arg.key?('description')
      assert arg.key?('user')
      assert arg.key?('token')
    end

    @wifi_token.save

    @mock.verify
    assert_not_empty @wifi_token.token
    assert_equal Time.zone.now + 5.hours, @wifi_token.expires_at
  end

  test 'validates hours_valid presence' do
    assert @wifi_token.invalid?
    assert_not_empty @wifi_token.errors['hours_valid']
  end

  test 'validates purpose presence' do
    assert @wifi_token.invalid?
    assert_not_empty @wifi_token.errors['purpose']
  end
end
