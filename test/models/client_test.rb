require 'test_helper'

class ClientTest < ActiveSupport::TestCase
  let(:client_name) { 'EFIGENCE SPÓŁKA AKCYJNA' }
  let(:vat_number) { '5213018541' }

  before do
    double = mock('gus_bir_client')
    double.stubs(:find_by).with(nip: vat_number).returns(
      [OpenStruct.new(
        name: client_name,
        regon: '014988443',
        province: 'MAZOWIECKIE',
        district: 'm. st. Warszawa',
        community: 'Mokotów',
        city: 'Warszawa',
        zip_code: '02-583',
        street: 'ul. Test-Krucza',
        street_number: '9A',
        house_number: nil,
        type: 'P',
        silos_id: '6',
        type_desc: 'Typ jednostki – jednostka prawna',
        silos_desc: 'Miejsce prowadzenia działalności jednostki prawnej',
        report: 'PublDaneRaportPrawna',
        post_city: 'Warszawa'
      )]
    )
    GUS_BIR_CLIENT.stubs(:with).yields(double)

    VATRegister.stubs(:vat_subject?).with(vat_number).returns(true)
  end

  should validate_presence_of :street_number
  should validate_presence_of :post
  should validate_presence_of :invoice_sending_method

  test 'valid' do
    assert client.valid?
  end

  test 'name presence is validated' do
    client.name = ''
    assert client.invalid?
    refute_empty client.errors[:name]
  end

  test 'name uniqueness is validated' do
    client.name = 'Artegence'
    assert client.invalid?
    refute_empty client.errors[:name]
  end

  test 'city presence is validated' do
    client.city = ''
    assert client.invalid?
    refute_empty client.errors[:city]
  end

  test 'postcode presence is validated' do
    client.postcode = ''
    assert client.invalid?
    refute_empty client.errors[:postcode]
  end

  test 'country presence is validated' do
    client.country = ''
    assert client.invalid?
    refute_empty client.errors[:country]
  end

  test 'vat_number presence is validated' do
    client.vat_number = ''

    assert client.invalid?
    refute_empty client.errors[:vat_number]
  end

  test 'invoice_sending_email presence is validated iff electronic invoice sending method' do
    assert client.valid?

    client.invoice_sending_method = :electronic

    assert client.invalid?
    refute_empty client.errors[:invoice_sending_email]
  end

  test 'create client from GUS with paper invoice' do
    client = Client.create(
      invoice_sending_method: :paper,
      vat_number: vat_number,
      download_from_gus: true
    )

    assert client.valid?
    assert_equal client.name, 'EFIGENCE SPÓŁKA AKCYJNA'
  end

  test 'create client from GUS with electronic invoice' do
    client = Client.create(
      invoice_sending_method: :electronic,
      invoice_sending_email: '<EMAIL>',
      vat_number: vat_number,
      download_from_gus: true
    )

    assert client.valid?
    assert_equal client.name, 'EFIGENCE SPÓŁKA AKCYJNA'
  end

  test 'downloads client from gus if download_from_gus is checked' do
    client = Client.new(
      vat_number: vat_number,
      download_from_gus: true,
      invoice_sending_method: 'paper'
    )

    assert client.save

    assert_equal client_name, client.name
  end

  test 'returns validation error if download_from_gus is checked and gus returns nothing' do
    vat_number = '5213018541'
    double = mock('gus_bir_client')
    double.stubs(:find_by).with(nip: vat_number).returns(
      [OpenStruct.new(
        name: nil,
        regon: nil,
        province: nil,
        district: nil,
        community: nil,
        city: nil,
        zip_code: nil,
        street: nil,
        street_number: nil,
        house_number: nil,
        street_address: ' ',
        type: nil,
        silos_id: nil,
        type_desc: nil,
        silos_desc: nil,
        report: nil,
        post_city: nil
      )]
    )
    GUS_BIR_CLIENT.stubs(:with).yields(double)

    client = Client.new(
      vat_number: vat_number,
      download_from_gus: true,
      invoice_sending_method: 'paper'
    )

    assert client.invalid?
    assert_not_empty client.errors[:download_from_gus]
  end

  private

  def client
    @client ||= Client.new(
      name: 'Efigence',
      street: 'Wołoska',
      street_number: '9a',
      city: 'Warszawa',
      postcode: '00-999',
      post: 'Warszawa',
      voivodeship: 'mazowieckie',
      district: 'Warszawa',
      community: 'Mokotów',
      country: 'pl',
      vat_number: '2525222525',
      invoice_sending_method: :paper
    )
  end
end
