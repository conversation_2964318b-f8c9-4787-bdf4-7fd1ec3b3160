require 'test_helper'

class HolidayRequestTest < ActiveSupport::TestCase
  should validate_presence_of(:applicant_id)
  should validate_presence_of(:starts_on)
  should validate_presence_of(:ends_on)
  should validate_presence_of(:category)

  should validate_absence_of(:hours)

  setup do
    @mock = MiniTest::Mock.new
    RequestStore.store[Userstamping.config[:store_key]] = users(:mkalita_user).to_global_id.to_s
  end

  def test_create_holiday_request
    Absence.delete_all
    absence_count = Absence.count
    save_holiday_request
    SyncAbsencesJob.new.perform(@holiday_request, @holiday_request.previous_changes)
    assert_equal(9, @holiday_request.absences_count)
  end

  def test_update_absences
    Absence.delete_all
    save_holiday_request
    @holiday_request.update(
      applicant_id: users(:mkalita_user).id,
      starts_on: '2040-01-01',
      ends_on: '2040-01-02',
      updated_by_user_id: users(:mkalita_user).id,
      modified_by_user_at: Time.zone.now
    )
    SyncAbsencesJob.new.perform(@holiday_request, @holiday_request.previous_changes)
    assert_equal(@original_absence_count - 8, @holiday_request.absences_count)
  end

  def test_update_absences_for_non_working_day_request
    Absence.delete_all
    save_holiday_request
    @holiday_request.update(non_working_day_request: true)
    SyncAbsencesJob.new.perform(@holiday_request, @holiday_request.previous_changes)
    assert_equal(@original_absence_count + 5, @holiday_request.absences_count)
  end

  def test_update_absences_while_starts_on_ends_on_update
    Timecop.freeze('2018-12-18') do
      user = users(:mkalita_user)
      user.update_attribute(:absence_balance, 20)
      now = Time.zone.now
      holiday_request = HolidayRequest.new(
        applicant: user, starts_on: '2019-01-01', ends_on: '2019-01-03', accepted_at: now,
        created_by_user_id: user.id, updated_by_user_id: user.id, modified_by_user_at: now
      )

      assert_difference('user.reload.absence_balance', -2) do
        holiday_request.update(starts_on: '2018-12-28')
      end
    end
  end

  def test_upload_document
    holiday_request = holiday_requests(:milosz_parental_holiday)
    original_file = File.open(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'))
    tempfile = Tempfile.new('jpg_705kb.jpg')
    tempfile.write(original_file.read)
    holiday_request.file = tempfile
    holiday_request.save
    file = holiday_request.file
    expect File.exist?(file.storage.path(file.id))
  end

  def test_destroy_absences
    Absence.delete_all
    save_holiday_request
    @holiday_request.destroy
    SyncAbsencesJob.new.perform(@holiday_request, @holiday_request.previous_changes)
    assert_equal(@original_absence_count - 9, Absence.count)
  end

  def test_create_time_entries
    request = holiday_requests(:one)
    range = request.starts_on..request.ends_on
    @mock.expect(:perform_async, true, ['create', request.applicant_id, request.id,
                                        range.begin.to_s, range.end.to_s])
    request.time_entries_worker = @mock
    request.update(visible: true,
                   updated_by_user_id: users(:mkalita_user).id,
                   modified_by_user_at: Time.zone.now)
    @mock.verify
  end

  def test_update_time_entries
    request = holiday_requests(:visible_holiday_request)
    initial_holiday_date = request.starts_on
    new_start = initial_holiday_date + 2
    new_end = initial_holiday_date + 5
    old_range = initial_holiday_date..initial_holiday_date
    new_range = new_start..new_end
    @mock.expect(:perform_async, true, ['update', request.applicant_id, request.id,
                                        old_range.begin.to_s, old_range.end.to_s, new_range.begin.to_s, new_range.end.to_s])
    request.time_entries_worker = @mock
    request.update(starts_on: new_start,
                   ends_on: new_end,
                   updated_by_user_id: users(:mkalita_user).id,
                   modified_by_user_at: Time.zone.now)
    @mock.verify
  end

  def test_create_time_entries_for_sick_leave
    request = build_holiday_request
    request.assign_attributes(HolidayRequest.accept_params)
    request.time_entries_worker = @mock
    @mock.expect(:perform_async, true, ['create', request.applicant_id, Integer,
                                        request.starts_on.to_s, request.ends_on.to_s])
    request.save!
    @mock.verify
  end

  def test_delete_time_entries_with_proper_range_if_visibility_changed_to_false
    request = holiday_requests(:visible_holiday_request)
    request.time_entries_worker = @mock
    range = request.starts_on..request.ends_on + 3
    @mock.expect(:perform_async, true, ['destroy', request.applicant_id, request.id, range.begin.to_s, range.end.to_s])
    request.update(visible: false,
                   starts_on: request.starts_on + 1,
                   ends_on: request.ends_on + 3,
                   updated_by_user_id: users(:mkalita_user).id,
                   modified_by_user_at: Time.zone.now)
    @mock.verify
  end

  def test_delete_time_entries_if_request_deleted
    request = holiday_requests(:visible_holiday_request)
    request.time_entries_worker = @mock
    range = request.starts_on..request.ends_on
    @mock.expect(:perform_async, true, ['destroy', request.applicant_id, nil, range.begin.to_s, range.end.to_s])
    request.destroy
    @mock.verify
  end

  def test_create_resource_bookings
    request = holiday_requests(:one)
    range = request.starts_on..request.ends_on
    @mock.expect(:perform_async, true, ['create', request.applicant_id, request.hours,
                                        range.begin.to_s, range.end.to_s])
    request.resource_bookings_worker = @mock
    request.update(visible: true,
                   updated_by_user_id: users(:mkalita_user).id,
                   modified_by_user_at: Time.zone.now)
    @mock.verify
  end

  def test_update_resource_bookings
    request = holiday_requests(:visible_holiday_request)
    initial_holiday_date = request.starts_on
    new_start = initial_holiday_date + 2
    new_end = initial_holiday_date + 5
    old_range = initial_holiday_date..initial_holiday_date
    new_range = new_start..new_end
    @mock.expect(:perform_async, true, ['update', request.applicant_id, request.hours,
                                        old_range.begin.to_s, old_range.end.to_s, new_range.begin.to_s, new_range.end.to_s])
    request.resource_bookings_worker = @mock
    request.update(starts_on: new_start,
                   ends_on: new_end,
                   updated_by_user_id: users(:mkalita_user).id,
                   modified_by_user_at: Time.zone.now)
    @mock.verify
  end

  def test_delete_resource_bookings_if_request_deleted
    request = holiday_requests(:visible_holiday_request)
    request.resource_bookings_worker = @mock
    range = request.starts_on..request.ends_on
    @mock.expect(:perform_async, true, ['destroy', request.applicant_id, request.hours, range.begin.to_s, range.end.to_s])
    request.destroy
    @mock.verify
  end

  def test_create_outlook_calendar_event
    request = holiday_requests(:one)
    OutlookCalendarEventsWorker.expects(:perform_async)
                               .with('create', request.applicant_id, request.id, request.outlook_calendar_event_id)

    request.update(visible: true,
                   updated_by_user_id: users(:mkalita_user).id,
                   modified_by_user_at: Time.zone.now)
  end

  def test_update_outlook_calendar_event
    request = holiday_requests(:visible_holiday_request)
    initial_holiday_date = request.starts_on
    new_start = initial_holiday_date + 2
    new_end = initial_holiday_date + 5
    OutlookCalendarEventsWorker.expects(:perform_async)
                               .with('update', request.applicant_id, request.id, request.outlook_calendar_event_id)

    request.update(starts_on: new_start,
                   ends_on: new_end,
                   updated_by_user_id: users(:mkalita_user).id,
                   modified_by_user_at: Time.zone.now)
  end

  def test_destroy_outlook_calendar_event_if_request_deleted
    request = holiday_requests(:visible_holiday_request)
    OutlookCalendarEventsWorker.expects(:perform_async)
                               .with('destroy', request.applicant_id, nil, request.outlook_calendar_event_id)

    request.destroy
  end

  def test_validate_holiday_request_does_not_overlap_other_holiday_request
    save_holiday_request
    # new record
    @hr = HolidayRequest.new(
      applicant_id: users(:mkalita_user).id,
      starts_on: '2040-01-01',
      ends_on: '2040-01-14',
      category: 'Niedostępność',
      created_by_user_id: users(:mkalita_user_alternative).id,
      updated_by_user_id: users(:mkalita_user_alternative).id,
      modified_by_user_at: Time.zone.now
    )
    refute @hr.valid?
    @hr.starts_on = '2039-12-30'
    @hr.ends_on = '2040-01-01'
    refute @hr.valid?
    @hr.starts_on = '2039-12-30'
    @hr.ends_on = '2040-01-02'
    refute @hr.valid?
    @hr.starts_on = '2039-12-30'
    @hr.ends_on = '2039-12-30'
    assert @hr.valid?, @hr.errors.inspect
    @hr.starts_on = '2040-01-13'
    @hr.ends_on = '2040-01-15'
    refute @hr.valid?
    @hr.starts_on = '2040-01-14'
    @hr.ends_on = '2040-01-15'
    refute @hr.valid?
    @hr.starts_on = '2040-01-16'
    @hr.ends_on = '2040-01-16'
    assert @hr.valid?, @hr.errors.inspect
    @hr.starts_on = '2040-01-02'
    @hr.ends_on = '2040-01-13'
    refute @hr.valid?
    @hr.starts_on = '2039-12-31'
    @hr.ends_on = '2040-01-15'
    refute @hr.valid?
    # existing record
    @hr = HolidayRequest.new(
      applicant_id: users(:mkalita_user).id,
      starts_on: '2040-01-16',
      ends_on: '2040-01-20',
      category: 'Niedostępność',
      created_by_user_id: users(:mkalita_user_alternative).id,
      updated_by_user_id: users(:mkalita_user_alternative).id,
      modified_by_user_at: Time.zone.now
    )
    assert @hr.save
    @hr.starts_on = '2039-12-30'
    @hr.ends_on = '2040-01-01'
    refute @hr.valid?
    @hr.starts_on = '2039-12-30'
    @hr.ends_on = '2040-01-02'
    refute @hr.valid?
    @hr.starts_on = '2039-12-30'
    @hr.ends_on = '2039-12-30'
    assert @hr.valid?, @hr.errors.inspect
    @hr.starts_on = '2040-01-13'
    @hr.ends_on = '2040-01-15'
    refute @hr.valid?
    @hr.starts_on = '2040-01-14'
    @hr.ends_on = '2040-01-15'
    refute @hr.valid?
    @hr.starts_on = '2040-01-16'
    @hr.ends_on = '2040-01-16'
    assert @hr.valid?, @hr.errors.inspect
    @hr.starts_on = '2040-01-02'
    @hr.ends_on = '2040-01-13'
    refute @hr.valid?
    @hr.starts_on = '2039-12-31'
    @hr.ends_on = '2040-01-15'
    refute @hr.valid?
  end

  context 'absence occasional' do
    subject { holiday_requests(:milosz_occassional_holiday) }

    should validate_numericality_of(:hours).only_integer
                                           .is_greater_than_or_equal_to(1)
                                           .is_less_than_or_equal_to(7)
                                           .allow_nil
  end

  private

  def build_holiday_request
    HolidayRequest.new(
      applicant_id: users(:mkalita_user).id,
      starts_on: '2040-01-01',
      ends_on: '2040-01-14',
      created_by_user_id: users(:mkalita_user_alternative).id,
      updated_by_user_id: users(:mkalita_user_alternative).id,
      modified_by_user_at: Time.zone.now
    )
  end

  def save_holiday_request
    @holiday_request = build_holiday_request
    assert @holiday_request.save, @holiday_request.errors.inspect
    SyncAbsencesJob.new.perform(@holiday_request, @holiday_request.previous_changes)
    @original_absence_count = Absence.where(holiday_request_id: @holiday_request.id).count
    @holiday_request
  end
end
