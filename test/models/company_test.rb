require 'test_helper'

class CompanyTest < ActiveSupport::TestCase
  test 'validates name presence' do
    company = Company.new
    assert company.invalid?
    assert_includes company.errors[:name], "can't be blank"
  end

  test 'validates name uniqueness' do
    company = Company.new(name: companies(:one).name)
    assert company.invalid?
    refute_empty company.errors[:name]
  end

  test 'should be valid with valid attributes' do
    company = Company.new(name: 'Company name', domain: 'companydomain.com')
    assert company.valid?
  end
end
