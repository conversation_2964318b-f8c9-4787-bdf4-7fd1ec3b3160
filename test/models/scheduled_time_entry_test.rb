require 'test_helper'

describe ScheduledTimeEntry do
  should belong_to(:project)
  should belong_to(:user)
  should belong_to(:issue).optional

  should validate_presence_of(:date_from)

  should validate_presence_of(:hours)
  should validate_numericality_of(:hours).is_greater_than_or_equal_to(0)

  it 'should validate date_to is after or equal to date_from' do
    time_entry = resource_time_entries(:milosz_scheduled_time_entry)

    time_entry.date_to = time_entry.date_from - 1.day
    assert time_entry.invalid?

    time_entry.date_to = time_entry.date_from
    assert time_entry.valid?
  end
end
