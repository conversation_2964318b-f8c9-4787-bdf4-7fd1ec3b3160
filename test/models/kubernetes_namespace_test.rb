require 'test_helper'

class KubernetesNamespaceTest < ActiveSupport::TestCase
  should validate_presence_of(:name)
  should validate_presence_of :vcpu_count
  should validate_presence_of :ram_count
  should validate_presence_of :storage
  should belong_to(:kubernetes_cluster)
  should belong_to(:project)
  should validate_numericality_of(:vcpu_count).is_greater_than(0)
  should validate_numericality_of(:ram_count).is_greater_than(0)
  should validate_numericality_of(:storage).is_greater_than(0)

  it 'sets the prefix of the namespace' do
    namespace = KubernetesNamespace.new(name: 'test', project: projects(:one))
    namespace.valid?

    assert_equal 'mystring-test', namespace.name
  end

  it 'validates the name of the namespace' do
    namespace = KubernetesNamespace.new(name: 'Test', project: projects(:one))
    namespace.valid?

    assert_includes namespace.errors[:name], 'is invalid'
  end

  it 'validates repos_to_send_key not being empty if the cluster is a production one' do
    cluster = kubernetes_clusters(:two)
    namespace = KubernetesNamespace.new(name: 'prod', project: projects(:one), kubernetes_cluster: cluster)
    namespace.valid?

    assert_includes namespace.errors[:repos_to_send_key], 'can\'t be blank'
  end

  it 'validates repos_to_send_key against redmine repositories' do
    response = %w[repo1 repo2]
    stub_request(:get, "#{Settings.redmine_api.uri}/projects/#{projects(:one).identifier}/gitlab_repositories.json")
      .to_return(body: response.to_json, headers: { 'Content-Type' => 'application/json' })
    namespace = KubernetesNamespace.new(
      name: 'prod', project: projects(:one), kubernetes_cluster: kubernetes_clusters(:one),
      repos_to_send_key: %w[repo1 repo3]
    )

    assert_not namespace.valid?

    assert_includes namespace.errors[:repos_to_send_key], 'Has invalid values'
  end

  it 'activates dev namespace upon create' do
    user = users(:wiktoria)
    namespace = KubernetesNamespace.new(
      name: 'test', project: projects(:one), kubernetes_cluster: kubernetes_clusters(:one),
      vcpu_count: 1, ram_count: 1, storage: 1, requester: user, requested_date: Date.current
    )

    namespace.save!

    assert namespace.active?
    assert_equal user, namespace.activated_by
  end
end
