require 'test_helper'

class TrainingRequestTest < ActiveSupport::TestCase
  should belong_to(:user)

  should validate_presence_of(:state)
  should validate_presence_of(:kind)
  should validate_presence_of(:provider)
  should validate_presence_of(:starts_on)
  should validate_presence_of(:ends_on)
  should validate_presence_of(:place)
  should validate_presence_of(:mode)
  should validate_presence_of(:description)
  should validate_numericality_of(:tickets_price).is_greater_than_or_equal_to(0)
  should validate_numericality_of(:transportation_price).is_greater_than_or_equal_to(0)
  should validate_numericality_of(:accommodation_price).is_greater_than_or_equal_to(0)
  should validate_length_of(:provider).is_at_most(255)

  test 'validates budget exceeded' do
    user = users(:wiktoria)
    message = 'budget for this year has been exceeded by 250.00 PLN'

    training_request = user.training_requests.create(
      provider: 'Udemy', starts_on: Date.current, ends_on: Date.current + 1.week, tickets_price: 100,
      transportation_price: 100, accommodation_price: 100, description: 'Ruby on Rails course'
    )

    training_request.accept

    assert training_request.invalid?

    assert_equal message, training_request.errors[:tickets_price].first
    assert_equal message, training_request.errors[:transportation_price].first
    assert_equal message, training_request.errors[:accommodation_price].first
  end

  test 'validates budget not exceeded for the separate department budget' do
    user = users(:milosz)
    training_request = user.training_requests.new(
      provider: 'Udemy', starts_on: Date.current, ends_on: Date.current + 1.week, tickets_price: 100,
      transportation_price: 100, accommodation_price: 100, description: 'Ruby on Rails course',
      kind: 'course', place: 'online', mode: 'remote'
    )

    assert training_request.valid?
  end
end
