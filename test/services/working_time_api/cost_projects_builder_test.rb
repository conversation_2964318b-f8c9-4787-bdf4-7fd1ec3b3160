require 'test_helper'
require_relative '../redmine_working_time_api/support/builder_test_helper'

module WorkingTimeApi
  class CostProjectsBuilderTest < ActiveSupport::TestCase
    include BuilderTestHelper

    # required work hours for fixture cost_invoice (2/2020) => 20 * 8 = 160
    let(:cost_invoice) { cost_invoices(:wiktoria_cost_invoice) }
    let(:user) { users(:wiktoria) }
    let(:project) { projects(:one) }
    let(:two_project) { projects(:two) }
    let(:mkalita_project) { projects(:mkalita_project_alternative) }
    let(:workspace_id) { companies(:one).click_up_workspace_id }

    subject do
      CostProjectsBuilder.generate(cost_invoice.total_amount, user,
                                   cost_invoice.sell_date)
    end

    describe 'when total reported time exceeds time required in invoicing month' do
      describe 'when all time is reported to a single project' do
        before do
          project.update!(click_up_id: *********)
          stub_api_request([{
            id: 1, project: { identifier: project.identifier },
            user: { login: user.username }, hours: 170
          }])

          stub_click_up_api_request(workspace_id, user, cost_invoice, {
            data: [
              { id: 1, user: { id: user.click_up_id, email: user.email }, duration: 20000000, task_location: { folder_id: project.click_up_id } },
              { id: 2, user: { id: user.click_up_id, email: user.email }, duration: 8800000, task_location: { folder_id: project.click_up_id } }
            ]
          })
        end

        it 'returns a single cost_project object' do
          expect(subject.count).must_equal(1)
        end

        it 'assigns full total_amount to the cost_project' do
          expect(subject.first[:amount]).must_equal(cost_invoice.total_amount)
        end
      end

      describe 'when time is reported to multiple projects' do
        before do
          project.update!(click_up_id: *********)
          stub_api_request([
            {
              id: 1, project: { identifier: project.identifier },
              user: { login: user.username }, hours: 70
            },
            {
              id: 2, project: { identifier: mkalita_project.identifier },
              user: { login: user.username }, hours: 120
            },
          ])

          stub_click_up_api_request(workspace_id, user, cost_invoice, {
            data: [
              { id: 1, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: project.click_up_id } },
              { id: 2, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: project.click_up_id } }
            ]
          })
        end

        it 'returns multiple cost_project objects' do
          expect(subject.count).must_equal(2)
        end

        it 'splits total_amount with correct proportions' do
          expect(subject.first[:amount]).must_equal(40)
          expect(subject.last[:amount]).must_equal(60)
        end
      end

      describe 'when all time is reported to a single project in clickup' do
        before do
          project.update!(click_up_id: *********)
          user.update!(redmine_id: nil)

          stub_click_up_api_request(workspace_id, user, cost_invoice, {
            data: [
              { id: 1, user: { id: user.click_up_id, email: user.email }, duration: 612000000, task_location: { folder_id: project.click_up_id } }
            ]
          })
        end

        it 'returns a single cost_project object' do
          expect(subject.count).must_equal(1)
        end

        it 'assigns full total_amount to the cost_project' do
          expect(subject.first[:amount]).must_equal(cost_invoice.total_amount)
        end
      end

      describe 'when all time is reported to a single project in redmine' do
        before do
          user.update!(click_up_id: nil)
          stub_api_request([{
                              id: 1, project: { identifier: project.identifier },
                              user: { login: user.username }, hours: 170
                            }])
        end

        it 'returns a single cost_project object' do
          expect(subject.count).must_equal(1)
        end

        it 'assigns full total_amount to the cost_project' do
          expect(subject.first[:amount]).must_equal(cost_invoice.total_amount)
        end
      end
    end

    describe 'when total reported time equals time required in invoicing month' do
      describe 'when all time is reported to a single project' do
        before do
          project.update!(click_up_id: *********)
          stub_api_request([{
            id: 1, project: { identifier: project.identifier },
            user: { login: user.username }, hours: 150
          }])

          stub_click_up_api_request(workspace_id, user, cost_invoice, {
            data: [
              { id: 1, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: project.click_up_id } },
              { id: 2, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: project.click_up_id } }
            ]
          })
        end

        it 'returns a single cost_project object' do
          expect(subject.count).must_equal(1)
        end

        it 'assigns full total_amount to the cost_project' do
          expect(subject.first[:amount]).must_equal(cost_invoice.total_amount)
        end
      end

      describe 'when time is reported to multiple projects' do
        before do
          project.update!(click_up_id: *********)
          mkalita_project.update!(click_up_id: ***********)
          stub_api_request([
            {
              id: 1, project: { identifier: project.identifier },
              user: { login: user.username }, hours: 55
            },
            {
              id: 2, project: { identifier: mkalita_project.identifier },
              user: { login: user.username }, hours: 95
            },
          ])

          stub_click_up_api_request(workspace_id, user, cost_invoice, {
            data: [
              { id: 1, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: project.click_up_id } },
              { id: 2, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: mkalita_project.click_up_id } }
            ]
          })
        end

        it 'returns multiple cost_project objects' do
          expect(subject.count).must_equal(2)
        end

        it 'splits total_amount with correct proportions' do
          expect(subject.first[:amount]).must_equal(37.5)
          expect(subject.last[:amount]).must_equal(62.5)
        end
      end

      describe 'when all time is reported to a single project in clickup' do
        before do
          project.update!(click_up_id: *********)
          user.update!(redmine_id: nil)

          stub_click_up_api_request(workspace_id, user, cost_invoice, {
            data: [
              { id: 1, user: { id: user.click_up_id, email: user.email }, duration: 576000000, task_location: { folder_id: project.click_up_id } }
            ]
          })
        end

        it 'returns a single cost_project object' do
          expect(subject.count).must_equal(1)
        end

        it 'assigns full total_amount to the cost_project' do
          expect(subject.first[:amount]).must_equal(cost_invoice.total_amount)
        end
      end

      describe 'when all time is reported to a single project in redmine' do
        before do
          user.update!(click_up_id: nil)
          stub_api_request([{
                              id: 1, project: { identifier: project.identifier },
                              user: { login: user.username }, hours: 160
                            }])
        end

        it 'returns a single cost_project object' do
          expect(subject.count).must_equal(1)
        end

        it 'assigns full total_amount to the cost_project' do
          expect(subject.first[:amount]).must_equal(cost_invoice.total_amount)
        end
      end
    end

    describe 'when time is reported to multiple projects but in the same accounting number' do
      before do
        project.update!(click_up_id: *********)
        two_project.update!(click_up_id: ***********)
        stub_api_request([
                           {
                             id: 1, project: { identifier: project.identifier },
                             user: { login: user.username }, hours: 55
                           },
                           {
                             id: 2, project: { identifier: two_project.identifier },
                             user: { login: user.username }, hours: 95
                           }
                         ])

        stub_click_up_api_request(workspace_id, user, cost_invoice, {
          data: [
            { id: 1, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: project.click_up_id } },
            { id: 2, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: two_project.click_up_id } }
          ]
        })
      end

      it 'merges objects of the same accounting number to one' do
        expect(subject.count).must_equal(1)
      end

      it 'merges total_amount' do
        expect(subject.first[:amount]).must_equal(100)
      end
    end

    describe 'when total reported time is less than time required in invoicing month' do
      describe 'when all time is reported to a single project' do
        before do
          project.update!(click_up_id: *********)
          stub_api_request([{
            id: 1, project: { identifier: project.identifier },
            user: { login: user.username }, hours: 90
          }])

          stub_click_up_api_request(workspace_id, user, cost_invoice, {
            data: [
              { id: 1, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: project.click_up_id } },
              { id: 2, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: project.click_up_id } },
              { id: 2, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: nil } }
            ]
          })
        end

        it 'returns multiple cost_project objects and includes placeholder project' do
          expect(subject.count).must_equal(2)
          expect(subject.first[:accounting_number_id]).must_equal(accounting_numbers(:one).id)
          expect(subject.second[:accounting_number_id]).must_equal(
            accounting_numbers(:artegence_wew_number).id
          )
        end

        it 'assigns missing amount to the placeholder project' do
          expect(subject.first[:amount]).must_equal(62.5)
          expect(subject.second[:amount]).must_equal(37.5)
        end

        context 'when part_time user' do
          before { user.part_time = true }

          it 'returns a single cost_project object' do
            expect(subject.count).must_equal(1)
          end

          it 'assigns full total_amount to the cost_project' do
            expect(subject.first[:amount]).must_equal(cost_invoice.total_amount)
          end
        end
      end

      describe 'when time is reported to multiple projects' do
        before do
          project.update!(click_up_id: *********)
          mkalita_project.update!(click_up_id: ***********)
          stub_api_request([
            {
              id: 1, project: { identifier: project.identifier },
              user: { login: user.username }, hours: 45
            },
            {
              id: 2, project: { identifier: mkalita_project.identifier },
              user: { login: user.username }, hours: 45
            },
          ])

          stub_click_up_api_request(workspace_id, user, cost_invoice, {
            data: [
              { id: 1, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: project.click_up_id } },
              { id: 2, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: mkalita_project.click_up_id } },
              { id: 2, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: nil } }
            ]
          })
        end

        it 'returns multiple cost_project objects and includes placeholder project' do
          expect(subject.count).must_equal(3)
          expect(subject.first[:accounting_number_id]).must_equal(accounting_numbers(:one).id)
          expect(subject.second[:accounting_number_id]).must_equal(accounting_numbers(:two).id)
          expect(subject.last[:accounting_number_id]).must_equal(
            accounting_numbers(:artegence_wew_number).id
          )
        end

        it 'handles rounding to make items sum equal to total invoice quota' do
          sum = subject.sum { |i| i[:amount] }
          expect(sum).must_equal(cost_invoice.total_amount)
        end

        it 'assigns missing amount to the placeholder project' do
          expect(subject.first[:amount]).must_equal(31.25)
          expect(subject.second[:amount]).must_equal(31.25)
          expect(subject.last[:amount]).must_equal(37.5)
        end

        context 'when part_time user' do
          before { user.part_time = true }

          it 'returns multiple cost_project objects' do
            expect(subject.count).must_equal(2)
          end

          it 'splits total_amount with correct proportions' do
            expect(subject.first[:amount]).must_equal(50)
            expect(subject.last[:amount]).must_equal(50)
          end
        end
      end

      describe 'when time is reported to multiple projects in clickup' do
        before do
          project.update!(click_up_id: *********)
          mkalita_project.update!(click_up_id: ***********)
          user.update!(redmine_id: nil)

          stub_click_up_api_request(workspace_id, user, cost_invoice, {
            data: [
              { id: 1, user: { id: user.click_up_id, email: user.email }, duration: ********0, task_location: { folder_id: project.click_up_id } },
              { id: 2, user: { id: user.click_up_id, email: user.email }, duration: ********0, task_location: { folder_id: mkalita_project.click_up_id } },
              { id: 2, user: { id: user.click_up_id, email: user.email }, duration: ********, task_location: { folder_id: nil } }
            ]
          })
        end

        it 'returns multiple cost_project objects and includes placeholder project' do
          expect(subject.count).must_equal(3)
          expect(subject.first[:accounting_number_id]).must_equal(accounting_numbers(:one).id)
          expect(subject.second[:accounting_number_id]).must_equal(accounting_numbers(:two).id)
          expect(subject.last[:accounting_number_id]).must_equal(
            accounting_numbers(:artegence_wew_number).id
          )
        end

        it 'handles rounding to make items sum equal to total invoice quota' do
          sum = subject.sum { |i| i[:amount] }
          expect(sum).must_equal(cost_invoice.total_amount)
        end

        it 'assigns missing amount to the placeholder project' do
          expect(subject.first[:amount]).must_equal(31.25)
          expect(subject.second[:amount]).must_equal(31.25)
          expect(subject.last[:amount]).must_equal(37.5)
        end

        context 'when part_time user' do
          before { user.part_time = true }

          it 'returns multiple cost_project objects' do
            expect(subject.count).must_equal(2)
          end

          it 'splits total_amount with correct proportions' do
            expect(subject.first[:amount]).must_equal(50)
            expect(subject.last[:amount]).must_equal(50)
          end
        end
      end

      describe 'when time is reported to multiple projects in redmine' do
        before do
          user.update!(click_up_id: nil)

          stub_api_request([
                             {
                               id: 1, project: { identifier: project.identifier },
                               user: { login: user.username }, hours: 50
                             },
                             {
                               id: 2, project: { identifier: mkalita_project.identifier },
                               user: { login: user.username }, hours: 50
                             },
                           ])
        end

        it 'returns multiple cost_project objects and includes placeholder project' do
          expect(subject.count).must_equal(3)
          expect(subject.first[:accounting_number_id]).must_equal(accounting_numbers(:one).id)
          expect(subject.second[:accounting_number_id]).must_equal(accounting_numbers(:two).id)
          expect(subject.last[:accounting_number_id]).must_equal(
            accounting_numbers(:artegence_wew_number).id
          )
        end

        it 'handles rounding to make items sum equal to total invoice quota' do
          sum = subject.sum { |i| i[:amount] }
          expect(sum).must_equal(cost_invoice.total_amount)
        end

        it 'assigns missing amount to the placeholder project' do
          expect(subject.first[:amount]).must_equal(31.25)
          expect(subject.second[:amount]).must_equal(31.25)
          expect(subject.last[:amount]).must_equal(37.5)
        end

        context 'when part_time user' do
          before { user.part_time = true }

          it 'returns multiple cost_project objects' do
            expect(subject.count).must_equal(2)
          end

          it 'splits total_amount with correct proportions' do
            expect(subject.first[:amount]).must_equal(50)
            expect(subject.last[:amount]).must_equal(50)
          end
        end
      end
    end

    describe 'when user belongs to company with no placeholder project' do
      let(:user) { users(:mkalita_user) }

      before do
        cost_invoice.contractor.stubs(:user).returns(user)

        stub_api_request([
          {
            id: 1, project: { identifier: projects(:mkalita_project).identifier },
            user: { login: user.username }, hours: 50
          }
        ])
      end

      it 'raises an error' do
        expect { subject }.must_raise WorkingTimeApi::CostProjectsBuilder::UnprocessableRecordError
      end
    end
  end
end
