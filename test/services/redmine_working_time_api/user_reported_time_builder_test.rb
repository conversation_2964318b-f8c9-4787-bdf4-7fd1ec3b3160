require 'test_helper'

module RedmineWorkingTimeApi
  class UserReportedTimeBuilderTest < ActiveSupport::TestCase
    let(:user) { users(:milosz) }
    let(:project) { projects(:two) }
    let(:time_entry_1) { { id: 1, project: { id: project.redmine_id, identifier: project.identifier }, user: { login: user.username }, activity: {id: 176, name: '126 Client Service'}, hours: 100 } }
    let(:time_entry_2) { { id: 2, project: { id: project.redmine_id, identifier: project.identifier }, user: { login: user.username }, activity: {id: 176, name: '126 Client Service'}, hours: 60 } }
    let(:time_entry_3) { { id: 3, project: { id: project.redmine_id, identifier: project.identifier }, user: { login: user.username }, activity: {id: 16, name: 'Overtime'}, hours: 60 } }

    it 'generates summary successfully' do
      response = { time_entries: [time_entry_1, time_entry_2], total_count: 2 }

      stub_request(:get, "#{Settings.redmine_working_time.base_url}/timeapi.json")
        .with(query: hash_including('limit' => '1000'))
        .to_return(body: response.to_json, headers: { 'Content-Type' => 'application/json' })

      assert_nothing_raised do
        summary = RedmineWorkingTimeApi::UserReportedTimeBuilder.generate_summary(user)

        assert_equal 160, summary.reported
      end
    end

    it 'generates summary successfully - overtime not included to summary' do
      response = { time_entries: [time_entry_1, time_entry_2, time_entry_3], total_count: 2 }

      stub_request(:get, "#{Settings.redmine_working_time.base_url}/timeapi.json")
        .with(query: hash_including('limit' => '1000'))
        .to_return(body: response.to_json, headers: { 'Content-Type' => 'application/json' })

      assert_nothing_raised do
        summary = RedmineWorkingTimeApi::UserReportedTimeBuilder.generate_summary(user)

        assert_equal 160, summary.reported
      end
    end
  end
end
