module BuilderTestHelper
  def stub_api_request(entries, total_count: nil)
    stub_request(:get, "#{Settings.redmine_working_time.base_url}/timeapi.json")
      .with(query: hash_including('limit' => '1000'))
      .to_return(
        body: { time_entries: entries, total_count: total_count || entries.count }.to_json,
        headers: { 'Content-Type' => 'application/json' }
      )
  end

  def stub_click_up_api_request(team_id, user, cost_invoice, response) # rubocop:disable Metrics/AbcSize
    stub_request(:get, "#{Settings.click_up_api.uri}/team/#{team_id}/time_entries")
      .with(
        query: {
          assignee: user.click_up_id,
          start_date: (cost_invoice.sell_date.beginning_of_month.to_datetime.to_i * 1000) - 100,
          end_date: (cost_invoice.sell_date.end_of_month.to_datetime.end_of_day.to_i * 1000) + 100,
          include_location_names: true
        }
      )
      .to_return(body: response.to_json, headers: { 'Content-Type' => 'application/json' })
  end

  def workbook_to_hash(workbook)
    worksheet = workbook.worksheet(0)
    keys = worksheet.first

    worksheet.each_with_index.reduce([]) do |results, (row, row_idx)|
      next results if row_idx.zero?

      hash = row.each_with_index.each_with_object({}) do |(entry, col_idx), acc|
        key = keys[col_idx]
        acc[key] = entry
      end

      results << hash
    end
  end
end
