require 'test_helper'

class DictionariesGeneratorTest < ActiveSupport::TestCase
  let(:company) { companies(:one) }

  test 'generates dictionary for company workers' do
    user = users(:milosz)

    xml = DictionariesGenerator.generate(company)

    assert xml.match(user.first_name)
  end

  test 'generates departments dictionary' do
    mpk_number = mpk_numbers(:ux)

    xml = DictionariesGenerator.generate(company)

    assert xml.match(mpk_number.name)
  end

  test 'generates projects dictionary' do
    accounting_number = accounting_numbers(:one)

    xml = DictionariesGenerator.generate(company)

    assert xml.match(accounting_number.description)
  end

  test 'generates customers dictionary' do
    client = clients(:polexit)

    xml = DictionariesGenerator.generate(company)

    assert xml.match(client.name)
  end
end
