require 'test_helper'

module ResourceTimeEntries
  class SyncProjectTest < ActiveSupport::TestCase
    subject { ResourceTimeEntries::SyncProject.new(project) }
    let(:project) { projects(:two) }
    let(:spent_time_entry) { resource_time_entries(:project_two_spent_time_entry) }
    let(:scheduled_time_entry) { resource_time_entries(:project_two_scheduled_time_entry) }
    let(:user) { users(:wik<PERSON>) }
    let(:time_entries) do
      [
        {
          id: 1001,
          project: { id: project.redmine_id, name: project.name, identifier: project.identifier },
          user: { id: user.redmine_id, name: user.full_name, login: user.username },
          activity: { id: 11, name: 'Development' },
          hours: 8.0,
          comments: '.',
          spent_on: '2023-09-01',
          created_on: '2023-09-01T12:12:46+02:00',
          updated_on: '2023-09-01T12:12:46+02:00'
        }
      ]
    end
    let(:time_entries_response) do
      {
        time_entries: time_entries,
        total_count: 1,
        offset: 0,
        limit: 1000
      }
    end
    let(:resource_bookings) do
      [
        {
          id: 2001,
          assigned_to_id: user.redmine_id,
          project_id: project.redmine_id,
          start_date: '2023-09-01T00:00:00+02:00',
          end_date: '2023-09-05T00:00:00+02:00',
          hours_per_day: 4.0,
          notes: '.',
          created_at: '2023-09-01T12:12:46+02:00',
          updated_at: '2023-09-01T12:12:46+02:00'
        }
      ]
    end
    let(:resource_bookings_response) do
      {
        resources: resource_bookings,
        total_count: 1
      }
    end

    setup do
      stub_request(:get, "#{Settings.redmine_working_time.base_url}/timeapi.json")
        .with(query: hash_including('limit' => '1000'))
        .to_return(body: time_entries_response.to_json, headers: { 'Content-Type' => 'application/json' })
      stub_request(:get, "#{Settings.redmine_api.uri}/resource_bookings.json")
        .with(query: hash_including('limit' => '100'))
        .to_return(body: resource_bookings_response.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it 'deletes existing entries' do
      subject.call

      assert_raises(ActiveRecord::RecordNotFound) { spent_time_entry.reload }
      assert_raises(ActiveRecord::RecordNotFound) { scheduled_time_entry.reload }
    end

    it 'imports current spent time entries' do
      spent_time_entry.destroy

      assert_difference('project.spent_time_entries.count', 1) do
        subject.call
      end

      new_entry = project.spent_time_entries.last
      assert_equal user, new_entry.user
      assert_nil new_entry.issue
      assert_equal Date.new(2023, 9, 1), new_entry.date
      assert_equal 8, new_entry.hours
      assert_equal 'Development', new_entry.activity
    end

    it 'imports current scheduled time entries' do
      scheduled_time_entry.destroy

      assert_difference('project.scheduled_time_entries.count', 1) do
        subject.call
      end

      new_entry = project.scheduled_time_entries.last
      assert_equal user, new_entry.user
      assert_nil new_entry.issue
      assert_equal Date.new(2023, 9, 1), new_entry.date_from
      assert_equal Date.new(2023, 9, 5), new_entry.date_to
      assert_equal 4, new_entry.hours
    end
  end
end
