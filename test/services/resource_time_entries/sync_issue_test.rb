require 'test_helper'

module ResourceTimeEntries
  class SyncIssueTest < ActiveSupport::TestCase
    subject { ResourceTimeEntries::SyncIssue.new(issue) }
    let(:issue) { issues(:one) }
    let(:three_issue) { issues(:three) }
    let(:spent_time_entry) { resource_time_entries(:milosz_spent_time_entry) }
    let(:scheduled_time_entry) { resource_time_entries(:milosz_scheduled_time_entry) }
    let(:user) { users(:wik<PERSON>) }
    let(:mikolaj) { users(:mikolaj) }
    let(:time_entries) do
      [
        {
          id: 1001,
          project: { id: issue.project.redmine_id, name: issue.project.name, identifier: issue.project.identifier },
          issue: { id: issue.redmine_id },
          user: { id: user.redmine_id, name: user.full_name, login: user.username },
          activity: { id: 11, name: 'Development' },
          hours: 8.0,
          comments: '.',
          spent_on: '2023-09-01',
          created_on: '2023-09-01T12:12:46+02:00',
          updated_on: '2023-09-01T12:12:46+02:00'
        }
      ]
    end
    let(:time_entries_response) do
      {
        time_entries: time_entries,
        total_count: 1,
        offset: 0,
        limit: 1000
      }
    end
    let(:resource_bookings) do
      [
        {
          id: 2001,
          assigned_to_id: user.redmine_id,
          project_id: issue.project.redmine_id,
          issue_id: issue.redmine_id,
          start_date: '2023-09-01T00:00:00+02:00',
          end_date: '2023-09-05T00:00:00+02:00',
          hours_per_day: 4.0,
          notes: '.',
          created_at: '2023-09-01T12:12:46+02:00',
          updated_at: '2023-09-01T12:12:46+02:00'
        }
      ]
    end
    let(:resource_bookings_response) do
      {
        resources: resource_bookings,
        total_count: 1
      }
    end
    let(:click_up_time_entries) do
      [
        {
          'id' => 'time_entry_1',
          'task' => { 'id' => three_issue.click_up_id, 'name' => 'Test Task' },
          'user' => { 'id' => mikolaj.click_up_id, 'username' => 'testuser' },
          'intervals' => [
            {
              'id' => 1234321343215342,
              'start' => 1693526400000,
              'time' => 28800000,
              'end' => 1693555200000
            }
          ]
        }
      ]
    end

    setup do
      stub_request(:get, "#{Settings.redmine_working_time.base_url}/timeapi.json")
        .with(query: hash_including('limit' => '1000'))
        .to_return(body: time_entries_response.to_json, headers: { 'Content-Type' => 'application/json' })
      stub_request(:get, "#{Settings.redmine_api.uri}/resource_bookings.json")
        .with(query: hash_including('limit' => '100'))
        .to_return(body: resource_bookings_response.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it 'deletes existing entries' do
      subject.call

      assert_raises(ActiveRecord::RecordNotFound) { spent_time_entry.reload }
      assert_raises(ActiveRecord::RecordNotFound) { scheduled_time_entry.reload }
    end

    it 'returns true on successful completion' do
      assert_equal true, subject.call
    end

    it 'returns true even when there are no entries to import' do
      stub_request(:get, "#{Settings.redmine_working_time.base_url}/timeapi.json")
        .with(query: hash_including('limit' => '1000'))
        .to_return(body: { time_entries: [], total_count: 0 }.to_json, headers: { 'Content-Type': 'application/json' })

      stub_request(:get, "#{Settings.redmine_api.uri}/resource_bookings.json")
        .with(query: hash_including('limit' => '100'))
        .to_return(body: { resources: [], total_count: 0 }.to_json, headers: { 'Content-Type': 'application/json' })

      assert subject.call
    end

    it 'imports current spent time entries' do
      spent_time_entry.destroy

      assert_difference('issue.spent_time_entries.count', 1) do
        subject.call
      end

      new_entry = issue.spent_time_entries.last
      assert_equal issue.project, new_entry.project
      assert_equal user, new_entry.user
      assert_equal Date.new(2023, 9, 1), new_entry.date
      assert_equal 8, new_entry.hours
      assert_equal 'Development', new_entry.activity
    end

    it 'imports current scheduled time entries' do
      scheduled_time_entry.destroy

      assert_difference('issue.scheduled_time_entries.count', 1) do
        subject.call
      end

      new_entry = issue.scheduled_time_entries.last
      assert_equal issue.project, new_entry.project
      assert_equal user, new_entry.user
      assert_equal Date.new(2023, 9, 1), new_entry.date_from
      assert_equal Date.new(2023, 9, 5), new_entry.date_to
      assert_equal 4, new_entry.hours
    end

    it 'handles empty time entries response' do
      spent_time_entry.destroy
      empty_response = {
        time_entries: [],
        total_count: 0,
        offset: 0,
        limit: 1000
      }

      stub_request(:get, "#{Settings.redmine_working_time.base_url}/timeapi.json")
        .with(query: hash_including('limit' => '1000'))
        .to_return(body: empty_response.to_json, headers: { 'Content-Type': 'application/json' })

      assert_no_difference('issue.spent_time_entries.count') do
        subject.call
      end
    end

    it 'handles empty resource bookings response' do
      scheduled_time_entry.destroy
      empty_response = {
        resources: [],
        total_count: 0
      }

      stub_request(:get, "#{Settings.redmine_api.uri}/resource_bookings.json")
        .with(query: hash_including('limit' => '100'))
        .to_return(body: empty_response.to_json, headers: { 'Content-Type': 'application/json' })

      assert_no_difference('issue.scheduled_time_entries.count') do
        subject.call
      end
    end

    it 'skips entries for users not found by redmine_id' do
      spent_time_entry.destroy
      modified_time_entries = [
        {
          id: 1001,
          project: { id: issue.project.redmine_id, name: issue.project.name, identifier: issue.project.identifier },
          issue: { id: issue.redmine_id },
          user: { id: 999999, name: 'Unknown User', login: 'unknown' },
          activity: { id: 11, name: 'Development' },
          hours: 8.0,
          comments: '.',
          spent_on: '2023-09-01',
          created_on: '2023-09-01T12:12:46+02:00',
          updated_on: '2023-09-01T12:12:46+02:00'
        }
      ]

      modified_response = {
        time_entries: modified_time_entries,
        total_count: 1,
        offset: 0,
        limit: 1000
      }

      stub_request(:get, "#{Settings.redmine_working_time.base_url}/timeapi.json")
        .with(query: hash_including('limit' => '1000'))
        .to_return(body: modified_response.to_json, headers: { 'Content-Type': 'application/json' })

      assert_no_difference('issue.spent_time_entries.count') do
        subject.call
      end
    end

    describe 'with click_up synchronization' do
      before do
        issue.project.company.update_column(:click_up_synchronization, true)
        issue.project.company.update_column(:redmine_synchronization, false)
        issue.update_column(:click_up_id, 'click_up_task_123')

        mpk_number = MpkNumber.find_or_create_by(key: '123', name: 'Test MPK')
        mikolaj.department.update_column(:mpk_number_id, mpk_number.id)

        ClickUpTimeEntriesImporter.any_instance.stubs(:import).returns(click_up_time_entries)
      end

      it 'imports current spent time entries from ClickUp' do
        spent_time_entry.destroy

        assert_difference('issue.spent_time_entries.count', 1) do
          subject.call
        end

        new_entry = issue.spent_time_entries.last
        assert_equal issue.project, new_entry.project
        assert_equal mikolaj, new_entry.user
        assert_equal Date.new(2023, 9, 1), new_entry.date
        assert_equal 8, new_entry.hours
        assert_equal '123 Test MPK', new_entry.activity
      end

      it 'does not import scheduled time entries when company has redmine_synchronization disabled' do
        scheduled_time_entry.destroy

        assert_no_difference('issue.scheduled_time_entries.count') do
          subject.call
        end
      end

      it 'skips entries for users not found by click_up_id' do
        spent_time_entry.destroy
        mikolaj.update_column(:click_up_id, 'different_click_up_id')

        assert_no_difference('issue.spent_time_entries.count') do
          subject.call
        end
      end

      it 'handles multiple intervals in a time entry' do
        spent_time_entry.destroy

        click_up_time_entries.first['intervals'] << {
          'start' => 1693612800000,
          'time' => 14400000,
          'end' => 1693627200000
        }

        assert_difference('issue.spent_time_entries.count', 2) do
          subject.call
        end

        entries = issue.spent_time_entries.order(:date_from)
        assert_equal Date.new(2023, 9, 1), entries.first.date
        assert_equal 8, entries.first.hours
        assert_equal Date.new(2023, 9, 2), entries.last.date
        assert_equal 4, entries.last.hours
      end

      it 'handles empty intervals array' do
        spent_time_entry.destroy
        click_up_time_entries.first['intervals'] = []

        assert_no_difference('issue.spent_time_entries.count') do
          subject.call
        end
      end
    end
  end
end
