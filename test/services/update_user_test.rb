require 'test_helper'

class UpdateUserTest < ActiveSupport::TestCase
  test 'updates username when user\'s company changes' do
    u = User.new(first_name: '<PERSON>', last_name: 'Locked', activates_on: Time.zone.today,
                 company_id: companies(:two).id)
    updated_user = UpdateUser.new.call(u, {first_name: u.first_name, last_name: u.last_name, company_id: companies(:one).id })
    assert_equal companies(:one).id, updated_user.company_id
    assert_equal '<EMAIL>', updated_user.email
    assert_equal %w(<EMAIL> <EMAIL>
                    <EMAIL> <EMAIL>
                    <EMAIL>).sort, updated_user.email_aliases.map(&:email).sort
  end

  test 'updates username when user\'s surname and company changes' do
    u = User.new(first_name: '<PERSON>', last_name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', activates_on: Time.zone.today,
                 company_id: companies(:two).id)
    updated_user = UpdateUser.new.call(u, {first_name: u.first_name, last_name: 'Locked', company_id: companies(:one).id })
    assert_equal companies(:one).id, updated_user.company_id
    assert_equal '<EMAIL>', updated_user.email
    assert_equal 'Locked', updated_user.last_name
    assert_equal %w(<EMAIL> <EMAIL>
                    <EMAIL> <EMAIL>
                    <EMAIL>).sort, updated_user.email_aliases.map(&:email).sort
  end

  test 'updates username when user\'s surname changes' do
    u = User.new(first_name: 'John', last_name: 'Locked-Defoe', activates_on: Time.zone.today,
                 company_id: companies(:two).id)
    updated_user = UpdateUser.new.call(u, {first_name: u.first_name, last_name: 'Locked', company_id: companies(:two).id })
    assert_equal companies(:two).id, updated_user.company_id
    assert_equal '<EMAIL>', updated_user.email
    assert_equal 'Locked', updated_user.last_name
    assert_equal %w(<EMAIL> <EMAIL>
                    <EMAIL> <EMAIL>
                    <EMAIL>).sort, updated_user.email_aliases.map(&:email).sort
  end

  test 'updates username when user\'s surname and company changes and user has email aliases' do
    u = CreateUser.new.call(first_name: 'John', last_name: 'Locked-Defoe', activates_on: Time.zone.today,
                 company_id: companies(:two).id)
    refute_empty u.email_aliases
    updated_user = UpdateUser.new.call(u, {first_name: u.first_name, last_name: 'Locked', company_id: companies(:one).id })
    assert_equal companies(:one).id, updated_user.company_id
    assert_equal '<EMAIL>', updated_user.email
    assert_equal 'Locked', updated_user.last_name
    assert_equal %w(<EMAIL> <EMAIL>
                    <EMAIL> <EMAIL>
                    <EMAIL>).sort, updated_user.email_aliases.map(&:email).sort
  end

  test 'updates username when user\'s surname and company changes and user has email aliases and user\'s new last_name has two parts' do
    u = CreateUser.new.call(first_name: 'John', last_name: 'Locked', activates_on: Time.zone.today,
                 company_id: companies(:two).id)
    refute_empty u.email_aliases
    updated_user = UpdateUser.new.call(u, {first_name: u.first_name, last_name: 'Locked-Defoe', company_id: companies(:one).id })
    assert_equal companies(:one).id, updated_user.company_id
    assert_equal '<EMAIL>', updated_user.email
    assert_equal 'Locked-Defoe', updated_user.last_name
    assert_equal %w(<EMAIL> <EMAIL>
                    <EMAIL> <EMAIL>
                    <EMAIL>).sort, updated_user.email_aliases.map(&:email).sort
  end
end
