require 'test_helper'

class ClickUpIssuesImporterTest < ActiveSupport::TestCase
  let(:importer) { ClickUpIssuesImporter.new }
  let(:company1) { companies(:one) }
  let(:company2) { companies(:two) }
  let(:tasks1) { [
    {
      'id' => 'task_id_1',
      'name' => 'Task 1',
      'status' => { 'status' => 'in progress' },
      'date_updated' => '1609459200000'
    },
    {
      'id' => 'task_id_2',
      'name' => 'Task 2',
      'status' => { 'status' => 'completed' },
      'date_updated' => '1609545600000'
    }
  ] }
  let(:tasks2) { [{ 'id' => 'task_id_3', 'name' => 'Task 3', 'status' => { 'status' => 'to do' }, 'date_updated' => '1609632000000' }] }
  let(:api_response1_page1) { { 'tasks' => tasks1, 'last_page' => false } }
  let(:api_response1_page2) { { 'tasks' => [], 'last_page' => true } }
  let(:api_response2) { { 'tasks' => tasks2, 'last_page' => true } }
  let(:custom_items) { [{ 'id' => 'custom_item_1', 'name' => 'Bug' }, { 'id' => 'custom_item_2', 'name' => 'Feature' }] }
  let(:custom_items_response) { { 'custom_items' => custom_items } }

  setup do
    company1.update_column(:click_up_workspace_id, 'workspace_id_1')
    company2.update_column(:click_up_workspace_id, 'workspace_id_2')
  end

  test 'import fetches tasks from all companies' do
    ClickUpApi.expects(:get)
              .with("/team/#{company1.click_up_workspace_id}/task", query: { limit: ClickUpImporter::DEFAULT_STEP, page: 0 })
              .returns(api_response1_page1)

    ClickUpApi.expects(:get)
              .with("/team/#{company1.click_up_workspace_id}/task", query: { limit: ClickUpImporter::DEFAULT_STEP, page: 1 })
              .returns(api_response1_page2)

    ClickUpApi.expects(:get)
              .with("/team/#{company2.click_up_workspace_id}/task", query: { limit: ClickUpImporter::DEFAULT_STEP, page: 0 })
              .returns(api_response2)

    import = importer.import
    result = import.sort_by { |n| n['id'] }

    assert_equal tasks1 + tasks2, result.sort_by { |n| n['id'] }
    assert_equal 3, result.size
    assert_equal 'task_id_1', result.first['id']
    assert_equal 'task_id_3', result.last['id']
  end

  test 'import with task ID' do
    task_id = 'specific_task_id'

    ClickUpApi.expects(:get)
              .with("/team/#{company1.click_up_workspace_id}/task", query: { limit: ClickUpImporter::DEFAULT_STEP, page: 0, id: task_id })
              .returns(api_response1_page1)

    ClickUpApi.expects(:get)
              .with("/team/#{company1.click_up_workspace_id}/task", query: { limit: ClickUpImporter::DEFAULT_STEP, page: 1, id: task_id })
              .returns(api_response1_page2)

    ClickUpApi.expects(:get)
              .with("/team/#{company2.click_up_workspace_id}/task", query: { limit: ClickUpImporter::DEFAULT_STEP, page: 0, id: task_id })
              .returns(api_response2)

    result = importer.import(task_id)

    assert_equal tasks1 + tasks2, result.sort_by { |n| n['id'] }
  end

  test 'updated_after adds date filter' do
    date = Date.new(2021, 1, 1)

    importer.updated_after(date)

    ClickUpApi.expects(:get)
              .with("/team/#{company1.click_up_workspace_id}/task",
                    query: {
                      limit: ClickUpImporter::DEFAULT_STEP,
                      page: 0,
                      date_updated_gt: date,
                      subtasks: true,
                      include_closed: true
                    })
              .returns(api_response1_page1)

    ClickUpApi.expects(:get)
              .with("/team/#{company1.click_up_workspace_id}/task",
                    query: {
                      limit: ClickUpImporter::DEFAULT_STEP,
                      page: 1,
                      date_updated_gt: date,
                      subtasks: true,
                      include_closed: true
                    })
              .returns(api_response1_page2)

    ClickUpApi.expects(:get)
              .with("/team/#{company2.click_up_workspace_id}/task",
                    query: {
                      limit: ClickUpImporter::DEFAULT_STEP,
                      page: 0,
                      date_updated_gt: date,
                      subtasks: true,
                      include_closed: true
                    })
              .returns(api_response2)

    result = importer.import

    assert_equal tasks1 + tasks2, result.sort_by { |n| n['id'] }
  end

  test 'task_types fetches custom items' do
    workspace_id = 'workspace_id_1'

    ClickUpApi.expects(:get)
              .with("/team/#{workspace_id}/custom_item")
              .returns(custom_items_response)

    result = importer.task_types(workspace_id)

    assert_equal custom_items, result.sort_by { |n| n['id'] }
    assert_equal 2, result.size
    assert_equal 'custom_item_1', result.first['id']
    assert_equal 'Bug', result.first['name']
  end

  test 'import_task_types fetches from all companies' do
    ClickUpApi.expects(:get)
              .with("/team/#{company1.click_up_workspace_id}/custom_item")
              .returns(custom_items_response)

    ClickUpApi.expects(:get)
              .with("/team/#{company2.click_up_workspace_id}/custom_item")
              .returns(custom_items_response)

    result = importer.import_task_types

    assert_equal custom_items, result.sort_by { |n| n['id'] }
    assert_equal 2, result.size
  end

  test 'limit sets custom limit' do
    custom_limit = 50
    importer.limit(custom_limit)

    ClickUpApi.expects(:get)
              .with("/team/#{company1.click_up_workspace_id}/task", query: { limit: custom_limit, page: 0 })
              .returns(api_response1_page1)

    ClickUpApi.expects(:get)
              .with("/team/#{company1.click_up_workspace_id}/task", query: { limit: custom_limit, page: 1 })
              .returns(api_response1_page2)

    ClickUpApi.expects(:get)
              .with("/team/#{company2.click_up_workspace_id}/task", query: { limit: custom_limit, page: 0 })
              .returns(api_response2)

    result = importer.import

    assert_equal tasks1 + tasks2, result.sort_by { |n| n['id'] }
  end

  test 'handles empty company list' do
    original_workspace_id1 = company1.click_up_workspace_id
    original_workspace_id2 = company2.click_up_workspace_id

    company1.update_column(:click_up_workspace_id, nil)
    company2.update_column(:click_up_workspace_id, '')

    begin
      result = importer.import

      assert_empty result
    ensure
      company1.update_column(:click_up_workspace_id, original_workspace_id1)
      company2.update_column(:click_up_workspace_id, original_workspace_id2)
    end
  end
end
