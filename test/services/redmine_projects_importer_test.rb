require 'test_helper'

class RedmineProjectsImporterTest < ActiveSupport::TestCase
  def setup
    @importer = RedmineProjectsImporter.new
  end

  test 'imperator_attrs_updated_after adds imperator_attrs_updated_date_from to options' do
    @importer.imperator_attrs_updated_after('2022-01-01')
    assert_equal '2022-01-01', @importer.instance_variable_get(:@options)[:imperator_attrs_updated_date_from]
  end

  test 'limit adds limit to options' do
    @importer.limit(10)
    assert_equal 10, @importer.instance_variable_get(:@options)[:limit]
  end

  test 'import returns all projects' do
    response = { 'total_count' => 2, 'projects' => [{ 'id' => 1, 'name' => 'Project 1' }, { 'id' => 2, 'name' => 'Project 2' }] }
    stub_request(:get, "#{Settings.redmine_api.uri}/projects.json")
      .with(query: hash_including('limit'))
      .to_return(body: response.to_json, headers: { 'Content-Type' => 'application/json' })

    projects = @importer.import

    assert_equal 2, projects.size
    assert_equal([1, 2], projects.map { |p| p['id'] })
  end

  test 'import fetches projects in batches if there are more projects than the limit' do
    response = {
      'total_count' => 5,
      'projects' => [{ 'id' => 1, 'name' => 'Project 1' }, { 'id' => 2, 'name' => 'Project 2' }] }
    stub_request(:get, "#{Settings.redmine_api.uri}/projects.json")
      .with(query: hash_including('limit'))
      .to_return(body: response.to_json, headers: { 'Content-Type' => 'application/json' })

    @importer.limit(2).import

    assert_requested :get, "#{Settings.redmine_api.uri}/projects.json?limit=2&offset=0", times: 1
    assert_requested :get, "#{Settings.redmine_api.uri}/projects.json?limit=2&offset=2", times: 1
    assert_requested :get, "#{Settings.redmine_api.uri}/projects.json?limit=2&offset=4", times: 1
  end
end
