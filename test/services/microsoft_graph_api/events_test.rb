require 'test_helper'

module MicrosoftGraphApi
  class EventsTest < ActiveSupport::TestCase
    subject { MicrosoftGraphApi::Events }
    let(:credentials) { Settings.microsoft_graph_api }
    let(:user) { users(:mkalita_user) }
    let(:access_token) { 'access_token' }

    setup do
      stub_request(:post, "https://login.microsoftonline.com:443/#{credentials.tenant_id}/oauth2/v2.0/token")
        .with(
          headers: {
            'Content-Type' => 'application/x-www-form-urlencoded'
          },
          body: {
            grant_type: 'client_credentials',
            scope: 'https://graph.microsoft.com/.default'
          }
        ).to_return(
          status: 200,
          headers: {
            'Content-Type' => 'application/json'
          },
          body: {
            token_type: 'Bearer',
            expires_in: 3599,
            access_token: access_token
          }.to_json
        )
    end

    describe 'create_user_event' do
      let(:holiday_request) { holiday_requests(:many_days) }
      let(:event_body) { MicrosoftGraphApi::EventBuilder.new(holiday_request).call }
      let(:request_body) do
        {
          id: nil,
          '@odata.type': '#microsoft.graph.event',
          changeKey: nil,
          createdDateTime: nil,
          lastModifiedDateTime: nil,
          allowNewTimeProposals: false,
          attendees: [],
          body: {
            content: nil,
            contentType: 'html',
            '@odata.type': '#microsoft.graph.itemBody'
          },
          bodyPreview: nil,
          end: {
            dateTime: '2017-01-23T15:00:00',
            '@odata.type': '#microsoft.graph.dateTimeTimeZone',
            timeZone: 'Europe/Warsaw'
          },
          hasAttachments: nil,
          hideAttendees: nil,
          iCalUId: nil,
          importance: 'normal',
          isAllDay: false,
          isCancelled: nil,
          isDraft: false,
          isOnlineMeeting: nil,
          isOrganizer: true,
          isReminderOn: nil,
          location: {
            displayName: nil,
            locationEmailAddress: nil,
            locationType: 'default',
            locationUri: nil,
            '@odata.type': '#microsoft.graph.location',
            uniqueId: nil,
            uniqueIdType: 'unknown'
          },
          onlineMeetingProvider: 'unknown',
          onlineMeetingUrl: nil,
          organizer: {
            emailAddress: {
              address: user.email,
              name: user.full_name,
              '@odata.type': '#microsoft.graph.emailAddress'
            },
            '@odata.type': '#microsoft.graph.recipient'
          },
          originalEndTimeZone: nil,
          originalStart: nil,
          originalStartTimeZone: nil,
          recurrence: {
            '@odata.type': '#microsoft.graph.patternedRecurrence',
            pattern: {
              dayOfMonth: 0,
              firstDayOfWeek: 'sunday',
              index: 'first',
              interval: 1,
              month: 0,
              '@odata.type': '#microsoft.graph.recurrencePattern',
              type: 'daily'
            },
            range: {
              endDate: '2017-01-25',
              numberOfOccurrences: 0,
              '@odata.type': '#microsoft.graph.recurrenceRange',
              recurrenceTimeZone: 'Europe/Warsaw',
              startDate: '2017-01-23',
              type: 'endDate'
            }
          },
          reminderMinutesBeforeStart: nil,
          responseRequested: false,
          responseStatus: {
            '@odata.type': '#microsoft.graph.responseStatus',
            response: 'organizer',
            time: nil
          },
          sensitivity: 'normal',
          seriesMasterId: nil,
          showAs: 'oof',
          start: {
            dateTime: '2017-01-23T09:00:00',
            '@odata.type': '#microsoft.graph.dateTimeTimeZone',
            timeZone: 'Europe/Warsaw'
          },
          subject: 'Niedostępność',
          transactionId: Base64.urlsafe_encode64("HolidayRequest #{holiday_request.id}", padding: false),
          type: 'seriesMaster',
          webLink: nil
        }
      end

      before do
        holiday_request.update_columns(hours: 6)
        stub_request(:post, "https://graph.microsoft.com/v1.0/users/#{user.uid}/events")
          .with(
            headers: {
              'Content-Type' => 'application/json',
              'Authorization' => "Bearer #{access_token}"
            },
            body: request_body.to_json
          ).to_return(
            status: 201,
            headers: {
              'Content-Type' => 'application/json'
            },
            body: {
              id: 'AAMkAGViNDU7zAAAAA7zAAAZe6CkAAA='
            }.to_json
          )
      end

      it 'creates user event' do
        result = subject.create_user_event(user, event_body)
        assert_kind_of MicrosoftGraph::Models::Event, result
      end
    end

    describe 'delete_user_event' do
      let(:event_id) { 'abcd1234' }

      before do
        stub_request(:delete, "https://graph.microsoft.com/v1.0/users/#{user.uid}/events/#{event_id}")
          .with(
            headers: {
              'Authorization' => "Bearer #{access_token}"
            }
          ).to_return(status: 204)
      end

      it 'deletes user event' do
        result = subject.delete_user_event(user, event_id)
        assert_nil result
      end
    end
  end
end
