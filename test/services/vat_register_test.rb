require 'test_helper'

class VATRegisterTest < ActiveSupport::TestCase
  let(:vat_number) { '5228357232' }
  let(:existing_response) do
    { 'result' =>
      { 'subject' =>
        { 'name' => 'IZABELA BUKOWSKA',
         'nip' => '5228357232',
         'statusVat' => 'Czynny',
         'regon' => '651653809',
         'pesel' => nil,
         'krs' => nil,
         'residenceAddress' => 'ROBOTNICZA 95, 96-199 JAWORZNO',
         'workingAddress' => nil,
         'representatives' => [],
         'authorizedClerks' => [],
         'partners' => [],
         'registrationLegalDate' => '2015-02-25',
         'registrationDenialBasis' => nil,
         'registrationDenialDate' => nil,
         'restorationBasis' => nil,
         'restorationDate' => nil,
         'removalBasis' => nil,
         'removalDate' => nil,
         'accountNumbers' => ['27864710176079948947994324'],
         'hasVirtualAccounts' => false },
       'requestId' => 'gV5GT-8if0ghk',
       'requestDateTime' => Time.zone.now } }.to_json
  end
  let(:non_existing_response) do
    {
      'result' =>
        { 'subject' => nil, 'requestId' => '5aKfd-8if0jld', 'requestDateTime' => Time.zone.now }
    }.to_json
  end
  let(:headers) do
    { 'server' => 'nginx',
      'date' => 'Thu, 23 Feb 2023 07:59:01 GMT',
      'content-type' => 'application/json;charset=UTF-8',
      'transfer-encoding' => 'chunked',
      'connection' => 'keep-alive',
      'access-control-allow-headers' => 'access-control-allow-origin',
      'access-control-allow-methods' => 'GET,OPTIONS',
      'cache-control' => 'max-age=0' }
  end

  test 'returns true for the vat number being a vat subject' do
    stub_request(:get, "#{VATRegister::ADDRESS}#{vat_number}")
      .with(query: { date: Time.zone.today })
      .to_return(body: existing_response, headers: headers)

    assert VATRegister.vat_subject?(vat_number)
  end

  test 'returns false for the vat number not being a vat subject' do
    stub_request(:get, "#{VATRegister::ADDRESS}#{vat_number}")
      .with(query: { date: Time.zone.today })
      .to_return(body: non_existing_response, headers: headers)

    assert_not VATRegister.vat_subject?(vat_number)
  end
end
