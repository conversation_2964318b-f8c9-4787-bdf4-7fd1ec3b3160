require 'test_helper'

class ApprovalsManagement::ForProjectAgreementTest < ActiveSupport::TestCase
  include UsersHelper

  def setup
    @project_agreement = project_agreements(:two)
  end

  def subject
    ApprovalsManagement::ForProjectAgreement.new(@project_agreement)
  end

  test 'approvals are created for proper users' do
    users = User.joins(:memberships)
                .where(memberships: { project_id: @project_agreement.project_id },
                       company: @project_agreement.company,
                       contract_of_employment: !@project_agreement.business_to_business)

    assert_difference('Approval.count', users.count) do
      subject.call
    end
  end
end
