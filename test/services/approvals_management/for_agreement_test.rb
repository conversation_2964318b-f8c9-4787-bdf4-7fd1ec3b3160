require 'test_helper'

class ApprovalsManagement::ForAgreementTest < ActiveSupport::TestCase
  include UsersHelper
  include ForAgreementHelper

  before do
    @users_b2b_count = 5
    @users_uop_count = 3
    @agreement = agreements(:main)
    @users_b2b = generate_users(@users_b2b_count)
    @users_b2b.map { |u| u.user_contracts.create!(agreement_type: :b2b, starts_on: Date.new(2020, 2, 1), month_notice_period: 1, company: companies(:one))}
    @users_uop = generate_users(@users_uop_count, contract_of_employment: true)
    @users_uop.map { |u| u.user_contracts.create!(agreement_type: :employment, starts_on: Date.new(2020, 2, 1), month_notice_period: 1, company: companies(:one))}
    User.any_instance.stubs(:update_approvals_cache).returns(nil)
  end

  def subject
    ApprovalsManagement::ForAgreement.new(@agreement.reload)
  end

  test 'approvals are created for proper users belonging to assigned company and department' do
    users = User.includes(:user_contracts)
                .where(company: @agreement.companies,
                       department: @agreement.departments,
                       user_contracts: { agreement_type: :b2b })

    assert_equal 0, @agreement.approvals.count
    assert_same_elements users, @users_b2b

    assert_difference('Approval.count', @users_b2b_count) do
      subject.call
      assigned_users = User.joins(:approvals).where(approvals: {approvable: @agreement})

      assert_equal @users_b2b_count, @agreement.approvals.count
      assert_same_elements users, assigned_users
    end
  end

  test 'approvals should be destroyed if department is unassigned from agreement' do
    subject.call
    assert_equal @users_b2b_count, @agreement.approvals.count

    @agreement.departments.delete(departments(:agreement_department))

    assert_difference('Approval.count', -@users_b2b_count) do
      subject.call
      assert_empty @agreement.approvals.reload
    end
  end

  test 'approvals should be added for newly added department' do
    subject.call
    assert_equal @users_b2b_count, @agreement.approvals.count

    department = departments(:mkalita_department)
    new_members_count = 12
    users = generate_users(new_members_count, department: department)
    users.map { |u| u.user_contracts.create!(agreement_type: :b2b, starts_on: Date.new(2020, 3, 1), month_notice_period: 1, company: companies(:one))}

    assert_difference('Approval.count', new_members_count) do
      @agreement.departments << department
      subject.call
    end

    assert_equal new_members_count + @users_b2b_count, @agreement.approvals.count
  end

  test 'approvals should be destroyed if company is unassigned from agreement' do
    subject.call
    assert_equal @users_b2b_count, @agreement.approvals.count

    @agreement.companies.delete(companies(:agreement_company))

    assert_difference('Approval.count', -@users_b2b_count) do
      subject.call
      assert_empty @agreement.approvals.reload
    end
  end

  test 'approvals should be added for newly added company' do
    subject.call
    assert_equal @users_b2b_count, @agreement.approvals.count

    company = companies(:one)
    new_members_count = 15
    new_members = generate_users(new_members_count, company: company)
    new_members.map { |u| u.user_contracts.create!(agreement_type: :b2b, starts_on: Date.new(2020, 3, 1), month_notice_period: 1, company: companies(:one))}
    company.users = new_members

    assert_difference('Approval.count', new_members_count) do
      @agreement.companies << company
      subject.call
    end

    assert_equal new_members_count + @users_b2b_count, @agreement.approvals.count
  end

  test 'if only contract of employment is assigned, approvals should not be created for users with b2b' do
    @agreement.business_to_business = false
    @agreement.contract_of_employment = true
    @agreement.save

    assert_difference('Approval.count', @users_uop_count) do
      subject.call
    end
  end

  test 'if both contract of employment and b2b contracts are chosen, both kinds of users should be assigned' do
    @agreement.business_to_business = true
    @agreement.contract_of_employment = true
    @agreement.save

    all_users_count = @users_uop_count + @users_b2b_count

    assert_difference('Approval.count', all_users_count) do
      subject.call
    end
  end

  test 'if no cotract type is chosen, no approvals should be assigned to users' do
    @agreement.business_to_business = false
    @agreement.contract_of_employment = false
    @agreement.save(validate: false)

    assert_no_difference('Approval.count') do
      subject.call
    end
  end

  test 'filters users by employment contract type' do
    setup_test_users_and_contracts(true, true, true, true)
    agreement = create_agreement(contract_of_employment: true)

    ApprovalsManagement::ForAgreement.new(agreement).call

    assert_user_included(agreement, [:milosz])
    assert_users_excluded(agreement, [:mikolaj, :wiktoria, :wilhelm])
  end

  test 'filters users by mandate contract type' do
    setup_test_users_and_contracts(true, true, true, true)
    agreement = create_agreement(mandate_contract: true)

    ApprovalsManagement::ForAgreement.new(agreement).call

    assert_user_included(agreement, [:mikolaj])
    assert_users_excluded(agreement, [:milosz, :wiktoria, :wilhelm])
  end

  test 'filters users by b2b contract type' do
    setup_test_users_and_contracts(true, true, true, true)
    agreement = create_agreement(business_to_business: true)

    ApprovalsManagement::ForAgreement.new(agreement).call

    assert_user_included(agreement, [:wiktoria])
    assert_users_excluded(agreement, [:milosz, :mikolaj, :wilhelm])
  end

  test 'filters users by contract work type' do
    setup_test_users_and_contracts(true, true, true, true)
    agreement = create_agreement(contract_work: true)

    ApprovalsManagement::ForAgreement.new(agreement).call

    assert_user_included(agreement, [:wilhelm])
    assert_users_excluded(agreement, [:milosz, :mikolaj, :wiktoria])
  end

  test 'filters users by employment and b2b contract type' do
    setup_test_users_and_contracts(true, true, true, true)
    agreement = create_agreement({ contract_of_employment: true, business_to_business: true })

    ApprovalsManagement::ForAgreement.new(agreement).call

    assert_user_included(agreement, [:milosz, :wiktoria])
    assert_users_excluded(agreement, [:mikolaj, :wilhelm])
  end

  test 'filters users by contract work and b2b contract type' do
    setup_test_users_and_contracts(true, true, true, true)
    agreement = create_agreement({ contract_work: true, business_to_business: true })

    ApprovalsManagement::ForAgreement.new(agreement).call

    assert_user_included(agreement, [:wiktoria, :wilhelm])
    assert_users_excluded(agreement, [:milosz, :mikolaj])
  end

  test 'filters users by mandate, contract work and b2b contract type' do
    setup_test_users_and_contracts(true, true, true, true)
    agreement = create_agreement({ contract_work: true, business_to_business: true, mandate_contract: true })

    ApprovalsManagement::ForAgreement.new(agreement).call

    assert_user_included(agreement, [:wiktoria, :wilhelm, :mikolaj])
    assert_users_excluded(agreement, [:milosz])
  end

  test 'filters users by mandate, contract work and b2b contract type and without b2b user' do
    setup_test_users_and_contracts(true, true, false, true)
    agreement = create_agreement({ contract_work: true, business_to_business: true, mandate_contract: true })

    ApprovalsManagement::ForAgreement.new(agreement).call

    assert_user_included(agreement, [:wilhelm, :mikolaj])
    assert_users_excluded(agreement, [:wiktoria, :milosz])
  end

  test 'filters users by employment, contract work and b2b contract type and without b2b and employment user' do
    setup_test_users_and_contracts(false, true, false, true)
    agreement = create_agreement({ contract_work: true, business_to_business: true, contract_of_employment: true })

    ApprovalsManagement::ForAgreement.new(agreement).call

    assert_user_included(agreement, [:wilhelm])
    assert_users_excluded(agreement, [:wiktoria, :milosz, :mikolaj])
  end

  test 'filters users by employment, contract work and b2b contract type and without b2b, contract work and employment user' do
    setup_test_users_and_contracts(false, true, false, false)
    agreement = create_agreement({ contract_work: true, business_to_business: true, contract_of_employment: true })

    ApprovalsManagement::ForAgreement.new(agreement).call

    assert_empty agreement.reload.users
  end
end

