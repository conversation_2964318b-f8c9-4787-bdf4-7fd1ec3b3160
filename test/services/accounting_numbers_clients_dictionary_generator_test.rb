require 'test_helper'

class AccountingNumbersClientsDictionaryGeneratorTest < ActiveSupport::TestCase
  test 'generates dictionary properly' do
    company = companies(:one)
    client = clients(:arte)
    accounting_number = accounting_numbers(:one)

    xml = AccountingNumbersClientsDictionaryGenerator.generate(company)

    hash = Hash.from_xml(xml)
    assert([hash['ROOT']].compact.detect do |node|
      node['KLIENT_PROJEKT']['ID_PROJEKTU'] == accounting_number.number.to_s &&
      node['KLIENT_PROJEKT']['ID_KLIENTA'] == client.vat_number
    end)
  end
end
