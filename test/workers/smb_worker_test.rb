require 'test_helper'

class SMBWorkerTest < ActiveSupport::TestCase
  test 'generates proper path if not exist and copies file' do
    share_name = 'estelligence_share'
    folder = "Artegence/#{1.day.ago.to_date}"
    file_name = "cost_invoices_#{1.day.ago.to_date}_artegence.xml"
    source_path = Settings.invoices_integration.files_path
    File.open(File.join(source_path, file_name), 'w') { |file| file.write 'content of file' }

    folder_path = File.join(Settings.invoices_integration&.public_send("#{share_name}_path"),
                            folder)
    subject.perform(file_name, folder, share_name)

    assert File.exist?(File.join(folder_path, file_name))
  end
end
