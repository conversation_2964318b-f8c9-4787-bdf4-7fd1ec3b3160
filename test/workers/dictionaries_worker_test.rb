require 'test_helper'

class DictionariesWorkerTest < ActiveSupport::TestCase
  test 'generates dictionaries export file' do
    Timecop.freeze do
      company = companies(:one)
      path = Settings.invoices_integration.files_path
      regular_name =
        "#{Time.zone.now.utc.to_formatted_s(:number)}#{company.name.parameterize}_dictionary.xml"

      clients_projects_name = "#{company.name.parameterize}_clients_projects_dictionary.xml"

      regular_file_path = File.join(path, regular_name)
      clients_projects_file_path = File.join(path, clients_projects_name)

      DictionariesWorker.new.perform

      assert File.exist?(regular_file_path)
      assert File.exist?(clients_projects_file_path)

      File.delete(regular_file_path)
      File.delete(clients_projects_file_path)
    end
  end
end
