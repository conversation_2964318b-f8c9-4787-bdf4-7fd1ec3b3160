require 'test_helper'

class RedmineRolesWorkerTest < ActiveSupport::TestCase
  setup do
    @worker = RedmineRolesWorker.new
  end

  test 'creates redmine role and sets redmine_id' do
    VCR.use_cassette('create_redmine_role') do
      role = roles(:front)
      result = @worker.perform(:update, role.id)
      assert role.reload.redmine_id.present?
      assert_equal 201, result.code
    end
  end

  test 'updates redmine role and unlocks edition' do
    VCR.use_cassette('update_redmine_role') do
      role = roles(:account)
      role.update_attribute(:name, 'Edited role name')
      assert @worker.perform(:update, role.id)
    end
  end

  test 'destroys role' do
    VCR.use_cassette('destroy_redmine_role') do
      role = roles(:account)
      result = @worker.perform(:destroy, role.redmine_id)
      assert result
    end
  end
end
