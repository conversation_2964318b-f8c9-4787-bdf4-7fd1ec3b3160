require 'test_helper'

class PendingAssetsReminderWorkerTest < ActiveSupport::TestCase
  test 'sends pending assets emails' do
    server = Server.pending.first
    vpn = Vpn.pending.first

    vpn.update!(project: nil)
    ActionMailer::Base.deliveries.clear

    pm = User.project_pms(server.project_id).first
    chief = vpn.user.department.chief

    assert_difference('ActionMailer::Base.deliveries.count', 2) do
      PendingAssetsReminderWorker.new.perform
    end
    assert_same_elements [pm.email, chief.email], ActionMailer::Base.deliveries.map(&:to).flatten
  end
end
