require 'test_helper'

class ProjectResponsibleReminderWorkerTest < ActiveSupport::TestCase
  before do
    Settings.responsible_notified_positions = [positions(:head_of_client_service).id]
  end

  let(:project) { projects(:three) }

  test 'calls ResponsibleUsersMailer with proper parameters' do
    assert_difference('ActionMailer::Base.deliveries.count') do
      ProjectResponsibleReminderWorker.new.perform
    end

    email = ActionMailer::Base.deliveries.last

    assert_match(/#{project.name}/, email.html_part.body.to_s)
  end
end
