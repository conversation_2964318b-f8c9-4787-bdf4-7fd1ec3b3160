require 'test_helper'

class InvoiceAllocationVersionsWorkerTest < ActiveSupport::TestCase
  test 'does nothing if there are no allocation versions' do
    Timecop.freeze(2.days.from_now) do
      assert_no_difference('ActionMailer::Base.deliveries.count') do
        InvoiceAllocationVersionsWorker.new.perform
      end
    end
  end

  test 'calls InvoiceAllocationVersionMailer' do
    assert_difference('ActionMailer::Base.deliveries.count') do
      InvoiceAllocationVersionsWorker.new.perform
    end
  end
end
