require 'test_helper'

class GusValidatorWorkerTest < ActiveSupport::TestCase
  test 'perform calls save on every contractor from GUS' do
    contractors = Contractor.limit(2)
    contractor_1 = contractors.first
    contractor_2 = contractors.second

    contractor_1.expects(:save)
    contractor_2.expects(:save)

    contractors.expects(:find_each).multiple_yields([contractor_1], [contractor_2])

    Contractor.expects(:where).with(download_from_gus: true).returns(contractors)

    GusValidatorWorker.new.perform
  end
end
