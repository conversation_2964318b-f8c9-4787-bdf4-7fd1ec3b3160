require 'test_helper'

class RemoteWorkPeriodNotifierWorkerTest < ActiveSupport::TestCase
  let(:remote_work_period) { remote_work_periods(:milosz_remote_period) }

  test 'notification is sent every 2 days at first and every day after that' do
    travel (1.day + 1.minute) # minute offset to avoid created_at being a little bit too new for 5 day period check
    assert_no_difference -> { RemoteWorkPeriodMailer.deliveries.count } do
      RemoteWorkPeriodNotifierWorker.new.perform
    end

    travel 1.day
    assert_difference -> { RemoteWorkPeriodMailer.deliveries.count } do
      RemoteWorkPeriodNotifierWorker.new.perform
    end

    travel 1.day
    assert_no_difference -> { RemoteWorkPeriodMailer.deliveries.count } do
      RemoteWorkPeriodNotifierWorker.new.perform
    end

    travel 1.day
    assert_difference -> { RemoteWorkPeriodMailer.deliveries.count } do
      RemoteWorkPeriodNotifierWorker.new.perform
    end

    travel 1.day
    assert_difference -> { RemoteWorkPeriodMailer.deliveries.count } do
      RemoteWorkPeriodNotifierWorker.new.perform
    end

    travel 1.day
    assert_difference -> { RemoteWorkPeriodMailer.deliveries.count } do
      RemoteWorkPeriodNotifierWorker.new.perform
    end
  end

  test 'notification is not being sent if remote work period is accepted' do
    user = users(:wiktoria)

    remote_work_period.accept!(examiner: user)

    travel 2.days
    assert_no_difference -> { RemoteWorkPeriodMailer.deliveries.count } do
      RemoteWorkPeriodNotifierWorker.new.perform
    end
  end
end
