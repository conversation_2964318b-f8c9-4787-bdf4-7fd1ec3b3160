require 'test_helper'

class OutlookCalendarEventsWorkerTest < ActiveSupport::TestCase
  let(:holiday_request) { holiday_requests(:visible_holiday_request) }
  let(:applicant) { holiday_request.applicant }
  let(:event_mock) { mock }
  let(:event_id) { 'abcd1234' }

  subject { OutlookCalendarEventsWorker.new }

  setup do
    RequestStore.store[Userstamping.config[:store_key]] = applicant.to_global_id.to_s
  end

  test 'creates outlook calendar event' do
    holiday_request.update(outlook_calendar_event_id: nil)
    event_mock.expects(:id).returns(event_id)
    MicrosoftGraphApi::Events.expects(:create_user_event)
                             .with(applicant, instance_of(MicrosoftGraph::Models::Event))
                             .returns(event_mock)

    assert_changes 'holiday_request.reload.outlook_calendar_event_id', from: nil, to: event_id do
      subject.perform('create', applicant.id, holiday_request.id, nil)
    end
  end

  test 'updates outlook calendar event' do
    event_mock.expects(:id).returns(event_id)
    MicrosoftGraphApi::Events.expects(:delete_user_event)
                             .with(applicant, holiday_request.outlook_calendar_event_id)
    MicrosoftGraphApi::Events.expects(:create_user_event)
                             .with(applicant, instance_of(MicrosoftGraph::Models::Event))
                             .returns(event_mock)

    assert_changes 'holiday_request.reload.outlook_calendar_event_id', from: holiday_request.outlook_calendar_event_id,
                                                                       to: event_id do
      subject.perform('update', applicant.id, holiday_request.id, holiday_request.outlook_calendar_event_id)
    end
  end

  test 'destroys outlook calendar event' do
    MicrosoftGraphApi::Events.expects(:delete_user_event)
                             .with(applicant, holiday_request.outlook_calendar_event_id)

    assert_changes 'holiday_request.reload.outlook_calendar_event_id', from: holiday_request.outlook_calendar_event_id,
                                                                       to: nil do
      subject.perform('destroy', applicant.id, holiday_request.id, holiday_request.outlook_calendar_event_id)
    end
  end
end
