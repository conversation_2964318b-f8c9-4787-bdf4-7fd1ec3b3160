require 'test_helper'

class UserEntryCardsWorkerTest < ActiveSupport::TestCase
  test 'exports entry cards csv file' do
    freeze_time do
      path = Settings.invoices_integration.files_path
      timestamp = Time.zone.today.strftime('%Y%m%d')
      name = "entry_cards_#{timestamp}.csv"
      file_path = File.join(path, name)

      assert_difference -> { SMBWorker.jobs.count } do
        UserEntryCardsWorker.new.perform
      end

      assert File.exist?(file_path)

      File.delete(file_path)
    end
  end
end
