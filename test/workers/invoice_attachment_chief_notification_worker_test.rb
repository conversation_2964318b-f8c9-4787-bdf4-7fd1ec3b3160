require 'test_helper'

class InvoiceAttachmentChiefNotificationWorkerTest < ActiveSupport::TestCase
  test 'perform' do
     invoices(:payment_ten_amendment).update_column(:no_attachment, false)
     user = users(:wik<PERSON>)

     assert_difference('ActionMailer::Base.deliveries.count') do
       InvoiceAttachmentChiefNotificationWorker.new.perform
     end

     mail = ActionMailer::Base.deliveries.last
     assert_includes mail.to, user.email
  end
end
