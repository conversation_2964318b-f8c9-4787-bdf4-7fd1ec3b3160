# encoding: UTF-8
require 'test_helper'

class HolidayRequestReminderWorkerTest < ActiveSupport::TestCase
  include ActiveJob::TestHelper

  fixtures :users, :departments

  # Note that inline mode must be configured during test setup, running inside
  # of a testing block won't propagate between client (browser) and server
  # (rails) processes.

  setup do
    Timecop.travel(Time.local(2017, 11, 21, 14, 30))
    Settings.reload!
    Sidekiq::Testing.fake!
    Sidekiq::Worker.clear_all
    @worker = HolidayRequestReminderWorker.new
    Rails.configuration.action_mailer.perform_deliveries = true
    ActionMailer::Base.deliveries.clear
    @last_sent_at = Time.current - 10.day
    @starts_on = (Time.current + 1.month).to_date
    @ends_on = (Time.current + 1.month + 4.days).to_date
    @examiner = nil
  end

  teardown do
    Timecop.return
    Rails.configuration.action_mailer.perform_deliveries = false
    Sidekiq::Worker.clear_all
  end

  def test_perform
    HolidayRequest.new(holiday_request_params).save!
    perform_enqueued_jobs do
      @worker.perform
      refute ActionMailer::Base.deliveries.empty?
      assert_equal ['<EMAIL>'], ActionMailer::Base.deliveries.first.to
      subject = 'Wniosek niedostępności użytkownika mkalita_user_alternative oczekuje na akceptację (przypomnienie)'
      assert_equal subject, ActionMailer::Base.deliveries.first.subject
      notification = Notification.last
      assert_equal subject, notification.title
      refute_empty notification.email_dispatches
      assert_equal 'holiday_request_reminder', notification.origin
      refute_nil notification.last_sent_at
    end
  end

  def test_perform_if_not_disabled_in_settings
    Settings.disable_hr_workers = Config::Options.new(disable_holiday_request_reminder_worker: false)
    HolidayRequest.new(holiday_request_params).save!
    perform_enqueued_jobs do
      @worker.perform
      refute ActionMailer::Base.deliveries.empty?
    end
  end

  def test_perform_if_not_disabled_in_settings_2
    Settings.disable_hr_workers = false
    HolidayRequest.new(holiday_request_params).save!
    perform_enqueued_jobs do
      @worker.perform
      refute ActionMailer::Base.deliveries.empty?
    end
  end

  def test_perform_noop_if_disabled_in_settings
    Settings.disable_hr_workers = Config::Options.new(disable_holiday_request_reminder_worker: true)
    HolidayRequest.new(holiday_request_params).save!
    Sidekiq::Testing.inline! do
      @worker.perform
      assert ActionMailer::Base.deliveries.empty?
    end
  end

  def test_perform_noop_if_disabled_in_settings_2
    Settings.disable_hr_workers = true
    HolidayRequest.new(holiday_request_params).save!
    Sidekiq::Testing.inline! do
      @worker.perform
      assert ActionMailer::Base.deliveries.empty?
    end
  end

  def test_perform_send_to_examiner_if_present
    @examiner = users(:mkalita_user)
    HolidayRequest.new(holiday_request_params).save!
    # inline! - is not good here, because when user is touched by test, LDAP workers is executed synchronously also
    # deliver_now is used based if Rails.env.test?
    # Sidekiq::Testing.inline! do
    perform_enqueued_jobs do
      @worker.perform
      refute ActionMailer::Base.deliveries.empty?
      assert_equal ['<EMAIL>'], ActionMailer::Base.deliveries[0].to
      assert_equal ['<EMAIL>'], ActionMailer::Base.deliveries[1].to
      assert_equal 'Wniosek niedostępności użytkownika mkalita_user_alternative oczekuje na akceptację (przypomnienie)', ActionMailer::Base.deliveries.first.subject
    end
    # end
  end

  def test_perform_send_to_chief_if_present
    applicant = users(:mkalita_user_alternative)
    deparment = departments(:mkalita_department)
    deparment.chief = users(:mkalita_user)
    deparment.save!
    applicant.department = deparment
    applicant.save!
    HolidayRequest.new(holiday_request_params).save!
    # inline! - is not good here, because when user is touched LDAP workers is executed synchronously also,
    # deliver_now is used based if Rails.env.test?
    # Sidekiq::Testing.inline! do
    perform_enqueued_jobs do
      @worker.perform
      refute ActionMailer::Base.deliveries.empty?
      assert_equal ['<EMAIL>'], ActionMailer::Base.deliveries[0].to
      assert_equal ['<EMAIL>'], ActionMailer::Base.deliveries[1].to
      assert_equal 'Wniosek niedostępności użytkownika mkalita_user_alternative oczekuje na akceptację (przypomnienie)', ActionMailer::Base.deliveries.first.subject
    end
    # end
  end

  def test_perform_add_hr_to_receipients_if_expiring
    HolidayRequest.new(holiday_request_params.merge(created_at: 6.days.ago)).save!
    perform_enqueued_jobs do
      @worker.perform
      refute ActionMailer::Base.deliveries.empty?
      assert_equal 'Wniosek niedostępności użytkownika mkalita_user_alternative oczekuje na akceptację (przypomnienie)', ActionMailer::Base.deliveries.first.subject
    end
  end


  def test_perform_skip_when_notification_on_create_last_sent_at_over_2_days_ago
    @last_sent_at = Time.current - 3.days
    holiday_request = HolidayRequest.new(holiday_request_params)
    holiday_request.save!
    notification = Notification.new(
      user_id: users(:mkalita_user).id,
      subject: holiday_request,
      origin: 'holiday_request_created'
    )

    notification.title = 'Holiday request waits for approval (reminder)'
    notification.acknowledge_dispatch!(@last_sent_at)
    notification.save!
    # Sidekiq::Testing.inline! do
    perform_enqueued_jobs do
      @worker.perform
      refute ActionMailer::Base.deliveries.empty?
    end
    # end
  end

  def test_perform_skip_when_notification_on_create_last_sent_at_closer_than_2_days
    @last_sent_at = Time.current - 1.day
    holiday_request = HolidayRequest.new(holiday_request_params)
    holiday_request.save!
    notification = Notification.new(
      user_id: users(:mkalita_user).id,
      subject: holiday_request,
      origin: 'holiday_request_created'
    )

    notification.title = 'Holiday request waits for approval (reminder)'
    notification.acknowledge_dispatch!(@last_sent_at)
    notification.save!
    # Sidekiq::Testing.inline! do
      @worker.perform
      assert ActionMailer::Base.deliveries.empty?
    # end
  end

  def test_perform_skip_when_reminder_last_sent_at_over_2_days_ago
    @last_sent_at = Time.current - 3.day
    holiday_request = HolidayRequest.new(holiday_request_params)
    holiday_request.save!
    notification = Notification.new(
      user_id: users(:mkalita_user).id,
      subject: holiday_request,
      origin: 'holiday_request_reminder'
    )
    notification.title = 'Holiday request waits for approval (reminder)'
    notification.acknowledge_dispatch!(@last_sent_at)
    notification.save!
    # Sidekiq::Testing.inline! do
    perform_enqueued_jobs do
      @worker.perform
      refute ActionMailer::Base.deliveries.empty?
    end
    # end
  end

  def test_perform_skip_when_reminder_last_sent_at_closer_than_2_days
    @last_sent_at = Time.current - 1.day
    holiday_request = HolidayRequest.new(holiday_request_params)
    holiday_request.save!
    notification = Notification.new(
      user_id: users(:mkalita_user).id,
      subject: holiday_request,
      origin: 'holiday_request_reminder'
    )
    notification.title = 'Holiday request waits for approval (reminder)'
    notification.acknowledge_dispatch!(@last_sent_at)
    notification.save!
    # Sidekiq::Testing.inline! do
      @worker.perform
      assert ActionMailer::Base.deliveries.empty?
    # end
  end

  private

  def holiday_request_params
    {
      applicant_id: users(:mkalita_user_alternative).id,
      starts_on: @starts_on,
      ends_on: @ends_on,
      category: 'Niedostępność',
      examiner_id: @examiner ? @examiner.id : nil,
      created_by_user_id: users(:mkalita_user_alternative).id,
      updated_by_user_id: users(:mkalita_user_alternative).id,
      modified_by_user_at: Time.zone.now
    }
  end
end
