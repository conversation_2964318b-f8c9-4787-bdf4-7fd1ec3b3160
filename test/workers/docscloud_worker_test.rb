require 'test_helper'
require 'owncloud_provider'

class DocscloudWorkerTest < ActiveSupport::TestCase
  setup do
    @provider_instance_mock = mock
    OwncloudProvider.expects('new').with('docscloud').returns(@provider_instance_mock).once
    @worker = DocscloudWorker.new
  end

  test 'calls owncloud provider with correct parameters on create' do
    args = ['create_directory', 'directory_name', 23]
    @provider_instance_mock.expects(args.first)
                           .with(*args.last(2),
                                 subdirectories: DocsFile.categories.transform_values { nil })
                           .once
    @worker.perform(*args)
  end

  test 'calls owncloud provider on share' do
    args = ['update_directory_share', 1111]
    @provider_instance_mock.expects(args.first).with(args.last).once
    @worker.perform(*args)
  end
end
