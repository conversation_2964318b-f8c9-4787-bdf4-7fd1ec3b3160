require 'test_helper'

class ReportedTimeWorkerTest < ActiveSupport::TestCase
  let(:company) { companies(:one) }

  before do
    stub_request(:get, "#{Settings.click_up_api.uri}/team/#{company.click_up_workspace_id}/time_entries")
      .with(query: { start_date: (Date.today.to_datetime.beginning_of_month.to_i * 1000) - 100,
                     end_date: (Date.today.to_datetime.end_of_month.end_of_day.to_i * 1000) + 100,
                     assignee: users(:wik<PERSON>).click_up_id, include_location_names: true,
                     limit: 100 })
      .to_return(
        status: 200,
        body: { 'data' => [
          { 'id' => 'entry_1', 'duration' => 20000000, 'start' => (Date.today.to_datetime.to_i - 2) * 1000,
            'end' => (Date.today.to_datetime - 2).end_of_day.to_i * 1000, 'task' => { 'id' => 'task_123' },
            'task_location' => { folder_id: '548615489651' }  },
          { 'id' => 'entry_2', 'duration' => 15000000, 'start' => (Date.today.to_datetime.to_i - 2) * 1000,
            'end' => (Date.today.to_datetime - 2).end_of_day.to_i * 1000, 'task' => { 'id' => 'task_123' },
            'task_location' => { folder_id: '548615489651' }  }
        ] }.to_json,
        headers: { 'Content-Type' => 'application/json' }
      )
    stub_request(:get, "#{Settings.click_up_api.uri}/team/#{company.click_up_workspace_id}/time_entries")
      .with(query: { start_date: (Date.today.to_datetime.beginning_of_month.to_i * 1000) - 100,
                     end_date: (Date.today.to_datetime.end_of_month.end_of_day.to_i * 1000) + 100,
                     assignee: users(:mikolaj).click_up_id, include_location_names: true,
                     limit: 100 })
      .to_return(
        status: 200,
        body: { 'data' => [
          { 'id' => 'entry_1', 'duration' => 10000000, 'start' => (Date.today.to_datetime.to_i - 2) * 1000,
            'end' => (Date.today.to_datetime - 2).end_of_day.to_i * 1000, 'task' => { 'id' => 'task_123' },
            'task_location' => { folder_id: '548615486151' } },
          { 'id' => 'entry_2', 'duration' => 15000000, 'start' => (Date.today.to_datetime.to_i - 2) * 1000,
            'end' => (Date.today.to_datetime - 2).end_of_day.to_i * 1000, 'task' => { 'id' => 'task_123' },
            'task_location' => { folder_id: '548618486151' }  }
        ] }.to_json,
        headers: { 'Content-Type' => 'application/json' }
      )
  end

  test 'perform' do
    stub_redmine_time_import_success
    internal_user = users(:internal_user)
    department = departments(:board_member_department)

    internal_user.update(department_id: department.id)
    department.update(chief_id: internal_user.id, uber_chief_id: internal_user.id)

    assert_difference('ActionMailer::Base.deliveries.count', Department.joins(:users).distinct.count) do
      ReportedTimeWorker.new.perform
    end
  end
end
