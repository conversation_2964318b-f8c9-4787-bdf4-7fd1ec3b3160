require 'test_helper'

class InvoiceAttachmentNotificationoWorkerTest < ActiveSupport::TestCase
  test 'perform' do
    invoices(:payment_ten_amendment).update_column(:no_attachment, false)
    user = users(:mikolaj)

    assert_difference('ActionMailer::Base.deliveries.count', 1) do
      InvoiceAttachmentNotificationWorker.new.perform
    end

    mail = ActionMailer::Base.deliveries.last
    assert_includes mail.to, user.email
  end
end
