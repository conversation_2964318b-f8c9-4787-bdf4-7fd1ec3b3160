require 'test_helper'
require 'owncloud_provider'

class AcceptanceReportDocscloudWorkerTest < ActiveSupport::TestCase
  setup do
    @provider_instance_mock = mock
    OwncloudProvider.expects('new').with('docscloud').returns(@provider_instance_mock).once
  end

  test 'calls owncloud provider with correct parameters' do
    attachment = attachments(:invoice_attachment)
    attachment.attachable.project.update_column(:docs_cloud, true)
    path = File.join(
      attachment.attachable.project.identifier,
      '05. Faktury i załączniki do faktur',
      attachment.attachable.id.to_s
    )

    file = attachment.file
    @provider_instance_mock.expects(:config_valid?).once.returns(true)
    @provider_instance_mock.expects(:send_file).with(file, path, file.metadata['filename']).once
                           .returns(true)

    AcceptanceReportDocscloudWorker.new.perform(attachment.id)
  end
end
