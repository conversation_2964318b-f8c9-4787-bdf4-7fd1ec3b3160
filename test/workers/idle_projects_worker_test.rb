require 'test_helper'

class IdleProjectsWorkerTest < ActiveSupport::TestCase
  subject { IdleProjectsWorker.new }
  let(:project) { projects(:one) }

  it 'unmarks projects' do
    project.update(idle: true)

    assert_changes 'project.reload.idle?', from: true, to: false do
      subject.perform
    end
  end

  it 'marks idle projects' do
    project.update(starts_on: IdleProjectsWorker::CHECK_PERIOD.ago)

    assert_changes 'project.reload.idle?', from: false, to: true do
      subject.perform
    end
  end

  it 'sends notifications' do
    project.update(starts_on: IdleProjectsWorker::CHECK_PERIOD.ago)

    assert_difference('ActionMailer::Base.deliveries.count', 3) do
      subject.perform
    end
  end

  it 'updates job last time' do
    travel 1.hour

    assert_changes 'SidekiqScheduler::RedisManager.get_job_last_time(IdleProjectsWorker::JOB_NAME)', to: /#{Time.zone.today}/ do
      subject.perform
    end
  end
end
