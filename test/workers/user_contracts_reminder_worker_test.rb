require 'test_helper'

class UserContractsReminderWorkerTest < ActiveSupport::TestCase
  subject { UserContractsReminderWorker.new }
  let(:user_contract) { user_contracts(:milosz_contract) }

  test 'does not remind if contract is not expiring' do
    user_contract.update!(ends_on: 7.weeks.from_now.to_date)

    assert_no_difference -> { UserContractsReminderMailer.deliveries.count } do
      subject.perform
    end
  end

  test 'reminds 4 weeks before expiration' do
    freeze_time do
      user_contract.update!(ends_on: 4.weeks.from_now.to_date)

      assert_difference -> { UserContractsReminderMailer.deliveries.count }, 2 do
        subject.perform
      end
    end
  end

  test 'reminds 6 weeks before expiration' do
    freeze_time do
      user_contract.update!(ends_on: 6.weeks.from_now.to_date)

      assert_difference -> { UserContractsReminderMailer.deliveries.count }, 2 do
        subject.perform
      end
    end
  end
end
