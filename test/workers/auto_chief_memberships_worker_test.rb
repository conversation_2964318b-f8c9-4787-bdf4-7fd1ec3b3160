require 'test_helper'

class AutoChiefMembershipsWorkerTest < ActiveSupport::TestCase
  setup do
    Settings.default_chief_role = 'Observer'
  end

  test 'add observers to projects' do
    project = Project.find_by(name: 'MyString')
    user = users(:mkalita_user)
    wiktoria = users(:wiktoria)

    assert_nil project.memberships.find_by(member: user)
    assert_not_nil project.memberships.find_by(member: wiktoria)

    AutoChiefMembershipsWorker.new.perform

    member = project.memberships.find_by(member: user)
    member_wiktoria = project.memberships.find_by(member: wiktoria)

    assert_not_nil member
    assert_includes member.membership_roles.map { |mr| mr.role.name }, Settings.default_chief_role
    assert member.membership_roles.includes(:role).find_by(role: { name: Settings.default_chief_role })&.auto_created
    assert_not_includes member_wiktoria.membership_roles
                                       .map { |mr| mr.role.name }, Settings.default_chief_role
  end
  test 'remove redundant observers from projects' do
    project = Project.find_by(name: 'MyString')

    user = users(:mkalita_user)
    wiktoria = users(:wiktoria)
    wilhelm = users(:wilhelm)

    assert_nil project.memberships.find_by(member: user)
    assert_not_nil project.memberships.find_by(member: wiktoria)

    AutoChiefMembershipsWorker.new.perform

    member = project.memberships.find_by(member: user)
    member_wiktoria = project.memberships.find_by(member: wiktoria)

    assert_not_nil member
    assert_includes member.membership_roles.map { |mr| mr.role.name }, Settings.default_chief_role
    assert member.membership_roles.includes(:role).find_by(role: { name: Settings.default_chief_role })&.auto_created
    assert_not_includes member_wiktoria.membership_roles
                                       .map { |mr| mr.role.name }, Settings.default_chief_role

    wiktoria.department.update(chief_id: wilhelm.id)

    AutoChiefMembershipsWorker.new.perform

    member_wiktoria = project.memberships.find_by(member: wiktoria)
    member_wilhelm = project.memberships.find_by(member: wilhelm)

    assert_nil project.memberships.find_by(member: user)
    assert_not_nil member_wilhelm
    assert_includes member_wilhelm.membership_roles.map { |mr| mr.role.name }, Settings.default_chief_role
    assert member_wilhelm.membership_roles
                         .includes(:role).find_by(role: { name: Settings.default_chief_role })&.auto_created
    assert_not_includes member_wiktoria.membership_roles
                                       .map { |mr| mr.role.name }, Settings.default_chief_role
  end
end
