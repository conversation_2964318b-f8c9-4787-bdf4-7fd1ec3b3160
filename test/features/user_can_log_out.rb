require 'test_helper'

class UserCanLogOut < AppCapybaraRailsTestCase
  scenario 'global_admin signs out', js: true do
    sign_in_as_admin
    refute_button page, 'Sign in'
    assert_content page, 'PROJECTS'
    assert_content page, 'Capybara Admin'

    find('a', text: 'power_settings_new').click
    wait_for_url(:hash, '/profile')
    refute_content page, 'PROJECTS'
    refute_content page, 'Capybara Admin'
  end

  scenario 'global_user signs out', js: true do
    sign_in_as_user
    refute_button page, 'Sign in'
    assert_content page, 'PROJECTS'
    assert_content page, 'Capybara User'

    find('a', text: 'power_settings_new').click
    wait_for_url(:hash, '/profile')
    refute_content page, 'PROJECTS'
    refute_content page, 'Capybara User'
  end
end
