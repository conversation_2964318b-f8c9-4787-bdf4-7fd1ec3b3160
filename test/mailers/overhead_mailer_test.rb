require 'test_helper'

class OverheadMailerTest < ActionMailer::TestCase
  test 'mail is sent with proper content to global accounts' do
    accounting_number = accounting_numbers(:artegence_wew_number)
    user = users(:wik<PERSON>)

    email = OverheadMailer.notify(accounting_number.id)

    assert_equal([user.email], email.to)
    assert_match accounting_number.number.to_s, email.html_part.body.to_s
    assert_match accounting_number.number.to_s, email.text_part.body.to_s
  end
end
