require 'test_helper'

module Dms
  class NotificationMailerTest < ActionMailer::TestCase
    let(:user) { users(:wik<PERSON>) }
    let(:cost_invoice) { cost_invoices(:dms_pending_cost_invoice) }

    test 'notify_author' do
      mail = NotificationMailer.notify_author(cost_invoice)

      assert_equal([cost_invoice.user.email], mail.to)
      assert_match('[DMS] Draft cost invoice awaiting', mail.subject)
    end

    test 'notify_supervisor_periodically' do
      mail = NotificationMailer.notify_supervisor_periodically(user, [cost_invoice])

      assert_equal([user.email], mail.to)
      assert_match('[DMS] Cost invoices awaiting review', mail.subject)
      assert_match(cost_invoice.id.to_s, mail.body.parts.first.to_s)
    end

    test 'notify_supervisor' do
      mail = NotificationMailer.notify_supervisor(user, cost_invoice)

      assert_equal([user.email], mail.to)
      assert_match('[DMS] New cost invoice awaiting review', mail.subject)
      assert_match(cost_invoice.id.to_s, mail.body.parts.first.to_s)
    end

    test 'notify_rejected' do
      mail = NotificationMailer.notify_rejected(cost_invoice)
      assert_equal([cost_invoice.user.email], mail.to)
      assert_match('[DMS] Your cost invoice returned for correction', mail.subject)
      assert_match(cost_invoice.id.to_s, mail.body.parts.first.to_s)
    end

    test 'notify_accepted' do
      mail = NotificationMailer.notify_accepted(cost_invoice)
      assert_equal([cost_invoice.user.email], mail.to)
      assert_match('[DMS] Your cost invoice has been accepted', mail.subject)
      assert_match(cost_invoice.id.to_s, mail.body.parts.first.to_s)
    end
  end
end
