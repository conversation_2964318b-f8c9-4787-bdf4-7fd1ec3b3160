require 'test_helper'

class UserContractsReminderMailerTest < ActionMailer::TestCase
  let(:user_contract) { user_contracts(:milosz_contract) }
  let(:chief) { user_contract.user.department.chief }
  let(:hr_coordinator) { users(:wik<PERSON>) }

  test 'six_weeks_chief_reminder' do
    email = UserContractsReminderMailer.six_weeks_chief_reminder(user_contract)

    assert_emails(1) { email.deliver_now }
    assert_includes email.to, chief.email

    assert_equal('Zbliżający się koniec umowy Pracownika/Współpracownika', email.subject)
    assert_match("Umowa dla #{user_contract.user.full_name} zbliża się", email.html_part.body.to_s)
  end

  test 'six_weeks_hr_reminder' do
    email = UserContractsReminderMailer.six_weeks_hr_reminder(user_contract)

    assert_emails(1) { email.deliver_now }
    assert_includes email.to, hr_coordinator.email

    assert_equal('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>cy się koniec umowy Pracownika/Współpracownika', email.subject)
    assert_match("W dniu #{I18n.l(user_contract.ends_on, format: :long, locale: :pl)} kończy się",
                 email.html_part.body.to_s)
  end

  test 'four_weeks_chief_reminder' do
    email = UserContractsReminderMailer.four_weeks_chief_reminder(user_contract)

    assert_emails(1) { email.deliver_now }
    assert_includes email.to, chief.email

    assert_equal('Zbliżający się koniec umowy Pracownika/Współpracownika', email.subject)
    assert_match("umowa #{user_contract.user.full_name} dobiega końca i trzeba działać!", email.html_part.body.to_s)
  end

  test 'four_weeks_hr_reminder' do
    email = UserContractsReminderMailer.four_weeks_hr_reminder(user_contract)

    assert_emails(1) { email.deliver_now }
    assert_includes email.to, hr_coordinator.email

    assert_equal('Zbliżający się koniec umowy Pracownika/Współpracownika', email.subject)
    assert_match("System krzyczy, że umowa dla #{user_contract.user.full_name} dobiega końca.",
                 email.html_part.body.to_s)
  end
end
