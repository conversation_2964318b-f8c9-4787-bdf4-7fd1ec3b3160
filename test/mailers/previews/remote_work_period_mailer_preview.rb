# Preview all emails at http://localhost:3000/rails/mailers/remote_work_period_mailer

class RemoteWorkPeriodMailerPreview < ActionMailer::Preview
  def remote_work_period_created
    RemoteWorkPeriodMailer.remote_work_period_created(RemoteWorkPeriod.first.id)
  end

  def remote_work_period_accepted
    RemoteWorkPeriodMailer.remote_work_period_accepted(RemoteWorkPeriod.first.id)
  end

  def remote_work_period_rejected
    RemoteWorkPeriodMailer.remote_work_period_rejected(RemoteWorkPeriod.first.id)
  end

  def remote_work_period_reminder
    RemoteWorkPeriodMailer.remote_work_period_reminder(RemoteWorkPeriod.first)
  end

  def remote_work_period_report
    RemoteWorkPeriodMailer.remote_work_period_report
  end
end
