module B2B
  class CostInvoiceNotificationMailerPreview < ActionMailer::Preview
    # Preview this email at http://localhost:3000/rails/mailers/b2b/cost_invoice_notification_mailer/notify_accepted
    def notify_accepted
      B2B::CostInvoiceNotificationMailer.with(controller_id: User.first.id,
                                              cost_invoice_id: B2B::CostInvoice.first.id)
                                        .notify_accepted
    end

    # Preview this email at http://localhost:3000/rails/mailers/b2b/cost_invoice_notification_mailer/notify_rejected
    def notify_rejected
      B2B::CostInvoiceNotificationMailer.with(controller_id: User.first.id,
                                              cost_invoice_id: B2B::CostInvoice.first.id)
                                        .notify_rejected
    end
  end
end
