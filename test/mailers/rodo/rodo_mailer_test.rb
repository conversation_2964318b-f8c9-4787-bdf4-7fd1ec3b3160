require 'test_helper'

module Rodo
  class RodoMailerTest < ActionMailer::TestCase
    test 'new_approval' do
      approval = approvals(:milosz_approval)

      email = RodoMailer.new_approval(approval)

      assert_equal [users(:milosz).email], email.to
      assert_match(/Powiadomienie/, email.subject)
    end

    test 'new_approval with project agreement' do
      approval = approvals(:mkalita_approval)

      email = RodoMailer.new_approval(approval)

      assert_equal [users(:mkalita_user).email], email.to
      assert_match(/#{approval.approvable.project.name}/, email.subject)
    end

    test 'rodo reminder' do
      user = users(:milosz)

      email = RodoMailer.rodo_reminder(user)

      assert_equal [user.email], email.to
      assert_match(/Powiadomienie/, email.subject)
    end
  end
end
