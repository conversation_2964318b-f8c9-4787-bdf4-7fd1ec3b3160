require 'test_helper'

class RemoteWorkPeriodMailerTest < ActionMailer::TestCase
  let(:remote_work_period) { remote_work_periods(:milosz_remote_period) }

  test 'remote_work_period_created' do
    email = RemoteWorkPeriodMailer.remote_work_period_created(remote_work_period.id)

    assert_match 'New remote work request', email.subject
    assert_includes email.to, remote_work_period.user.department.chief.email
    assert_includes email.cc,
                    User.joins(:global_roles).find_by(global_roles: { remote_work_periods_notifications: true }).email
  end

  test 'remote_work_period_accepted' do
    email = RemoteWorkPeriodMailer.remote_work_period_accepted(remote_work_period.id)

    assert_match 'Remote work request has been accepted', email.subject
    assert_equal [remote_work_period.user.email], email.to
    assert_includes email.cc,
                    User.joins(:global_roles).find_by(global_roles: { remote_work_periods_notifications: true }).email
  end

  test 'remote_work_period_rejected' do
    email = RemoteWorkPeriodMailer.remote_work_period_rejected(remote_work_period.id)

    assert_match 'Remote work request has been rejected', email.subject
    assert_equal [remote_work_period.user.email], email.to
    assert_includes email.cc,
                    User.joins(:global_roles).find_by(global_roles: { remote_work_periods_notifications: true }).email
  end

  test 'remote_work_period_reminder' do
    email = RemoteWorkPeriodMailer.remote_work_period_reminder(remote_work_period)

    assert_match "Remote work request for #{remote_work_period.user.full_name} is waiting for acceptance", email.subject
    assert_includes email.to, remote_work_period.user.department.chief.email
  end

  test 'remote_work_period_report' do
    email = RemoteWorkPeriodMailer.remote_work_period_report

    assert_includes email.to,
                    User.joins(:global_roles).find_by(global_roles: { remote_work_periods_notifications: true }).email
    assert_equal 2, email.attachments.reject(&:inline?).count
  end
end
