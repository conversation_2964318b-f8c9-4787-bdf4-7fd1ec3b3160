require 'test_helper'

class AdminRequestMailerTest < ActionMailer::TestCase
  test 'mail is sent ' do
    request = RequestTrackerIssue.new(title: 'title', project_id: projects(:two).id, content: 'test')
    current_user = users(:milosz)
    email = AdminRequestMailer.notify_admin(request, current_user)

    assert_emails 1 do
      email.deliver_now
    end
  end

  test 'mail with attachments is sent ' do
    location = Rails.root.join('test', 'fixtures', 'files', 'jpg_705kB.jpg')
    filename = 'jpg_705kB.jpg'
    request = RequestTrackerIssue.new(title: 'title', project_id: projects(:two).id, content: 'test',
              attachments_info: [{location: location, filename: filename}])
    current_user = users(:milosz)
    email = AdminRequestMailer.notify_admin(request, current_user)

    assert_emails 1 do
      email.deliver_now
    end
  end

  test 'mail with eml attachment is sent ' do
    location = Rails.root.join('test', 'fixtures', 'files', 'sample.eml')
    filename = 'sample.eml'
    request = RequestTrackerIssue.new(title: 'title', project_id: projects(:two).id, content: 'test',
                                      attachments_info: [{location: location, filename: filename}])
    current_user = users(:milosz)
    email = AdminRequestMailer.notify_admin(request, current_user)

    assert_emails 1 do
      email.deliver_now
    end

    assert_equal 'application/octet-stream', email.attachments.first.mime_type
  end

  test 'notify_admin_about_asset for VPN' do
    asset = vpns(:vpn_one)

    email = AdminRequestMailer.notify_admin_about_asset(asset)

    assert_match 'New Vpn request for', email.subject
  end

  test 'notify_admin_about_asset_decommission for VPN' do
    asset = vpns(:vpn_one)
    user = asset.user
    asset.activate(user)
    asset.close(user)

    email = AdminRequestMailer.notify_admin_about_asset_decommission(asset, user)

    assert_equal "#{asset.type} decommission request for #{user.full_name} (#{user.email})", email.subject
  end
end
