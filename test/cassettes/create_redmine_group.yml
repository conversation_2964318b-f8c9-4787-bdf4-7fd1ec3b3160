---
http_interactions:
- request:
    method: post
    uri: http://local.non.3dart.com:3001/imperator_api/v1/groups.json
    body:
      encoding: UTF-8
      string: '{"group":{"name":"MyString"}}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 201
      message: Created
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Location:
      - http://local.non.3dart.com:3001/groups/1156
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"90709823df99a0345f00dd4a49a7931c"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Set-Cookie:
      - request_method=POST; path=/
      X-Request-Id:
      - 3588d6b8-c3e9-46e7-8633-4068986864e2
      X-Runtime:
      - '0.099069'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"group":{"id":1156,"name":"MyString","custom_fields":[{"id":3,"name":"viewable
        in team time sheet","value":"0"}]}}'
    http_version: 
  recorded_at: Wed, 18 May 2016 09:29:15 GMT
recorded_with: VCR 3.0.1
