---
http_interactions:
- request:
    method: get
    uri: http://local.non.3dart.com:3001/imperator_api/v1/users/1155.json?include=memberships
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"7b3fbd4ec14eb0934612d7e9043a7048"
      Cache-Control:
      - max-age=0, private, must-revalidate
      X-Request-Id:
      - 5e40d34d-85ea-47f8-8a72-c5591180b1f4
      X-Runtime:
      - '0.071885'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"user":{"id":1155,"login":"whanow<PERSON><PERSON>","firstname":"<PERSON><PERSON><PERSON>","lastname":"<PERSON>owerska","mail":"<EMAIL>","created_on":"2016-05-17T12:33:56Z","api_key":"b21a1178f3506ae7d38f0395a6c06eb3dd773740","status":1,"custom_fields":[{"id":4,"name":"Team
        Stats","value":"2 weeks"}],"memberships":[{"id":71432,"project":{"id":818,"name":"MyStringTwo"},"roles":[{"id":33,"name":"Account"}]}]}}'
    http_version: 
  recorded_at: Tue, 31 May 2016 09:31:08 GMT
- request:
    method: put
    uri: http://local.non.3dart.com:3001/imperator_api/v1/memberships/71432.json
    body:
      encoding: UTF-8
      string: '{"membership":{"role_ids":[8]}}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Cache-Control:
      - no-cache
      Set-Cookie:
      - request_method=PUT; path=/
      X-Request-Id:
      - c2d6a800-cda2-4604-8f6c-a5e613500943
      X-Runtime:
      - '0.235046'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: ''
    http_version: 
  recorded_at: Tue, 31 May 2016 09:31:08 GMT
recorded_with: VCR 3.0.1
