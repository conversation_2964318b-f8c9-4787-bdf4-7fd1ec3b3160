---
http_interactions:
- request:
    method: get
    uri: http://local.non.3dart.com:3001/imperator_api/v1/users/1155.json?include=memberships
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"ca11600d3388662c43e1f202eff5a532"
      Cache-Control:
      - max-age=0, private, must-revalidate
      X-Request-Id:
      - 128a8ab0-aa8b-41a1-888b-e7605f4d8c9f
      X-Runtime:
      - '0.067729'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"user":{"id":1155,"login":"whanow<PERSON><PERSON>","firstname":"<PERSON><PERSON><PERSON>","lastname":"<PERSON>owerska","mail":"<EMAIL>","created_on":"2016-05-17T12:33:56Z","api_key":"b21a1178f3506ae7d38f0395a6c06eb3dd773740","status":1,"custom_fields":[{"id":4,"name":"Team
        Stats","value":"2 weeks"}],"memberships":[]}}'
    http_version: 
  recorded_at: Tue, 31 May 2016 10:40:11 GMT
recorded_with: VCR 3.0.1
