---
http_interactions:
- request:
    method: get
    uri: http://local.non.3dart.com:3001/imperator_api/v1/resource_bookings.json?date_from=2017-01-04&date_to=2017-01-09&f%5B%5D=assigned_to_id&f%5B%5D=project_id&limit=100&op%5Bassigned_to_id%5D==&op%5Bproject_id%5D==&page=1&v%5Bassigned_to_id%5D%5B0%5D=1155&v%5Bproject_id%5D%5B0%5D=&v%5Bproject_id%5D%5B1%5D=796
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 83f9887452a64366da8102e711a1284eb5dae0de
      X-Imperator-Api-Key:
      - de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"********************************"
      Cache-Control:
      - max-age=0, private, must-revalidate
      X-Request-Id:
      - 11dfc5d9-bdd8-4f50-a73d-622986f515a4
      X-Runtime:
      - '0.682884'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"resources":[{"id":26,"assigned_to_id":1155,"project_id":796,"issue_id":169527,"start_date":"2017-01-04T00:00:00+01:00","end_date":"2017-01-09T00:00:00+01:00","hours_per_day":8.0,"notes":".","created_at":"2023-07-17T11:05:46+02:00","updated_at":"2023-07-17T11:05:46+02:00"}],"total_count":1}'
  recorded_at: Mon, 02 Jan 2017 23:00:00 GMT
- request:
    method: delete
    uri: http://local.non.3dart.com:3001/imperator_api/v1/resource_bookings/destroy.json
    body:
      encoding: UTF-8
      string: '{"ids":[26]}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 83f9887452a64366da8102e711a1284eb5dae0de
      X-Imperator-Api-Key:
      - de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json
      Cache-Control:
      - no-cache
      X-Request-Id:
      - 1674334c-60c7-4c35-b815-ddc3d281ca09
      X-Runtime:
      - '0.028855'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: ''
  recorded_at: Mon, 02 Jan 2017 23:00:00 GMT
recorded_with: VCR 6.1.0
