---
http_interactions:
- request:
    method: get
    uri: http://local.non.3dart.com:3001/imperator_api/v1/projects/8/copy_source.json
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 200
      message: 'OK '
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"8a964ae165a9117e1ecbb224f39e8d5a"
      Cache-Control:
      - max-age=0, private, must-revalidate
      X-Request-Id:
      - be91e22d-9174-4a57-b405-997168eaeba1
      X-Runtime:
      - '0.045442'
      Server:
      - WEBrick/1.3.1 (Ruby/2.3.0/2015-12-25)
      Date:
      - Wed, 22 Jun 2016 08:58:19 GMT
      Content-Length:
      - '2166'
      Connection:
      - Keep-Alive
    body:
      encoding: UTF-8
      string: '{"id":null,"name":"","description":"original project description","homepage":"original-project","is_public":true,"parent_id":null,"created_on":"2016-06-16T13:00:28.000Z","updated_on":"2016-06-16T13:00:28.000Z","identifier":null,"status":1,"lft":null,"rgt":null,"inherit_members":false,"enabled_modules":[{"project_id":8,"id":154,"name":"news"},{"project_id":8,"id":159,"name":"issue_tracking"},{"project_id":8,"id":160,"name":"time_tracking"},{"project_id":8,"id":161,"name":"documents"},{"project_id":8,"id":162,"name":"files"},{"project_id":8,"id":163,"name":"wiki"},{"project_id":8,"id":164,"name":"repository"},{"project_id":8,"id":166,"name":"calendar"},{"project_id":8,"id":167,"name":"gantt"}],"trackers":[{"id":3,"name":"Support","is_in_chlog":false,"position":3,"is_in_roadmap":false,"fields_bits":0,"default_status_id":1}],"custom_values":[{"id":115,"custom_field_id":4,"customized_id":8,"customized_type":"Project","value":""},{"id":116,"custom_field_id":5,"customized_id":8,"customized_type":"Project","value":""}],"issue_custom_fields":[],"all_available_issue_cutom_fields":[{"id":6,"name":"dsdsds","field_format":"string","possible_values":[],"regexp":"","min_length":null,"max_length":null,"is_required":false,"is_for_all":false,"is_filter":false,"position":1,"searchable":false,"default_value":"","editable":true,"visible":true,"multiple":false,"format_store":{"text_formatting":"","url_pattern":""},"description":""},{"id":7,"name":"my_issue_field","field_format":"string","possible_values":[],"regexp":"","min_length":null,"max_length":null,"is_required":false,"is_for_all":false,"is_filter":false,"position":2,"searchable":false,"default_value":"","editable":true,"visible":true,"multiple":false,"format_store":{"text_formatting":"","url_pattern":""},"description":""}],"all_available_trackers":[{"id":1,"name":"Bug","is_in_chlog":true,"position":1,"is_in_roadmap":false,"fields_bits":0,"default_status_id":1},{"id":2,"name":"Feature","is_in_chlog":true,"position":2,"is_in_roadmap":true,"fields_bits":0,"default_status_id":1},{"id":3,"name":"Support","is_in_chlog":false,"position":3,"is_in_roadmap":false,"fields_bits":0,"default_status_id":1}]}'
    http_version: 
  recorded_at: Wed, 22 Jun 2016 08:58:19 GMT
recorded_with: VCR 3.0.3
