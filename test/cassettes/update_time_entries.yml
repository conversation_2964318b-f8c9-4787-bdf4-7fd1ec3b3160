---
http_interactions:
- request:
    method: get
    uri: http://local.non.3dart.com:3001/imperator_api/v1/time_entries.json?f%5B%5D=project_id&f%5B%5D=spent_on&f%5B%5D=user_id&limit=100&op%5Bproject_id%5D==&op%5Bspent_on%5D=%3E%3C&op%5Buser_id%5D==&page=1&v%5Bproject_id%5D%5B0%5D=&v%5Bproject_id%5D%5B1%5D=796&v%5Bspent_on%5D%5B0%5D=2017-01-03&v%5Bspent_on%5D%5B1%5D=2017-01-08&v%5Buser_id%5D%5B0%5D=1155
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 83f9887452a64366da8102e711a1284eb5dae0de
      X-Imperator-Api-Key:
      - de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"b583adf3cdad37192ed62c88ad84df92"
      Cache-Control:
      - max-age=0, private, must-revalidate
      X-Request-Id:
      - 855a47ce-be20-4d3c-b561-c3133a88432e
      X-Runtime:
      - '0.139688'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"time_entries":[{"id":457841,"project":{"id":1124,"name":"Konica Minolta
        - portal B2B - development"},"issue":{"id":182500},"user":{"id":1155,"name":"Diego
        Mendilaharzu"},"activity":{"id":9,"name":"Development"},"hours":8.0,"comments":".","spent_on":"2017-01-05","created_on":"2019-01-23T11:21:37+01:00","updated_on":"2019-01-23T11:21:37+01:00"},{"id":457840,"project":{"id":1124,"name":"Konica
        Minolta - portal B2B - development"},"issue":{"id":182500},"user":{"id":1155,"name":"Diego
        Mendilaharzu"},"activity":{"id":9,"name":"Development"},"hours":8.0,"comments":".","spent_on":"2017-01-04","created_on":"2019-01-23T11:21:37+01:00","updated_on":"2019-01-23T11:21:37+01:00"},{"id":457839,"project":{"id":1124,"name":"Konica
        Minolta - portal B2B - development"},"issue":{"id":182500},"user":{"id":1155,"name":"Diego
        Mendilaharzu"},"activity":{"id":9,"name":"Development"},"hours":8.0,"comments":".","spent_on":"2017-01-03","created_on":"2019-01-23T11:21:37+01:00","updated_on":"2019-01-23T11:21:37+01:00"}],"total_count":3,"offset":0,"limit":25}'
    http_version:
  recorded_at: Mon, 02 Jan 2017 23:00:00 GMT
- request:
    method: delete
    uri: http://local.non.3dart.com:3001/imperator_api/v1/time_entries/destroy.json
    body:
      encoding: UTF-8
      string: '{"ids":[457838,457841,457840,457839]}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 83f9887452a64366da8102e711a1284eb5dae0de
      X-Imperator-Api-Key:
      - de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json
      Cache-Control:
      - no-cache
      X-Request-Id:
      - 8d548d31-7d82-43b9-bf7f-469f36983ddf
      X-Runtime:
      - '0.093980'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: ''
    http_version:
  recorded_at: Mon, 02 Jan 2017 23:00:00 GMT
- request:
    method: post
    uri: http://local.non.3dart.com:3001/imperator_api/v1/time_entries.json
    body:
      encoding: UTF-8
      string: '{"time_entry":{"user_id":1155,"project_id":1124,"issue_id":182500,"spent_on":"2017-01-04","spent_to":"2017-01-09","comments":".","hours":8}}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 83f9887452a64366da8102e711a1284eb5dae0de
      X-Imperator-Api-Key:
      - de772c9e8a27127345d2256e20db61238d1fa73ec16484a7d335cf4b6969d3bed5f94a
  response:
    status:
      code: 201
      message: Created
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Location:
      - http://local.non.3dart.com:3001/time_entries/457842
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"281c77868ae95b8b9ceaa37d108502e3"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Set-Cookie:
      - _redmine_session=ZzJrMGVucldMNGhZa055cDF1UkhhZG1KUXhiZFh1MjVKalIyWU1LcDhqQlpKV1FhbHdacGdvSmpneUNCQVkrVEpPMkhSOEVJUjFGWVNqU2VIcGxJSlBUN1JEcGtDUVo2Q0dJU0VmUnB6cmo4OWlpUDEwc1JJcjdDa1BENE1MRTAtLW1aNDZveXd1bHJhMEgrREwzV3hMdnc9PQ%3D%3D--fc456d6370da4e8374b349f1ac19b9885033a036;
        path=/; HttpOnly
      X-Request-Id:
      - 62f3b5c2-c733-4b3d-89f2-1b0fc8a25a7c
      X-Runtime:
      - '0.160840'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"time_entry":{"id":457842,"project":{"id":1124,"name":"Konica Minolta
        - portal B2B - development"},"issue":{"id":182500},"user":{"id":1155,"name":"Diego
        Mendilaharzu"},"activity":{"id":9,"name":"Development"},"hours":8.0,"comments":".","spent_on":"2017-01-04","created_on":"2019-01-23T11:21:38+01:00","updated_on":"2019-01-23T11:21:38+01:00"}}'
    http_version:
  recorded_at: Mon, 02 Jan 2017 23:00:00 GMT
recorded_with: VCR 3.0.3
