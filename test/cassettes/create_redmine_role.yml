---
http_interactions:
- request:
    method: post
    uri: http://local.non.3dart.com:3001/imperator_api/v1/roles.json
    body:
      encoding: UTF-8
      string: '{"role":{"name":"Front"}}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 201
      message: Created
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Location:
      - http://local.non.3dart.com:3001/roles/31
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"25c0e3999af16b4d28f9c39240868e0a"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Set-Cookie:
      - request_method=POST; path=/
      X-Request-Id:
      - 177f4f1f-7297-4b0a-af54-7de2ba024826
      X-Runtime:
      - '0.208478'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"role":{"id":31,"name":"Front","permissions":[]}}'
    http_version: 
  recorded_at: Thu, 19 May 2016 12:39:09 GMT
recorded_with: VCR 3.0.1
