---
http_interactions:
- request:
    method: put
    uri: http://local.non.3dart.com:3001/imperator_api/v1/groups/1157.json
    body:
      encoding: UTF-8
      string: '{"group":{"name":"Last group"}}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Cache-Control:
      - no-cache
      Set-Cookie:
      - _redmine_session=WDViN2VtaVVYKys3R1YwZVZ1QVZYb3V5LzNkeVUzb2Rad3hKaUlJN0k4bElJbmtBTS9hcVBxRFZFZHBVQk90QzNiODFyR0JlaG5PRmg2WGtvTml4USt2QWNXNTRYM1h5ZE5ZZ2hrbHNYeUFjZDJPMmZzUDBPOTh4NjdiSHpwTWNEdzdUMjY2eTRxVTVieVhYOWt6c25TbHF5VGZ4cnFqekJWVllXOHNOaVJzTHpXRkd0Y1I5anVsdlJ4TGFhZm5wbExiWllsam1GMU95YmxoOFVHalE4UT09LS1BanI4VDl5cEZ6QzVWTzhZSmpnTmJBPT0%3D--723652b998428ee7c49c5661f757718ba344d6ae;
        path=/; HttpOnly
      - request_method=PUT; path=/
      X-Request-Id:
      - 7fe53ae3-f760-45c4-bad4-05b6003582f6
      X-Runtime:
      - '0.052853'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: ''
    http_version: 
  recorded_at: Wed, 18 May 2016 12:09:54 GMT
recorded_with: VCR 3.0.1
