require 'test_helper'

class EvaluationDecoratorTest < Draper::TestCase
  setup do
    @in_progress_subject = evaluations(:milosz_evaluation).decorate
    @pending_subject = evaluations(:milosz_pending_evaluation).decorate
  end

  test 'main answers are generated for evaluation in progress' do
    comment = answer_comments(:milosz_evaluation_comment)
    answer = question_answers(:onboarding_evaluated_question_answer)
    main_answers = @in_progress_subject.main_answers
    assert_equal 1, main_answers.count
    first_question_answers = main_answers.first
    assert_equal answer.content, first_question_answers[:evaluated]['content']
    assert_nil first_question_answers[:evaluator]['content']
    assert_equal comment.content, first_question_answers[:comment]['content']
  end

  test 'main answers return nil for pending evaluation' do
    assert_nil @pending_subject.main_answers
  end
end
