require 'test_helper'

class RoleDecoratorTest < Draper::TestCase
  fixtures :roles

  def test_roles
    assert roles(:mkalita_role).respond_to?(:decorate)
    assert_equal 'RoleDecorator', roles(:mkalita_role).decorate.class.name
    assert_instance_of Array, roles(:mkalita_role).decorate.class.attribute_names_visible_to_all
    assert_instance_of Array, roles(:mkalita_role).decorate.class.attribute_names_for_collection_for_select_visible_to_all
  end
end
