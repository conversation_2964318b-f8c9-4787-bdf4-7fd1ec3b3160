require 'test_helper'

class ApplicationDecoratorTest < Draper::TestCase
  fixtures :companies, :groups, :group_memberships, :memberships, :membership_roles, :projects, :roles, :users

  def test_app_decorator
    assert_kind_of Hash, groups(:mkalita_group).decorate.as_json
    assert groups(:mkalita_group).decorate.as_json['cache_ts']
    assert_kind_of Hash, memberships(:mkalita_membership_user).decorate.as_json
    assert_kind_of Hash, roles(:mkalita_role).decorate.as_json
    assert roles(:mkalita_role).decorate.as_json['cache_ts']
    assert_kind_of Hash, users(:mkalita_user).decorate.as_json
    assert users(:mkalita_user).decorate.as_json['cache_ts']
  end
end
