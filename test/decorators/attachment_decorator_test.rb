require 'test_helper'

class AttachmentDecoratorTest < Draper::TestCase
  test 'attributes visible to all' do
    assert attachments(:one).respond_to?(:decorate)
    assert_equal 'AttachmentDecorator', attachments(:one).decorate.class.name
    assert_instance_of Array, attachments(:one).decorate.class.attribute_names_visible_to_all
    assert_equal %i[id attachable_id attachable_type file_data created_at updated_at], attachments(:one).decorate.class.attribute_names_visible_to_all
  end
end
