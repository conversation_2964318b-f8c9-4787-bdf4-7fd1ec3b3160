require 'test_helper'

class UserDecoratorTest < Draper::TestCase
  fixtures :users

  test 'user decorator' do
    attributes_visible_to_all = %i[
      id username first_name last_name uid_number image email cloud docs_cloud redmine_id redmine company_id
      absence_quota absence_balance sick_absence_quota state created_at updated_at confirmed_at chat board_member
      confirmed_at system monitoring svn activates_on position_id remote time_reports_not_required
      profile_comment part_time activity_validation_disabled remote_allowed remote_yearly_limit contract_of_employment
    ]

    assert users(:mkalita_user).respond_to?(:decorate)
    assert_equal 'UserDecorator', users(:mkalita_user).decorate.class.name
    assert_instance_of Array, users(:mkalita_user).decorate.class.attribute_names_visible_to_all
    assert_equal attributes_visible_to_all, users(:mkalita_user).decorate.class.attribute_names_visible_to_all
    assert_instance_of Array, users(:mkalita_user).decorate.class.attribute_names_for_collection_for_select_visible_to_all
    assert users(:mkalita_user).decorate.respond_to?(:full_name)
  end
end
