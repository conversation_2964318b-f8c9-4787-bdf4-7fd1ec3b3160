require 'test_helper'

class Pages::ProfileDecoratorTest < Draper::TestCase
  def test_profile
    assert Pages::Profile.new(users(:mkalita_user)).respond_to?(:decorate)
    assert_equal 'Pages::ProfileDecorator', Pages::Profile.new(users(:mkalita_user)).decorate.class.name
    assert_raise(NotImplementedError) do
      Pages::Profile.new(users(:mkalita_user)).decorate.class.attribute_names_visible_to_all
    end
    assert_raise(NotImplementedError) do
      Pages::Profile.new(users(:mkalita_user)).decorate.class.attribute_names_for_collection_for_select_visible_to_all
    end
  end
end
