require 'test_helper'

class Pages::NavbarDecoratorTest < Draper::TestCase
  def test_navbar
    assert Pages::Navbar.new(users(:mkalita_user)).respond_to?(:decorate)
    assert_equal 'Pages::NavbarDecorator', Pages::Navbar.new(users(:mkalita_user)).decorate.class.name
    assert_raise(NotImplementedError) do
      Pages::Navbar.new(users(:mkalita_user)).decorate.class.attribute_names_visible_to_all
    end
    assert_raise(NotImplementedError) do
      Pages::Navbar.new(users(:mkalita_user)).decorate.class.attribute_names_for_collection_for_select_visible_to_all
    end
  end
end
