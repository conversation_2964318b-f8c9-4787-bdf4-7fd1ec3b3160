require 'test_helper'

class DepartmentDecoratorTest < Draper::TestCase
  fixtures :departments

  def test_departments
    assert departments(:one).respond_to?(:decorate)
    assert_equal 'DepartmentDecorator', departments(:one).decorate.class.name
    assert_instance_of Array, departments(:one).decorate.class.attribute_names_visible_to_all
    assert_instance_of Array, departments(:one).decorate.class.attribute_names_for_collection_for_select_visible_to_all
  end
end
