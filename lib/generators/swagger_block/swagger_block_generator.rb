class SwaggerBlockGenerator < Rails::Generators::NamedBase
  desc 'Creates a swagger blocks scaffold'

  source_root File.expand_path('../templates', __FILE__)

  check_class_collision suffix: 'SwaggerBlock'

  argument :attributes, type: :array, default: [], banner: 'field:type field:type'

  def create_swagger_block_file
    template 'swagger_block.rb.erb', File.join('app/controllers/api/v1/concerns/documentation', class_path, "#{plural_file_name}_endpoint.rb")
    template 'swagger_block_model.rb.erb', File.join('app/models/concerns/documentation', class_path, "#{file_name}_model.rb")
  end

  private

  def attributes_names
    [:id] + Array.wrap(attributes).reject(&:reference?).map! { |a| a.name.to_sym }
  end

  def association_names
    attributes.select(&:reference?).map! { |a| a.name.to_sym }
  end

  def columns_hash_type(name)
    Array.wrap(attributes).select { |i| i.name.to_s == name.to_s }.first.try(:type) || :array
  end

  def human_attribute_name(attribute)
    attribute.to_s.titleize
  end
end
