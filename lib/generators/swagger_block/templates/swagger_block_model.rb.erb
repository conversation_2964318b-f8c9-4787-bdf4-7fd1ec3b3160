# encoding: UTF-8
# https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#parameterObject
module Concerns
  module Documentation
    <% module_namespacing do -%>
    module <%= class_name -%>Model
      include Swagger::Blocks
      extend ActiveSupport::Concern

      included do
        swagger_schema :<%= class_name -%>Responses do
          property :<%= class_name -%>Response do
            key :'$ref', :<%= class_name -%>Response
          end
          property :<%= class_name -%>ResponseCollectionForSelect do
            key :'$ref', :<%= class_name -%>ResponseCollectionForSelect
          end
        end
        swagger_schema :<%= class_name -%>Response do
          key :required, [] + %i[id created_at updated_at url cache_ts]
          key :id, :<%= class_name -%>
          <% attributes_names.to_a.each do |attribute| -%>
          property :<%= attribute -%> do
            <% if attribute.to_s == 'password' -%>
            key :type, :password
            <% elsif columns_hash_type(attribute.to_s).to_s == 'datetime' -%>
            key :type, :'date-time'
            <% elsif columns_hash_type(attribute.to_s).to_s == 'string' -%>
            key :type, :string
            key :maximum, 255
            <% elsif columns_hash_type(attribute.to_s).to_s == 'text' -%>
            key :type, :string
            <% elsif attribute.to_s == 'id' || columns_hash_type(attribute.to_s).to_s == 'integer' -%>
            key :type, :integer
            key :format, :int32
            key :maximum, 2_147_483_647
            <% else -%>
            key :type, :<%= columns_hash_type(attribute.to_s) -%>
            <% end -%>
            <% if ['date', 'datetime'].include?(columns_hash_type(attribute.to_s)) -%>
            key :description, '<%= human_attribute_name(attribute) -%>, ISO 8601'
            <% else -%>
            key :description, '<%= human_attribute_name(attribute) -%>'
            <% end -%>
          end
          <% end -%>
          <% association_names.to_a.each do |attribute| -%>
          property :<%= attribute -%> do
            key :type, :<%= columns_hash_type(attribute.to_s) -%>
            key :description, '<%= human_attribute_name(attribute) -%>'
          end
          <% end -%>
          property :created_at do
            key :type, :string
            key :format, :'date-time'
          end
          property :updated_at do
            key :type, :string
            key :format, :'date-time'
          end
          property :url do
            key :type, :string
          end
          property :cache_ts do
            key :type, :string
            key :maximum, 255
          end
          property :actions_for_current_user do
            key :type, :object
            key :description, 'authoritative action permissions'
            (%w() + %w(index show create update destroy)).each do |action|
              property action.to_sym do
                key :type, :boolean
                key :default, false
              end
            end
          end
        end
        swagger_schema :<%= class_name -%>ResponseCollectionForSelect do
          key :required, [] + %i[id updated_at url cache_ts]
          key :id, :<%= class_name -%>
          property :id do
            key :type, :integer
            key :format, :int32
            key :maximum, 2_147_483_647
          end
          property :updated_at do
            key :type, :string
            key :format, :'date-time'
          end
          property :url do
            key :type, :string
          end
          property :cache_ts do
            key :type, :string
            key :maximum, 255
          end
        end
        swagger_schema :<%= class_name -%>Request do
          key :required, []
          property :<%= class_name.underscore -%> do
          <% attributes_names.reject { |i| [:id].include?(i) }.each do |attribute| -%>
          property :<%= attribute -%> do
            <% if attribute.to_s == 'password' -%>
            key :type, :password
            <% elsif columns_hash_type(attribute.to_s).to_s == 'date' -%>
            key :type, :string
            key :maximum, 10
            key :format, 'YYYY-MM-DD'
            key :default, Time.current.to_date.to_s
            <% elsif columns_hash_type(attribute.to_s).to_s == 'datetime' -%>
            key :type, :string
            # https://www.codementor.io/yitzofthebits/localize-time-traditional-rails-app-momentjs-twitter-clone-du107wwfg
            key :maximum, 19
            key :format, 'YYYY-MM-DD HH:MM:SS'
            key :default, Time.current.to_date.to_fs(:db)
            <% elsif columns_hash_type(attribute.to_s).to_s == 'string' -%>
            key :type, :string
            key :maximum, 255
            <% elsif columns_hash_type(attribute.to_s).to_s == 'text' -%>
            key :type, :string
            <% elsif columns_hash_type(attribute.to_s).to_s == 'integer' -%>
            key :type, :integer
            key :format, :int32
            key :maximum, 2_147_483_647
            <% else -%>
            key :type, :<%= columns_hash_type(attribute.to_s) -%>
            <% end -%>
            <% if ['date', 'datetime'].include?(columns_hash_type(attribute.to_s)) -%>
            key :description, '<%= human_attribute_name(attribute) -%>, ISO 8601'
            <% else -%>
            key :description, '<%= human_attribute_name(attribute) -%>'
            <% end -%>
          end
          <% end -%>
          <% association_names.to_a.each do |attribute| -%>
          property :<%= attribute -%> do
            key :type, :<%= columns_hash_type(attribute.to_s) -%>
            key :description, '<%= human_attribute_name(attribute) -%>'
          end
          <% end -%>
          end
        end
      <% end -%>
      end
    end
  end
end
