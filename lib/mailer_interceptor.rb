class MailerInterceptor
  class << self
    def delivering_email(message)
      return unless Settings.overwrite_mail_to

      to = [message.to, message.cc, message.bcc].compact.flatten
      login_list = to.map { |mail| mail[/[^@+]+/] }
      return unless (login_list - Settings.overwrite_mail_whitelist).any?

      modify_headers(message, to)
    end

    private

    def modify_headers(message, to)
      message.headers('X-Real-To' => to.to_json)
      message.to = Settings.overwrite_mail_list_to
      message.cc = message.bcc = []
    end
  end
end
