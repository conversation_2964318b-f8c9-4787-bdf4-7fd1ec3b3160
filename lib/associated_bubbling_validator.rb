module ActiveRecord
  module Validations
    class AssociatedBubblingValidator < ActiveModel::EachValidator
      def validate_each(record, attribute, value)
        if value.blank?
          record.errors.add(attribute, :blank)
          return
        end

        return if value.valid?

        value.errors.messages.each do |key, messages|
          messages.each do |message|
            record.errors.add("#{attribute}.#{key}", message, **options.merge(value: value))
          end
        end
      end
    end

    module ClassMethods
      def validates_associated_bubbling(*attr_names)
        validates_with AssociatedBubblingValidator, _merge_attributes(attr_names)
      end
    end
  end
end
