namespace :imperator do
  task set_global_roles: :environment do
    global_role_global_user = GlobalRole.find_or_initialize_by(name: 'Global User')
    global_role_global_user.activities = Activity.default
    global_role_global_user.save!

    global_dms_general_flow_enabled = GlobalRole.find_or_initialize_by(name: 'Global User with DMS general flow enabled')
    global_dms_general_flow_enabled.activities = Activity.general_flow
    global_dms_general_flow_enabled.save!

    global_dms_all_flows_enabled = GlobalRole.find_or_initialize_by(name: 'Global User with DMS all flows enabled')
    global_dms_all_flows_enabled.activities = Activity.all_flows
    global_dms_all_flows_enabled.save!

    global_role_global_admin_and_imperator_programmer = GlobalRole.find_or_initialize_by(name: 'Global Admin Programmer')
    global_role_global_admin_and_imperator_programmer.activities = Activity.programmer
    global_role_global_admin_and_imperator_programmer.global_admin = true
    global_role_global_admin_and_imperator_programmer.save!

    global_role_global_uber_project_manager = GlobalRole.find_or_initialize_by(name: 'Global Uber Project Manager')
    global_role_global_uber_project_manager.activities = Activity.uber_project_manager
    global_role_global_uber_project_manager.save!

    global_role_global_project_manager = GlobalRole.find_or_initialize_by(name: 'Global Project Manager')
    global_role_global_project_manager.activities = Activity.project_manager
    global_role_global_project_manager.save!

    global_role_global_hr_manager = GlobalRole.find_or_initialize_by(name: 'Global HR Manager')
    global_role_global_hr_manager.activities = Activity.hr_manager
    global_role_global_hr_manager.remote_work_periods_notifications = true
    global_role_global_hr_manager.save!

    global_role_global_accounting = GlobalRole.find_or_initialize_by(name: 'Global Accounting')
    global_role_global_accounting.activities = Activity.accounting
    global_role_global_accounting.notify_dms_controller_acceptances = true
    global_role_global_accounting.save!

    global_role_global_client = GlobalRole.find_or_initialize_by(name: 'Global Client')
    global_role_global_client.activities = Activity.client
    global_role_global_client.save!

    # helpdesk
    global_role_global_admin = GlobalRole.find_or_initialize_by(name: 'Global System Administrator')
    global_role_global_admin.activities = Activity.admin
    global_role_global_admin.global_admin = true
    global_role_global_admin.save!
    #---

    global_role_global_admin = GlobalRole.find_or_initialize_by(name: 'Global Agreements Admin')
    global_role_global_admin.activities = Activity.agreements_admin
    global_role_global_admin.save!

    global_role_global_traffic = GlobalRole.find_or_initialize_by(name: 'Global Traffic')
    global_role_global_traffic.activities = Activity.global_traffic
    global_role_global_traffic.save!

    global_asset_manager = GlobalRole.find_or_initialize_by(name: 'Global Asset Manager')
    global_asset_manager.activities = Activity.asset_manager
    global_asset_manager.save!

    global_coordinator = GlobalRole.find_or_initialize_by(name: 'Global Coordinator')
    global_coordinator.activities = Activity.coordinator
    global_coordinator.save!

    global_controlling_reader = GlobalRole.find_or_initialize_by(name: 'Global Controlling Reader')
    global_controlling_reader.activities = Activity.controlling_reader
    global_controlling_reader.save!

    global_project_coordinator = GlobalRole.find_or_initialize_by(name: 'Global Project Coordinator')
    global_project_coordinator.activities = Activity.project_coordinator
    global_project_coordinator.save!

    global_accounting_reader = GlobalRole.find_or_initialize_by(name: 'Global Accounting Reader')
    global_accounting_reader.activities = Activity.accounting_reader
    global_accounting_reader.save!

    global_hr_coordinator = GlobalRole.find_or_initialize_by(name: 'Global HR Coordinator')
    global_hr_coordinator.activities = Activity.hr_coordinator
    global_hr_coordinator.save!
  end
end
