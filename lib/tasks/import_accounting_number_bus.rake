namespace :imperator do
  task import_accounting_number_bus: :environment do
    accounting_number_bus = [
      [364, :tech],
      [517, :tech],
      [1011, :tech],
      [1037, :tech],
      [1090, :tech],
      [1131, :tech],
      [1329, :tech],
      [1349, :comm],
      [1354, :tech],
      [1377, :comm],
      [1381, :tech],
      [1387, :tech],
      [1403, :tech],
      [1556, :tech],
      [1644, :tech],
      [1852, :comm],
      [1925, :tech],
      [1941, :tech],
      [1944, :tech],
      [1974, :tech],
      [1980, :comm],
      [1989, :tech],
      [1994, :comm],
      [1995, :tech],
      [2000, :tech],
      [2001, :tech],
      [2003, :tech],
      [2017, :tech],
      [2025, :tech],
      [2048, :tech],
      [2070, :tech],
      [2085, :tech],
      [2089, :tech],
      [2098, :tech],
      [2170, :comm],
      [2193, :comm],
      [2199, :tech],
      [2215, :comm],
      [2226, :tech],
      [2228, :comm],
      [2232, :comm],
      [2242, :tech],
      [2248, :tech],
      [2267, :tech],
      [2284, :tech],
      [2285, :comm],
      [2291, :tech],
      [2298, :tech],
      [2306, :tech],
      [2314, :tech],
      [2326, :comm],
      [2339, :comm],
      [2343, :comm],
      [2353, :tech],
      [2354, :tech],
      [2360, :comm],
      [2362, :tech],
      [2366, :tech],
      [2368, :tech],
      [2376, :comm],
      [2385, :comm],
      [2386, :tech],
      [2388, :comm],
      [2397, :tech],
      [2402, :comm],
      [2407, :tech],
      [2413, :tech],
      [2414, :tech],
      [2415, :tech],
      [2416, :comm],
      [2417, :tech],
      [2422, :comm],
      [2430, :comm],
      [2435, :tech],
      [2442, :tech],
      [2448, :comm],
      [2458, :comm],
      [2459, :comm],
      [2467, :tech],
      [2474, :tech],
      [2476, :tech],
      [2479, :tech],
      [2482, :comm],
      [2484, :tech],
      [2486, :tech],
      [2487, :tech],
      [2489, :tech],
      [2490, :comm],
      [2492, :tech],
      [2498, :comm],
      [2499, :tech],
      [2500, :tech],
      [2502, :comm],
      [2503, :tech],
      [2505, :tech],
      [2506, :comm],
      [2508, :tech],
      [2509, :comm],
      [2510, :comm],
      [2511, :comm],
      [2512, :comm],
      [2513, :tech],
      [2514, :tech],
      [2515, :tech],
      [2517, :tech],
      [2521, :tech],
      [2522, :comm],
      [2523, :comm],
      [2524, :comm],
      [2525, :comm],
      [2526, :comm],
      [2527, :tech],
      [2528, :tech],
      [2529, :tech],
      [2532, :comm],
      [2533, :comm],
      [2534, :tech],
      [2535, :comm],
      [2537, :tech],
      [2540, :comm],
      [2544, :comm],
      [2547, :rb],
      [2548, :rb],
      [2550, :rb],
      [2551, :comm],
      [2552, :tech],
      [2555, :tech],
      [2556, :comm],
      [2557, :tech],
      [2558, :tech],
      [2559, :comm],
      [2560, :tech],
      [2562, :comm],
      [2563, :tech],
      [2565, :comm],
      [2566, :tech],
      [2567, :tech],
      [2568, :tech],
      [2569, :comm],
      [2570, :comm],
      [2571, :tech],
      [2572, :comm],
      [2573, :comm],
      [2574, :rb],
      [2576, :comm],
      [2577, :comm],
      [2579, :comm],
      [2580, :rb],
      [2581, :tech],
      [2582, :rb],
      [2583, :comm],
      [2586, :tech],
      [2588, :tech],
      [2589, :tech],
      [2590, :rb],
      [2592, :comm],
      [2593, :comm],
      [2594, :tech],
      [2595, :tech],
      [2596, :tech],
      [2597, :tech],
      [2598, :comm],
      [2600, :tech],
      [2601, :tech],
      [2602, :tech],
      [2604, :comm],
      [2605, :comm],
      [2606, :comm],
      [2607, :comm],
      [2608, :comm],
      [2609, :comm],
      [2613, :tech],
      [2614, :comm],
      [2615, :tech],
      [2616, :tech],
      [2617, :comm],
      [2618, :comm],
      [2619, :comm],
      [2620, :comm],
      [2622, :rb],
      [2623, :tech],
      [2626, :tech],
      [2627, :comm],
      [2628, :rb],
      [2629, :rb],
      [2630, :rb],
      [2631, :tech],
      [2632, :rb],
      [2633, :tech],
      [2634, :tech],
      [2635, :rb],
      [2636, :tech],
      [2637, :comm],
      [2638, :tech],
      [2639, :tech],
      [2640, :tech],
      [2641, :rb],
      [2642, :tech],
      [2643, :comm],
      [2644, :tech],
      [2645, :comm],
      [2646, :tech],
      [2648, :comm],
      [2649, :rb],
      [2650, :comm],
      [2651, :tech],
      [2652, :tech],
      [2653, :tech],
      [2654, :rb],
      [2655, :comm],
      [2656, :comm],
      [2658, :tech],
      [2661, :comm],
      [2662, :rb],
      [2663, :rb],
      [2665, :tech],
      [2666, :comm],
      [2667, :comm],
      [2668, :tech],
      [2669, :tech],
      [2670, :comm],
      [2671, :tech],
      [2672, :tech],
      [2673, :tech],
      [2674, :tech],
      [2675, :tech],
      [2676, :comm],
      [2677, :tech],
      [2678, :rb],
      [2679, :tech],
      [2680, :comm],
      [2681, :comm],
      [2682, :rb],
      [2683, :comm],
      [2684, :tech],
      [2685, :comm],
      [2687, :comm],
      [2689, :tech],
      [2691, :tech],
      [2699, :tech],
      [2700, :tech],
      [2701, :rb],
      [2690, :tech],
      [2698, :tech],
      [2702, :tech],
      [2694, :comm],
      [2695, :comm],
      [2697, :comm],
      [2696, :comm],
      [2692, :tech],
      [2425, :comm],
      [2147, :tech],
      [2050, :comm],
      [2686, :tech],
      [2688, :tech],
      [2693, :tech],
      [2705, :tech],
      [2706, :tech],
      [2707, :tech],
      [2553, :tech],
      [2703, :rb],
      [2710, :comm],
      [2709, :tech],
      [2725, :rb],
      [2726, :tech],
      [2723, :comm],
      [2554, :tech],
      [2708, :comm],
      [2711, :comm],
      [2704, :comm],
      [1942, :comm],
      [2031, :tech],
      [2345, :tech],
      [2429, :comm],
      [2432, :comm],
      [2471, :tech],
      [2481, :comm],
      [2518, :tech],
      [2541, :tech],
      [2542, :comm],
      [2543, :comm],
      [2546, :tech],
      [2575, :comm],
      [2578, :comm],
      [2584, :tech],
      [2585, :tech],
      [2591, :tech],
      [2603, :comm],
      [2610, :comm],
      [2612, :comm],
      [2621, :comm],
      [2625, :comm],
      [2647, :comm],
      [2713, :comm],
      [2714, :comm],
      [2715, :comm],
      [2719, :tech],
      [2720, :comm],
      [2721, :comm],
      [2722, :comm],
      [2729, :comm],
      [2731, :tech],
      [2732, :comm],
      [2733, :comm],
      [2739, :comm],
      [2724, :comm],
      [2462, :tech],
      [2664, :rb],
      [2734, :tech],
      [2735, :tech],
      [2736, :tech],
      [2745, :rb]
    ]

    accounting_number_bus.each do |number, bu|
      AccountingNumber.find_by(number: number)&.update(bu: bu)
    end
  end
end
