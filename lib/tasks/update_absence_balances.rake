# encoding: UTF-8
namespace :imperator do
  task update_absence_balances: :environment do
    raise 'Comment this out if you want to run this on production!' if Rails.env.production?
    require 'csv'
    # last_name,first_name,id,my_department,rocznie,zalegle
    filename = Rails.root.join('docs/users.csv')
    actor = User.where(last_name: '<PERSON><PERSON>').first!
    skipped_without_id = []
    puts '--------------------------------START-------------------------------'
    CSV.foreach(filename, headers: true, col_sep: ',') do |row|
      row_hash = row.to_hash
      User.transaction do
        if row_hash['id'].present? && (row_hash['rocznie'].to_s.strip.to_i > 0 || row_hash['zalegle'].to_s.strip.present?)
          model_instance = User.find(row_hash['id'])

          if model_instance.absence_balance.nil?
            raise row_hash['last_name'] + ' ' + row_hash['first_name'] + ' absence_balance is nil'
          end
          if model_instance.absence_quota.nil?
            raise row_hash['last_name'] + ' ' + row_hash['first_name'] + ' absence_quota is nil'
          end
          if model_instance.absence_quota_received_in_balance_in_years.nil?
            raise row_hash['last_name'] + ' ' + row_hash['first_name'] + ' absence_quota_received_in_balance_in_years is nil'
          end

          puts "START>>: #{row_hash['last_name']} #{row_hash['first_name']}"
          if row_hash['rocznie'].to_s.strip.to_i > 0
            if [20, 26].include?(row_hash['rocznie'].to_s.strip.to_i)
              proc_object = Proc.new do
                model_instance.absence_quota_received_in_balance_in_years[Time.zone.now.year.to_s] = row_hash['rocznie'].to_s.strip.to_i
                model_instance.absence_quota = row_hash['rocznie'].to_s.strip.to_i
                model_instance.save(validate: false)
              end
            else # odchodzi z firmy
              proc_object = Proc.new do
                decrease = model_instance.absence_quota - row_hash['rocznie'].to_s.strip.to_i
                model_instance.absence_balance = model_instance.absence_balance - decrease
                model_instance.save(validate: false)
              end
            end
            puts "Rocznie #{row_hash['rocznie']}. Zmiana rocznie: #{model_instance.previous_changes.select {|k| ['absence_quota_received_in_balance_in_years', 'absence_quota', 'absence_balance'].include?(k.to_s) }}"
          end
          model_instance.reload
          if row_hash['zalegle'].to_s.strip.present? # moze by ujemne
            proc_object = Proc.new do
              model_instance.absence_balance = model_instance.absence_balance + row_hash['zalegle'].to_s.strip.to_i
              model_instance.save(validate: false)
            end
            puts "Zalegle #{row_hash['zalegle']}. Zmiana salda: #{model_instance.previous_changes.select {|k| ['absence_quota_received_in_balance_in_years', 'absence_quota', 'absence_balance'].include?(k.to_s) }}"
          end
          puts '<<END'
        else
          unless row_hash['id'].present?
            skipped_without_id << row_hash['last_name'] + ' ' + row_hash['first_name'] + ' skipped without id'
          end
        end
      end
    end
    puts '--------------------------------FINISH------------------------------'
    puts '--------------------------------SKIPPED WITHOUT ID------------------------------'
    puts skipped_without_id.join("\n")


    # sanity check
    check_one = User.where(last_name: 'Horak').first
    puts 'Horak !!!' unless check_one.absence_balance == 6 && check_one.absence_quota_received_in_balance_in_years["2017"].to_s == '20' && check_one.absence_quota == 20

    check_one = User.where(last_name: 'Cierpiatka', first_name: 'Piotr').first
    puts 'Cierpiatka !!!' unless check_one.absence_balance == 35 && check_one.absence_quota_received_in_balance_in_years["2017"].to_s == '26' && check_one.absence_quota == 26

    check_one = User.where(last_name: 'Cudzik').first
    puts 'Cudzik !!!' unless check_one.absence_balance == 24 && check_one.absence_quota_received_in_balance_in_years["2017"].to_s == '26' && check_one.absence_quota == 26

    nil
  end
end
