# "spółka"|"nazwisko i imię"|"dział"|2017|"kierownik"|"zastęp<PERSON>"|"zatwierdza urlop kierownika"
# "artegence"|"Graca Mariusz"|"Accounts"|26|"<PERSON><PERSON><PERSON>"|"<PERSON><PERSON><PERSON>"|"<PERSON><PERSON><PERSON>"

namespace :imperator do

  desc 'initial import of HR data'
  task :initial_import_of_hr_data => ['environment', 'imperator:import_department_users', 'imperator:import_absence_balances'] do
    raise 'Comment this out if you want to run this on production!' if Rails.env.production?
  end

end
