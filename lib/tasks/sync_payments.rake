namespace :imperator do
  task sync_payments: :environment do
    logger = Logger.new(STDOUT)
    logger.level = Logger::DEBUG

    Rails.logger = logger
    ActiveRecord::Base.logger = logger

    Payment.includes(:mpk_positions, :payment_schedule).find_each do |payment|
      payment.send :recalculate_scheduled_payments
    end
    Invoice.where(state: %i[pending accepted issued])
           .includes(:mpk_positions, payment: :payment_schedule).find_each do |invoice|
      invoice.send :recalculate_payments
    end
  end
end
