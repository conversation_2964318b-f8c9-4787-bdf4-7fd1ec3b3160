<pre>
____        _                               _                                      _
|  _ \  ___ | | ___   _ _ __ ___   ___ _ __ | |_  __      _______      ___ __   ___| |_ _ __ _____ __  _   _
| | | |/ _ \| |/ / | | | '_ ` _ \ / _ \ '_ \| __| \ \ /\ / / _ \ \ /\ / / '_ \ / _ \ __| '__|_  / '_ \| | | |
| |_| | (_) |   <| |_| | | | | | |  __/ | | | |_   \ V  V /  __/\ V  V /| | | |  __/ |_| |   / /| | | | |_| |
|____/ \___/|_|\_\\__,_|_| |_| |_|\___|_| |_|\__|   \_/\_/ \___| \_/\_/ |_| |_|\___|\__|_|  /___|_| |_|\__, |
                                                                                (_(                   |___/
</pre>

h1. Zasady działania modułu HR

*Zmiany należy synchronizować z plikiem @docs/business_rules.txt@ w repozytorium projektu.*

h2. § 1. Definicje

1.1. Dni niedostępności - dotyczą tylko dni roboczych. Polskie święta, soboty i niedziele, są ignorowane.

1.2. Kategorie niedostępności

1.2.1. Niedostępność - wypoczynkowa
1.2.2. Niedostępność/Ch - zwolnienie lekarskie
1.2.3. Niedostępność/O - okolicznościowa (określona przez ustawę/rozporządzenie)
1.2.4. Niedostępność/Ż (tzw. na żadanie) (Kategoria dostępna do wyboru tylko dla: administratora, pracownika HR, szefa/zastępcy szefa jakiegokolwiek działu.)
1.2.5. Niedostępność/B - bezpłatna
1.2.6. Niedostępność/Za YYYY-MM-DD (w zamian za święto w sobotę) (Odpowiednie daty do wyboru dodawane będą corocznie przez administratora systemu. Daty do wyboru widoczne są w tym samym miesiącu co święto oraz do końca nast. miesiąca (dla logujących wstecz).)

1.3. Użytkownik uprzywilejowany - osoba posiadająca określone globalne role w systemie:

- 'Global Uber Project Manager',
- 'Global Project Manager',
- 'Global HR Manager',
- 'Global System Administrator',
- 'Global Admin Programmer';
- lub pełniąca funkcję szefa / zastępcy szefa jakiegokolwiek działu.

1.4. Pracownicy HR

- "Użytkownik uprzywilejowany" - w tym kontekście, chodzi o osobę posiadająca w systemie konto z globalną rolą 'Global HR Manager'
- "Adresaci notyfikacji" - w tym kontekście, chodzi tylko o adresy e-mail do działu HR skonfigurowane w aplikacji
- aplikacja domyślnie obsługuje jeden adres mailowy do działu HR a jeśli ten adres nie jest podany w konfiguracji (puste), obsługiwanych jest jednocześnie kilka zastępczych adresów e-mail do pracowników HR, natomiast w przypadku niedostępności domyślnego pracownika HR, wystarczy usunąć na jakiś czas jego e-mail z konfiguracji, zalecane jest jednak utworzenie skrzynki/aliasu/grupy np. <EMAIL> i podanie tego adresu jako główny w konfiguracji

1.5 Użytkownicy wewnętrzni - przypisani do jednej z firm

h2. § 2. Zasady integracji z Redmine

2.1. Dni niedostępności, o które użytkownicy wnioskują, są synchronizowane z raportami czasu do projektu "Nieświadczenie usług".

2.2. Akceptacja wniosku oznacza równoczesne automatyczne dodanie raportów czasu, w podanym zakresie dat, w projekcie "Nieświadczenie usług".

2.3. Odrzucenie wniosku oznacza równoczesne automatyczne usunięcie raportów czasu, w podanym zakresie dat, w projekcie "Nieświadczenie usług".

§ 3. Zasady wnioskowania o niedostępność

3.1. Wnioski niedostępności nie mogą zachodzić na siebie zakresem dat (wnioski odrzucone są ignorowane).

3.2. Wnioski niedostępności muszą zawierać przynajmniej jeden dzień roboczy w podanym zakresie dat.

3.3. Obliczona liczba dni we wniosku, obejmuje tylko dni robocze (w przypadku wniosków odrzuconych, licznik jest wyzerowany).

3.4. Tworzone wnioski dotyczące użytkowników oznaczonych, jako członkowie zarządu, podlegają automatycznej akceptacji.

3.4.1. Członek zarządu nie może zmieniać swojego, zaakceptowanego (automatycznie) wniosku, podobnie jak inni użytkownicy, gdyż notyfikacja do pracowników HR została już wysłana (do niego również).
3.4.2. Użytkownik uprzywilejowany (nie dotyczy 'Global Uber Project Manager', 'Global Project Manager') może zmienić lub odrzucić zaakceptowany (automatycznie) wniosek członka zarządu, mimo że notyfikacja do pracowników HR została już wysłana (do aplikanta również). Zatwierdzający przyjmuje na siebie odpowiedzialność za powiadomienie pracowników HR o zmianie.
3.4.3. Automatyczna akceptacja wniosków dla członków zarządu nie dotyczy czynności: tworzenia wniosku jednocześnie odrzuconego, edycji wniosku odrzuconego, odrzucania wniosku.

3.5. Wniosek w swoim imieniu może utworzyć każdy użytkownik. Dostępność kategorii wniosków jest uzależniona od posiadanych uprawnień (zob. punkt 3.11.3.).

3.6. Wniosek w swoim imieniu może edytować każdy użytkownik, chyba że wniosek został już zaakceptowany.

3.7. Wniosek może usunąć tylko osoba, która jest:

- administratorem;
- osobą, która była twórcą wniosku, chyba że jest ona jednocześnie aplikantem wniosku a wniosek został już zaakceptowany;
- osobą, której niedostępność jest przedmiotem wniosku, chyba że wniosek został już zaakceptowany.

3.8. Wnioski oznaczone są jednym z następujących statusów: 'Accepted', 'Rejected', 'Pending'

3.9. Jeśli wniosek o zatwierdzenie niedostępności nie został rozpatrzony przez szefa / zastępcę szefa działu przez ponad 5 dni, wnioskujący powinien skontaktować się osobiście z Działem HR, który jest odpowiedzialny za doprowadzenie sprawy do końca.

3.10. Nie zezwala się na niedostępność w zamian za przepracowane nadgodziny. Należy w takim przypadku logować czas w Redmine w sposób ciągły, najlepiej nie używać tam kategorii "Overtime".

3.11. Warunki zatwierdzenia niedostępności

3.11.1. Wszystkie osoby widzące wniosek są informowane o przesłankach do niezatwierdzenia niedostępności w konkretnym przypadku, w konkretnej chwili. Niedostępności nie są reglamentowane przez system, tylko przez jego użytkowników.
3.11.2. Wniosek o niedostępność kategorii "Niedostępność/Ż" jest dopuszczalny maksymalnie 4x w roku.
3.11.3. Wniosek o niedostępność kategorii "Niedostępność/Ż" lub "Niedostępność/Ch" tworzy dla pracownika osoba z globalną rolą:
- 'Global HR Manager',
- 'Global System Administrator',
- 'Global Admin Programmer',
- lub pełniąca funkcję szefa / zastępcy szefa jakiegokolwiek działu (uprawnienie do wniosku dla konkretnej osoby sprawdzane jest dopiero w momencie akceptacji/odrzucenia).
We wniosku, członek zarządu może dla siebie wybrać kategorię "Niedostępność/Ż" lub "Niedostępność/Ch", nawet jeśli nie posiada ww. uprawnień.
3.11.4. Wniosek o niedostępność kategorii "Niedostępność/Ż" może być edytowany lub usunięty przez osobę wymienioną w pkt. 3.11.3. Wniosek zaakceptowany nie może być edytowany czy usunięty przez samego aplikanta, chyba że jest on członkiem zarządu.
3.11.5. Wniosek o niedostępność kategorii "Niedostępność", trwający 2-4 dni, musi zostać zgłoszony z 2 tygodniowym wyprzedzeniem, o czym wszyscy są informowani (zob. pkt. 3.11.1.).
3.11.6. Wniosek o niedostępność kategorii "Niedostępność", dłuższy niż (bądź równy) 5 dni, musi zostać zgłoszony z miesięcznym wyprzedzeniem, o czym wszyscy są informowani (zob. pkt. 3.11.1.).
3.11.7. Wniosek o niedostępność kategorii "Niedostępność", dłuższy niż (bądź równy) 10 dni, musi zostać zgłoszony z 3 miesięcznym wyprzedzeniem, o czym wszyscy są informowani (zob. pkt. 3.11.1.).
3.12.8. Wnioski o niedostępność kategorii: "Niedostępność/B", nie są ewaluowane przez system, wymagają jednak zatwierdzenia przez przełożonych.
3.12.9. Wnioski o niedostępność kategorii: "Niedostępność/Ch", "Niedostępność/O", nie są ewaluowane przez system, podlegają automatycznej akceptacji, poniważ z przyczyn obiektywnych, w obu przypadkach, nie możliwości negocjacji terminu takiej niedostępności.

3.13. Edycja wniosku zaakceptowanego

3.13.1. Edycja wniosku zaakceptowanego jest dopuszczalna dla: administratora, pracownika HR, szefa lub zastępcy szefa działu aplikanta wniosku.
3.13.2. Edycja wniosku zaakceptowanego nie jest dopuszczalna dla samego aplikanta, chyba że jest on osobą pełniącą jakąkolwiek rolę wymienioną w pkt. 3.13.1 lub będącą członkiem zarządu.

3.14. Członek zarządu może zobaczyć, edytować bądź zaakceptować/odrzucić każdy wniosek (niezależnie od tego czy jest przypisany do danego działu, jako osoba akceptująca wnioski szefa), nie może natomiast tworzyć i usuwać wniosków dla innych osób, chyba że pełni adekwatną rolę w aplikacji.
3.15. Członek zarządu może usunąć wniosek, w którym sam jest aplikantem.

h2. § 4. Notyfikacje

4.1. Na różnych etapach cyklu życia wniosku niedostępności wysyłane są notyfikacje e-mail.

4.2. Użytkownicy są informowani o najnowszych notyfikacjach również po zalogowaniu do aplikacji.

4.3. Notyfikacje e-mail ani informacje o notyfikacjach nie są dostarczane do użytkowników zewnętrznych (nie przypisanych do żadnej z firm).

4.4. Notyfikacje i informacje o notyfikacjach są tworzone w sytuacji:

- utworzenia wniosku (jednorazowo),
- edycji wniosku (jednorazowo),
- akceptacji/odrzucenia wniosku (jednorazowo),
- oczekiwania wniosku niedostępności (tylko kategoria 'Niedostępność') na rozpatrzenie do 5 dni (co 2 dni, nie częściej),
- oczekiwania wniosku niedostępności (tylko kategoria 'Niedostępność') na rozpatrzenie ponad 5 dni (codziennie, nie częściej).

4.5. Notyfikacje i informacje o notyfikacjach nie są tworzone w sytuacji usunięcia wniosku.

4.6. Adresaci notyfikacji

4.6.1. W sytuacji utworzenia wniosku:
- aplikujący,
- szef działu i zastępca szefa działu (dot. każdego działu, w przypadku przypisania użytkownika do wielu działów),
- rozpatrujący wniosek (jeśli już określony),
- pracownicy HR (jeśli wniosek jednocześnie został zaakceptowany i należy do jednej z kategorii: Niedostępność/B', 'Niedostępność/Ch),
- użytkownicy mający następujące role w projektach (także poprzez grupę), których aplikant jest członkiem (jeśli wniosek jednocześnie został zaakceptowany): 'Project Manager', 'Uber Project Manager'.
4.6.2. W sytuacji utworzenia wniosku dla członka zarządu:
- pracownicy HR, jeśli wniosek należy do jednej z kategorii: Niedostępność/B', 'Niedostępność/Ch,
- inni członkowie zarządu.
4.6.3. W sytuacji edycji wniosku, tak samo jak przy tworzeniu (zob. § 4. pkt. 4.6.1)
4.6.4. W sytuacji akceptacji/odrzucenia wniosku, tak samo jak przy tworzeniu (zob. § 4. pkt. 4.6.1)
4.6.5. W sytuacji oczekiwania wniosku na rozpatrzenie do 5 dni:
- aplikujący,
- szef działu i zastępca szefa działu (dot. każdego działu, w przypadku przypisania użytkownika do wielu działów),
- rozpatrujący wniosek (jeśli już określony).
4.6.6. W sytuacji oczekiwania wniosku na rozpatrzenie ponad 5 dni:
- tak samo jak w pkt. 4.6.5.,
- pracownicy HR, niezależnie od kategorii wniosku.

4.7. Notyfikacje e-mail do pracowników HR wysyłane są na adresy skonfigurowane w ustawieniach aplikacji. Ze względu na dużą ilość notyfikacji, jakie otrzymują pracownicy HR, przechowywane są jedynie informacje o notyfikacjach (widoczne po zalogowaniu do aplikacji) dotyczące ich samych w roli aplikanta lub rozpatrującego konkretny wniosek.

4.8. Wyjątki

4.8.1. Każdy, kto sam tworzy lub edytuje lub rozpatrza lub potwierdza wniosek nie otrzymuje z tego powodu notyfikacji, przy czym nie dotyczy to:
- notyfikacji dla aplikanta, który zawsze otrzymuje mailowe potwierdzenie
- notyfikacji związanych z wnioskiem dla członka zarządu
- notyfikacji dla menedżerów projektów
- notyfikacji mailowych wysyłanych na skrzynkę pocztową Pracowników HR (<EMAIL>)

h2. § 5. Zakres widoczności wniosków niedostępności

5.1. Menu

- 'My holidays' - widoczne dla wszystkich użytkowników wewmnętrznych (przypisanych do firmy), własne wnioski niedostępności
- 'Team calendar' - widoczne dla wszystkich użytkowników wewmnętrznych (przypisanych do firmy)
- 'Requests' - widoczne dla użytkowników z globalnymi rolami: 'Global HR Manager', 'Global System Administrator', 'Global Admin Programmer' lub pełniących funkcję szefa / zastępcy szefa jakiegokolwiek działu albo będących członkami zarządu.

5.2. Wnioski, które widzą użytkownicy:

- administratorzy, użytkownicy wewnętrzni: pracownicy HR, członkowie zarządu - wszystkie,
- szefowie / zastępcy szefów działów - swoje, pracowników działów, do których należą lub im podlegających,
- Uber/Project Manager - swoje lub wszystkie z datą zakończenia w tym miesiącu i przyszłości

5.3. Wybór działu do wyszukania dla użytkowników

- administratorzy, użytkownicy wewnętrzni: pracownicy HR, Uber/Project Manager, członkowie zarządu - wszystkie działy
- szefowie / zastępcy szefów działów (nie jednoznaczne z byciem członkiem działu) - działy im podlegające oraz te, do których należą
- osoba zatwierdzająca wnioski szefa działu (uber szef) - członek zarządu widzi wszystkie działy, w innym przypadku, na podstawie swoich uprawnień i/lub przynależności do działu jako członek działu (zob. powyżej)

5.4. Wybór użytkownika do wyszukania dla użytkowników

- administratorzy, użytkownicy wewnętrzni: pracownicy HR, Uber/Project Manager, członkowie zarządu - wszyscy użytkownicy
- szefowie / zastępcy szefów działów (nie jednoznaczne z byciem członkiem działu) - on sam lub członkowie działu mu podlegającego lub członkowie działu, do którego on sam należy
- osoba zatwierdzająca wnioski szefa działu (uber szef) - na podstawie swoich uprawnień i członkostw (zob. powyżej)

5.5. Statystyki

5.5.1. Zakres widoczności
- swoje widzą wszyscy
- innych, poszczególnych użytkowników, widzą tylko użytkownicy uprzywilejowani (określeni w § 1.4)
5.5.2. Definicje
- 'absence count this year' - suma zaakceptowanych dni niedostępności w bieżącym roku (obejmuje tylko kategorie: 'Niedostępność', 'Niedostępność/Ż')
- 'absence quota' - dostępne dni niedostępności (20 (domyślnie) lub 26) (dotyczy tylko kategorii: 'Niedostępność', 'Niedostępność/Ż')
- 'absence balance' - saldo dni niedostępności dostępnych w danym momencie (dotyczy tylko kategorii: 'Niedostępność', 'Niedostępność/Ż') (zob. par. 9.)

h2. § 6. Rozpatrywanie wniosków

6.1. Wnioski niedostępności mogą akceptować lub odrzucać jedynie osoby, które są:

- administratorami,
- pracownikami HR,
- szefem/zastępcą szefa działu, do którego należy użytkownik,
- osoba zatwierdzająca wnioski szefa działu (uber szef działu) - musi być członkiem zarządu lub użytkownikiem z jedną z globalnych ról: 'Global HR Manager', 'Global System Administrator', 'Global Admin Programmer'.

6.2. System przechowuje informację o osobie rozpatrującej wniosek.

6.3. System umożliwia dodawanie publicznych komentarzy we wniosku, dotyczących odpowiednio wniosku aplikanta i decyzji rozpatrującego. W szczególnych przypadkach system dodaje komentarz, np. podczas automatycznej akceptacji wniosku chorobowego lub dla członka zarządu.

6.4. Wniosek podczas tworzenia przechodzi podstawową walidację poprawności, zob. § 3. pkt. 3.1-2.

6.5. Zasadność wniosku niedostępności powinna być oceniana na podstawie informacji widocznych na karcie wniosku po jego utworzeniu, istotny jest bowiem stan aktualny ustalany przez system w momencie zatwierdzania wniosku.

h2. § 7. Zarządzanie niedostępnościami użytkowników

7.1. Przy tworzeniu użytkownika pracownik HR lub administrator zapisuje mu 'absence quota' - dostępne dni niedostępności (20 (domyślnie) lub 26) w bieżącym roku. Zmiany w tym polu powodują automatyczne zmiany 'absence_balance' po zapisaniu rekordu, chyba że została jednocześnie zmieniona przez użytkownika wartość w polu 'absence balance', wtedy zostanie przyjęta wartość podana przez użytkownika. Jak pracownik zaczyna pracę w trakcie roku, to automatycznie przydzielana jest mu pula niedostępnościowa w wymiarze: x/12 * liczba miesięcy do końca roku, gdzie x to liczba dni niedostępności, które mu przysługują (20 (domyślnie) lub 26).

7.2. Przy edycji użytkownika pracownik HR lub administrator w szczególnych przypadkach zmienia mu 'absence balance' - saldo dni niedostępności do wykorzystania w danym momencie (wartość może być ujemna, np. jeśli niedostępność jest wykorzystywana na przełomie lat). Suma jest zwiększana automatycznie przez systemowy proces na początku każdego roku (operacja odnotowana w bazie danych), o wartość 'absence quota'. Z tytułu "nowego roku" lub podczas edycji użytkownika bez skonfigurowanego 'absence quota', nie wolno samodzielnie dodawać do 'absence balance' wartości 'absence quota', gdyż system sam to zrobi następnego dnia rano, po dokonaniu edycji. Pole 'absence balance' służy jedynie do dokonywania ewentualnych korekt.

7.3. Saldo dni niedostępności zawiera wartość dostępną do końca bieżącego końca roku. W przypadku umów na czas określony, zwolnień, wartość powinna być skorygowana ręcznie przez pracownika HR i/lub brana pod uwagę przy zatwierdzania niedostępności w sposób relatywny do terminu wygaśnięcia umowy.

7.4. "Zaległe" dni niedostępności są składnikiem salda dni niedostępności w kolejnych latach kalendarzowych.

7.5. "Przepadające" niedostępności - saldo dni niedostępności wymaga ręcznej korekty przez pracownika HR, tj. pomniejszenia o liczbę dni wolnych niewykorzystanych w ciągu 3 lat od czasu uzyskania uprawnienia do nich.

h2. § 8. Zarządzanie działami firmy

8.1. Podczas tworzenia/edycji kont pracownicy są przypisywani do działów.

8.2. Dział ma skonfigurowane osoby: szefa, zastępcę szefa i osobę akceptującą wniosek szefa (uber szef), która z racji tego obowiązku powinna być jednocześnie członkiem zarządu lub użytkownikiem z jedną z globalnych ról: 'Global HR Manager', 'Global System Administrator', 'Global Admin Programmer'.

8.3. Szef / zastępca szefa i osoba akceptującą wniosek szefa muszą posiadać konto w systemie.

8.4. Wniosek zastępcy szefa akceptuje szef działu, do którego zastępca należy, jako członek. Użytkownik może być zastępcą w innym dziale niż ten, do którego należy, to nie oznacza, że szef tamtego działu powinien zatwierdzać jego wniosek.

8.5. Zakładka menu 'Departments' jest widoczna tylko dla użytkowników wewnętrznych, którzy mają uprawnienie do edytowania działów:
- 'Global System Administrator',
- 'Global Admin Programmer',
- 'Global HR Manager'.

h2. § 9. Saldo dni niedostępności

9.1. Dotyczy wyłącznie kategorii 'Niedostępność' lub 'Niedostępność/Ż'

9.2. W przypadku akceptacji wniosku jest ono pomniejszane o liczbę dni roboczych obliczonych we wniosku

9.3. W przypadku odrzucenia lub usunięcia wniosku jest powiększane o odjętą poprzednio liczbę obliczonych dni roboczych - w przypadku wniosków zaakceptowanych, w przypadku wniosków oczekujących lub odrzuconych - brak zmian.

9.4. W przypadku zmiany zakresu dat:
- wniosku zaakceptowanego - poprzednia odjęta liczba dni zostanie dodana, a aktualna liczba dni odjęta
- wniosku oczekującego lub odrzuconego - brak zmian

h2. § 10. Wnioski chorobowe

10.1. Pracownik powinien poinformować - mailem lub telefonicznie lub SMS-em - przełożonego lub zastępcę lub pracownika HR, który na jego prośbę, bezzwłocznie, utworzy wniosek na planowany okres. Aplikant otrzyma odpowiednią notyfikację.
10.2. Za datę wystawienia zwolnienia, umownie, przyjmuje się dzień utworzenia wniosku. Od tej daty, w 7. dniu lub następującym po nim dniu roboczym (jeśli to dzień wolny od pracy), wymagane jest dostarczenie zwolnienia do przełożonego lub zastępcy lub pracownika HR.
10.3. W przypadku niedostarczenia dokumentu potwierdzającego chorobę w terminie określonym powyżej, wniosek zostanie automatycznie zmieniony przez system na zwykły wniosek niedostępności. Aplikant, składający w jego imieniu wniosek (lub ostatni akceptujący), pracownicy HR, otrzymają odpowiednią notyfikację.
10.4. Wymagane jest odnotowanie we wniosku, dotyczącym niedostępności z powodu choroby, przez osobę mającą uprawnienia analogiczne jak do akceptacji wniosku, że wniosek został uznany za uzasadniony (checkbox `confirmed`). Zmiana ta spowoduje wysłanie notyfikacji do aplikanta i szefów działu (lub Działu HR jeśli wniosek dotyczy członka zarządu).
10.5. Egzekwowanie dostarczenia dokumentu potwierdzającego chorobę lub sposobu jego dostarczenia jest ściśle związane z rodzajem umowy zawartej z pracodawcą.
10.6. Dla uproszczenia procedur, aplikacja może przyjmować pewne założenia, np. że data wystawienia zwolnienia jest taka sama jak data złożenia wniosku.
10.7. Dla pracownika wiążące są terminy określone przez ustawodawcę i odpowiednie rozporządzenia.
10.8. Wniosek kategorii 'Niedostępność' może być konwertowany na wniosek kategorii 'Niedostępność/Ch' przez administratora lub Pracownika HR w celu odwrócenia działania opisanego w punkcie 10.3. Konwersja powinna być wykorzystywana tylko w tym celu, gdyż powoduje wysłanie notyfikacji jedynie do aplikanta (z założenia dotyczy tylko przeszłych lub aktualnych nieobecności). Konwersja powoduje przywrócenie aplikantowi - odjętych uprzednio z salda - dostępnych dni niedostępności.

h2. § 11. Wnioski okolicznościowe

11.1. Od daty zakończenia podanej we wniosku, w 7. dniu lub następującym po nim dniu roboczym (jeśli to dzień wolny od pracy), wymagane jest dostarczenie dokumentu usprawiedliwiającego do przełożonego lub zastępcy lub pracownika HR (np. kopia odpisu aktu stanu cywilnego - urodzenia, małżeństwa, zgonu).
11.2. W przypadku niedostarczenia dokumentu potwierdzającego zasadność wniosku w terminie określonym powyżej, wniosek zostanie automatycznie zmieniony przez system na zwykły wniosek niedostępności. Aplikant, składający w jego imieniu wniosek (lub ostatni akceptujący), pracownicy HR, otrzymają odpowiednią notyfikację.
11.3. Wymagane jest odnotowanie we wniosku, dotyczącym niedostępności z powodu 'okoliczności', przez osobę mającą uprawnienia analogiczne jak do akceptacji wniosku, że wniosek został uznany za uzasadniony (checkbox `confirmed`). Zmiana ta spowoduje wysłanie notyfikacji do aplikanta i szefów działu (lub Działu HR jeśli wniosek dotyczy członka zarządu).
11.4. Egzekwowanie dostarczenia dokumentu potwierdzającego 'okoliczność' lub sposobu jego dostarczenia jest ściśle związane z rodzajem umowy zawartej z pracodawcą.
11.5. Dla uproszczenia procedur, aplikacja może przyjmować pewne założenia, np. że możliwe jest dostarczenie dokumentu potwierdzającego w ciągu 7 dni.
11.6. Dla pracownika wiążące są terminy określone przez ustawodawcę i odpowiednie rozporządzenia.
11.7. Wniosek kategorii 'Niedostępność' może być konwertowany na wniosek kategorii 'Niedostępność/O' przez administratora lub Pracownika HR w celu odwrócenia działania opisanego w punkcie 11.2. Konwersja powinna być wykorzystywana tylko w tym celu, gdyż powoduje wysłanie notyfikacji jedynie do aplikanta (z założenia dotyczy tylko przeszłych lub aktualnych nieobecności). Konwersja powoduje przywrócenie aplikantowi - odjętych uprzednio z salda - dostępnych dni niedostępności.
