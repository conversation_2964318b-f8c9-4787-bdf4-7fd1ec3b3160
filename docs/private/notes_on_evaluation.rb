=begin
# https://cbabhusal.wordpress.com/2015/01/30/passing-modifiers-rails-generate-migration/
# http://railsguides.net/advanced-rails-model-generators/

SCAFFOLD_COMMAND='g' \
&& MODEL_NAME='Evaluation' \
&& CONTROLLER_UNDERSCORE=`echo "${MODEL_NAME}" | sed -r 's/([a-z0-9])([A-Z])/\1_\L\2/g'` \
&& CONTROLLER_NAME="${CONTROLLER_UNDERSCORE,,}s" \
&& SCAFFOLD_COLUMNS="user_id:integer iteration_every:integer on_hold:boolean communicativeness_avg:integer diligence_avg:integer promptness_avg:integer commitment_avg:integer independence_avg:integer" \
&& bin/rails $SCAFFOLD_COMMAND model $MODEL_NAME $SCAFFOLD_COLUMNS \
&& bin/rails $SCAFFOLD_COMMAND swagger_block $MODEL_NAME $SCAFFOLD_COLUMNS \
&& bin/rails $SCAFFOLD_COMMAND decorator $MODEL_NAME \
&& bin/rails $SCAFFOLD_COMMAND pundit:policy $MODEL_NAME \
&& bin/rails $SCAFFOLD_COMMAND custom_responders_controller "api/v1/${CONTROLLER_NAME}" $SCAFFOLD_COLUMNS --model-name=$MODEL_NAME --skip-decorator

EvaluationIteration
evaluation_id:integer number:integer completed:boolean summary:text communicativeness_avg:integer diligence_avg:integer promptness_avg:integer commitment_avg:integer independence_avg:integer

SCAFFOLD_COMMAND='g' \
&& MODEL_NAME='EvaluationIteration' \
&& CONTROLLER_UNDERSCORE=`echo "${MODEL_NAME}" | sed -r 's/([a-z0-9])([A-Z])/\1_\L\2/g'` \
&& CONTROLLER_NAME="${CONTROLLER_UNDERSCORE,,}s" \
&& SCAFFOLD_COLUMNS="evaluation_id:integer deadline_on:date number:integer completed:boolean summary:text communicativeness_avg:integer diligence_avg:integer promptness_avg:integer commitment_avg:integer independence_avg:integer" \
&& bin/rails $SCAFFOLD_COMMAND model $MODEL_NAME $SCAFFOLD_COLUMNS \
&& bin/rails $SCAFFOLD_COMMAND swagger_block $MODEL_NAME $SCAFFOLD_COLUMNS \
&& bin/rails $SCAFFOLD_COMMAND decorator $MODEL_NAME \
&& bin/rails $SCAFFOLD_COMMAND pundit:policy $MODEL_NAME \
&& bin/rails $SCAFFOLD_COMMAND custom_responders_controller "api/v1/${CONTROLLER_NAME}" $SCAFFOLD_COLUMNS --model-name=$MODEL_NAME --skip-decorator

EvaluationFeedback
respondent_id:integer evaluation_iteration_id:integer communicativeness:integer diligence:integer promptness:integer commitment:integer independence:integer strengths:text weaknesses:text comments:text

SCAFFOLD_COMMAND='g' \
&& MODEL_NAME='EvaluationAnswer' \
&& CONTROLLER_UNDERSCORE=`echo "${MODEL_NAME}" | sed -r 's/([a-z0-9])([A-Z])/\1_\L\2/g'` \
&& CONTROLLER_NAME="${CONTROLLER_UNDERSCORE,,}s" \
&& SCAFFOLD_COLUMNS="respondent_id:integer evaluation_iteration_id:integer communicativeness:integer diligence:integer promptness:integer commitment:integer independence:integer strengths:text weaknesses:text comments:text" \
&& bin/rails $SCAFFOLD_COMMAND model $MODEL_NAME $SCAFFOLD_COLUMNS \
&& bin/rails $SCAFFOLD_COMMAND swagger_block $MODEL_NAME $SCAFFOLD_COLUMNS \
&& bin/rails $SCAFFOLD_COMMAND decorator $MODEL_NAME \
&& bin/rails $SCAFFOLD_COMMAND pundit:policy $MODEL_NAME \
&& bin/rails $SCAFFOLD_COMMAND custom_responders_controller "api/v1/${CONTROLLER_NAME}" $SCAFFOLD_COLUMNS --model-name=$MODEL_NAME --skip-decorator

- szef dzialu widzi swoich, czł. zarządu widzi wszystkie
- maile maja link dla konkretnej evaluacji
- ewaluacje nie sa edytowalne, to zwykle formularze i da sie je postowac tylko raz dla konkretnej evaluacji (daj confirm przed zapisem), nie da sie zobaczyc i listowac swoich
- szef pisze podsumowujacy komentarz i oznacza iteracje jako zakonczona i wtedy nie przyjmowane sa formularze do tej ewaluacji
- przechodzi sie z listy userow
- na tydzień przed nastepna iteracja szef dostaje maila z prośbą o stworzenie następnego cyklu evaluacji (nic więcej, samo się nie dzieje)
- maile do ludzi idą w momencie tworzenia iteracji przez szefa (daś info, żeby nie dawali daty deadline zbyt długiej...)
- daty iteracji nie sa istotne (mozna je edytowac) poza tym ze nie powinno sie tworzyc wczesniejszych niz juz istniejace
- bez zarzadu i samych szefow
- analizujacy widzi tylko komentarz szefa i srednie ocen, moze dalej przejsc do poszczegolnych wynikow ankiet ale nie musi
=end
