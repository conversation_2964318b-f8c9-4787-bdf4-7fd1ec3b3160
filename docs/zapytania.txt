Do jakich projektow user nalezy jako PM:
ap Membership.of_user_and_groups(User.find(2108)).includes(:project).order('projects.name').select {|i| i.roles.map(&:name).grep(/Project/i).any? }.map{|i| [i.roles.first.name, i.project.name]}

pm = User.active.where(id: 2108).first!
q = User.active.where(username: 'm<PERSON><PERSON><PERSON>').first!
ap Membership.of_user_and_groups(pm).includes(:project).order('projects.name').where(projects: {status: 0}).select {|i| i.roles.to_a.map(&:name).grep(/Project/i).any? && q.involved_in_projects.ids.include?(i.project_id) }.map{|i| [i.roles.to_a.map(&:name).grep(/Project/i).first, i.project.name]}
