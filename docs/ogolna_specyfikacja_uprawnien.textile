h1. Ogólna specyfikacja uprawnień

<pre>
ap GlobalRoleDecorator.all_with_permission_kits_form
</pre>

{
              "Global Accounting" => [
        [0] "[ + ] ADD_SUBPROJECTS",
        [1] "[ + ] CLOSE_PROJECT",
        [2] "[ + ] CREATE_AND_VIEW_PROJECT",
        [3] "[ + ] EDIT_PROJECT",
        [4] "[ + ] EDIT_PROJECT_MEMBERSHIPS",
        [5] "[ + ] LIST_PROJECTS",
        [6] "[ + ] LIST_PROJECT_MEMBERSHIPS",
        [7] "[ - ] EDIT_USER",
        [8] "[ - ] UPDATE_GLOBAL_ROLE_ACTIVITIES",
        [9] "[ - ] UPDATE_GROUP_USERS"
    ],
        "Global Admin Programmer" => [
        [0] "[ + ] ADD_SUBPROJECTS",
        [1] "[ + ] CLOSE_PROJECT",
        [2] "[ + ] CREATE_AND_VIEW_PROJECT",
        [3] "[ + ] EDIT_PROJECT",
        [4] "[ + ] EDIT_PROJECT_MEMBERSHIPS",
        [5] "[ + ] EDIT_USER",
        [6] "[ + ] LIST_PROJECTS",
        [7] "[ + ] LIST_PROJECT_MEMBERSHIPS",
        [8] "[ + ] UPDATE_GLOBAL_ROLE_ACTIVITIES",
        [9] "[ + ] UPDATE_GROUP_USERS"
    ],
                 "Global Analyst" => [
        [0] "[ + ] LIST_PROJECT_MEMBERSHIPS",
        [1] "[ - ] ADD_SUBPROJECTS",
        [2] "[ - ] CLOSE_PROJECT",
        [3] "[ - ] CREATE_AND_VIEW_PROJECT",
        [4] "[ - ] EDIT_PROJECT",
        [5] "[ - ] EDIT_PROJECT_MEMBERSHIPS",
        [6] "[ - ] EDIT_USER",
        [7] "[ - ] LIST_PROJECTS",
        [8] "[ - ] UPDATE_GLOBAL_ROLE_ACTIVITIES",
        [9] "[ - ] UPDATE_GROUP_USERS"
    ],
                  "Global Client" => [
        [0] "[ - ] ADD_SUBPROJECTS",
        [1] "[ - ] CLOSE_PROJECT",
        [2] "[ - ] CREATE_AND_VIEW_PROJECT",
        [3] "[ - ] EDIT_PROJECT",
        [4] "[ - ] EDIT_PROJECT_MEMBERSHIPS",
        [5] "[ - ] EDIT_USER",
        [6] "[ - ] LIST_PROJECTS",
        [7] "[ - ] LIST_PROJECT_MEMBERSHIPS",
        [8] "[ - ] UPDATE_GLOBAL_ROLE_ACTIVITIES",
        [9] "[ - ] UPDATE_GROUP_USERS"
    ],
              "Global HR Manager" => [
        [0] "[ + ] EDIT_USER",
        [1] "[ + ] LIST_PROJECTS",
        [2] "[ + ] LIST_PROJECT_MEMBERSHIPS",
        [3] "[ + ] UPDATE_GROUP_USERS",
        [4] "[ - ] ADD_SUBPROJECTS",
        [5] "[ - ] CLOSE_PROJECT",
        [6] "[ - ] CREATE_AND_VIEW_PROJECT",
        [7] "[ - ] EDIT_PROJECT",
        [8] "[ - ] EDIT_PROJECT_MEMBERSHIPS",
        [9] "[ - ] UPDATE_GLOBAL_ROLE_ACTIVITIES"
    ],
         "Global Project Manager" => [
        [0] "[ + ] CLOSE_PROJECT",
        [1] "[ + ] EDIT_PROJECT",
        [2] "[ + ] EDIT_PROJECT_MEMBERSHIPS",
        [3] "[ + ] LIST_PROJECT_MEMBERSHIPS",
        [4] "[ - ] ADD_SUBPROJECTS",
        [5] "[ - ] CREATE_AND_VIEW_PROJECT",
        [6] "[ - ] EDIT_USER",
        [7] "[ - ] LIST_PROJECTS",
        [8] "[ - ] UPDATE_GLOBAL_ROLE_ACTIVITIES",
        [9] "[ - ] UPDATE_GROUP_USERS"
    ],
    "Global System Administrator" => [
        [0] "[ + ] ADD_SUBPROJECTS",
        [1] "[ + ] CLOSE_PROJECT",
        [2] "[ + ] CREATE_AND_VIEW_PROJECT",
        [3] "[ + ] EDIT_PROJECT",
        [4] "[ + ] EDIT_PROJECT_MEMBERSHIPS",
        [5] "[ + ] EDIT_USER",
        [6] "[ + ] LIST_PROJECTS",
        [7] "[ + ] LIST_PROJECT_MEMBERSHIPS",
        [8] "[ + ] UPDATE_GROUP_USERS",
        [9] "[ - ] UPDATE_GLOBAL_ROLE_ACTIVITIES"
    ],
    "Global Uber Project Manager" => [
        [0] "[ + ] ADD_SUBPROJECTS",
        [1] "[ + ] CLOSE_PROJECT",
        [2] "[ + ] CREATE_AND_VIEW_PROJECT",
        [3] "[ + ] EDIT_PROJECT",
        [4] "[ + ] EDIT_PROJECT_MEMBERSHIPS",
        [5] "[ + ] LIST_PROJECTS",
        [6] "[ + ] LIST_PROJECT_MEMBERSHIPS",
        [7] "[ - ] EDIT_USER",
        [8] "[ - ] UPDATE_GLOBAL_ROLE_ACTIVITIES",
        [9] "[ - ] UPDATE_GROUP_USERS"
    ],
                    "Global User" => [
        [0] "[ + ] LIST_PROJECT_MEMBERSHIPS",
        [1] "[ - ] ADD_SUBPROJECTS",
        [2] "[ - ] CLOSE_PROJECT",
        [3] "[ - ] CREATE_AND_VIEW_PROJECT",
        [4] "[ - ] EDIT_PROJECT",
        [5] "[ - ] EDIT_PROJECT_MEMBERSHIPS",
        [6] "[ - ] EDIT_USER",
        [7] "[ - ] LIST_PROJECTS",
        [8] "[ - ] UPDATE_GLOBAL_ROLE_ACTIVITIES",
        [9] "[ - ] UPDATE_GROUP_USERS"
    ]
}

CHANGELOG:

dodanie departments:create/update dla HR Manager

dodane departments:index dla wszystkich (#150028)

dodane users:index (nie dotyczy menu), tym samym LIST_PROJECT_MEMBERSHIPS, dla Global Project Manager

roles:create, roles:update zdjęte z Global Uber Project Manager

users:create zdjęte z Global Uber Project Manager

UPDATE_GROUP_USERS zdjęte z Global Uber Project Manager

UPDATE_GROUP_USERS zdjęte z Global Project Manager
CLOSE_PROJECT zdjęte z Global Analyst (w redmine było, sprawa dyskusyjna)

W sierpniu przez pomyłkę HR Manager dostał ADD_SUBPROJECTS i CLOSE_PROJECT, Marek to poprawił (wycofał), ale pierwotna moja poprawka powinna dodać EDIT_USER i UPDATE_GROUP_USERS do specyfikacji, co teraz czynię (zadanie zgłoszone ustnie przez Krzyśka - #142555).
